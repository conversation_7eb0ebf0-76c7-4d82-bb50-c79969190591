<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tauri集成示例 - Sprite Editor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }

        .button:hover {
            background: #0056b3;
        }

        .button.success {
            background: #28a745;
        }

        .button.warning {
            background: #ffc107;
            color: #212529;
        }

        #sprite-editor {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 5px;
        }

        .log {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-info {
            color: #007bff;
        }

        .log-success {
            color: #28a745;
        }

        .log-error {
            color: #dc3545;
        }

        .log-warning {
            color: #ffc107;
        }

        .input-group {
            margin: 10px 0;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }

        .api-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }

        .api-section h3 {
            margin-top: 0;
            color: #333;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🎯 Tauri集成示例 - Sprite Editor</h1>
        <p>这个页面演示了如何在Tauri应用中集成Sprite Editor，并通过iframe进行通信。</p>

        <div class="api-section">
            <h3>📋 可用的API方法</h3>
            <ul>
                <li><code>window.SpriteEditor.setExternalResourcePathForAccess(path)</code> - 设置外部资源路径</li>
                <li><code>window.SpriteEditor.setExternalSpriteForEdit(elementsArray)</code> - 设置要编辑的sprite数据</li>
                <li><code>window.SpriteEditor.loadExternalImageFile(filePath)</code> - 🆕 从外部加载图片文件</li>
                <li><code>window.SpriteEditor.loadExternalImageBuffer(filePath, arrayBuffer)</code> - 🆕
                    从外部加载图片ArrayBuffer</li>
                <li><code>window.SpriteEditor.saveCurrentSprite()</code> - 保存当前sprite</li>
                <li><code>window.handleFileSelectDialog(callback)</code> - 🆕 外部文件选择方法（Tauri提供）</li>
            </ul>
            <p><strong>🎯
                    新功能：</strong>现在<code>handleFileSelectDialog</code>的回调函数支持两个参数：<code>callback(filePath, arrayBuffer)</code>
            </p>
            <p><strong>✨ ImgEditor优化：</strong>现在完全基于ArrayBuffer，不再使用URL，所有图片显示都通过ArrayBuffer转换</p>
            <p><strong>🎯 TextSettings优化：</strong>当字体选项加载完成且bitmap属性设置完成后，自动停止定时器循环</p>
            <p><strong>🎯 舞台尺寸修复：</strong>现在严格使用传入的canvasWidth=816, canvasHeight=624，不再动态计算</p>
            <p><strong>🚫 黑屏修复：</strong>Canvas初始化时不再出现黑屏闪烁，等sprite加载完成后再显示</p>
        </div>

        <div class="controls">
            <h3>🛠️ 测试控制</h3>

            <div class="input-group">
                <label for="resource-path">外部资源路径:</label>
                <input type="text" id="resource-path" value="../projects/Project4/"
                    placeholder="例如: ../projects/Project4/">
            </div>

            <div class="input-group">
                <label for="image-path">图片文件路径:</label>
                <input type="text" id="image-path" value="../projects/Project4/img/faces/Actor1.png"
                    placeholder="例如: ../projects/Project4/img/faces/Actor1.png">
            </div>

            <button class="button" onclick="setResourcePath()">1. 设置资源路径</button>
            <button class="button success" onclick="loadExternalImage()">2. 🆕 加载外部图片</button>
            <button class="button" onclick="sendTestData()">3. 发送测试数据</button>
            <button class="button" onclick="testExternalFontSetting()">🔤 测试外部字体设置</button>
            <button class="button warning" onclick="saveSprite()">4. 保存Sprite</button>
            <button class="button" onclick="toggleFileSelectMethod()">🔄 切换文件选择方法</button>
            <button class="button" onclick="clearLog()">清空日志</button>
        </div>

        <iframe id="sprite-editor" src="http://localhost:3000" title="Sprite Editor">
        </iframe>

        <div class="log" id="log">
            <div class="log-entry log-info">等待Sprite Editor加载...</div>
        </div>
    </div>

    <script>
        // 模拟外部文件选择方法的状态
        let useExternalFileSelect = true;

        // 模拟Tauri提供的文件选择方法
        function mockHandleFileSelectDialog(callback) {
            addLog('🎭 模拟Tauri文件选择对话框', 'info');

            // 模拟用户选择文件的过程
            const mockFiles = [
                'img/faces/Actor1.png',
                'img/faces/Actor2.png',
                'img/characters/Actor1.png',
                'img/system/Window.png',
                'img/tilesets/Outside_A2.png'
            ];

            const selectedFile = prompt(
                '模拟文件选择（在真实Tauri环境中这将是原生对话框）:\n\n可选文件:\n' +
                mockFiles.map((f, i) => `${i + 1}. ${f}`).join('\n') +
                '\n\n请输入文件编号(1-5)或直接输入文件路径:'
            );

            if (selectedFile) {
                const fileIndex = parseInt(selectedFile) - 1;
                let finalPath;

                if (fileIndex >= 0 && fileIndex < mockFiles.length) {
                    finalPath = mockFiles[fileIndex];
                } else {
                    finalPath = selectedFile;
                }

                addLog(`📁 模拟选择文件: ${finalPath}`, 'success');

                // 🆕 模拟生成ArrayBuffer数据
                const shouldGenerateArrayBuffer = confirm('是否模拟生成ArrayBuffer数据？\n\n点击"确定"模拟ArrayBuffer传递\n点击"取消"只传递路径');

                if (shouldGenerateArrayBuffer) {
                    // 创建一个模拟的ArrayBuffer（实际应用中这将是真实的图片数据）
                    const mockImageData = new Uint8Array([
                        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG header
                        0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
                        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 pixel
                        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53,
                        0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41,
                        0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
                        0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
                        0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44,
                        0xAE, 0x42, 0x60, 0x82
                    ]);

                    addLog(`🔢 生成模拟ArrayBuffer，大小: ${mockImageData.byteLength} 字节`, 'info');
                    callback(finalPath, mockImageData.buffer);
                } else {
                    addLog('📄 只传递路径，不传递ArrayBuffer', 'info');
                    callback(finalPath);
                }
            } else {
                addLog('❌ 用户取消了文件选择', 'warning');
                callback(null);
            }
        }

        // 日志功能
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 切换文件选择方法
        function toggleFileSelectMethod() {
            useExternalFileSelect = !useExternalFileSelect;

            if (useExternalFileSelect) {
                // 绑定模拟的外部文件选择方法
                window.handleFileSelectDialog = mockHandleFileSelectDialog;
                addLog('✅ 已启用外部文件选择方法（模拟Tauri）', 'success');
            } else {
                // 移除外部文件选择方法
                delete window.handleFileSelectDialog;
                addLog('🔄 已禁用外部文件选择方法，将使用浏览器默认方法', 'info');
            }
        }

        // 初始化时启用外部文件选择方法
        toggleFileSelectMethod();

        // 监听来自Sprite Editor的保存事件
        document.addEventListener('spriteEditorSave', function (event) {
            addLog('🎉 收到保存事件', 'success');
            addLog(`📊 数据统计: ${event.detail.elementCount} 个元素`, 'info');
            addLog(`🎨 Sprite状态: (${event.detail.spriteState.x}, ${event.detail.spriteState.y})`, 'info');
            addLog(`📏 画布尺寸: ${event.detail.canvasSize.width}x${event.detail.canvasSize.height}`, 'info');

            // 显示保存的数据（截断显示）
            const dataStr = JSON.stringify(event.detail.elements, null, 2);
            const truncatedData = dataStr.length > 200 ? dataStr.substring(0, 200) + '...' : dataStr;
            addLog(`💾 保存的数据: ${truncatedData}`, 'info');
        });

        // 设置资源路径
        function setResourcePath() {
            const path = document.getElementById('resource-path').value;
            if (!path) {
                addLog('❌ 请输入资源路径', 'error');
                return;
            }

            const iframe = document.getElementById('sprite-editor');
            if (iframe.contentWindow.SpriteEditor) {
                iframe.contentWindow.SpriteEditor.setExternalResourcePathForAccess(path);
                addLog(`✅ 资源路径已设置: ${path}`, 'success');
            } else {
                addLog('❌ SpriteEditor对象不可用', 'error');
            }
        }

        // 🆕 加载外部图片
        function loadExternalImage() {
            const imagePath = document.getElementById('image-path').value;
            if (!imagePath) {
                addLog('❌ 请输入图片文件路径', 'error');
                return;
            }

            const iframe = document.getElementById('sprite-editor');
            if (iframe.contentWindow.SpriteEditor) {
                addLog(`🖼️ 开始加载外部图片: ${imagePath}`, 'info');
                iframe.contentWindow.SpriteEditor.loadExternalImageFile(imagePath);
                addLog('✅ 已调用loadExternalImageFile方法', 'success');
            } else {
                addLog('❌ SpriteEditor对象不可用', 'error');
            }
        }

        // 发送测试数据
        function sendTestData() {
            const iframe = document.getElementById('sprite-editor');

            if (!iframe.contentWindow.SpriteEditor) {
                addLog('❌ SpriteEditor对象不可用', 'error');
                return;
            }

            const testData = [
                {
                    type: 'image',
                    source: {
                        _url: '../projects/Project4/img/faces/Actor1.png',
                        width: 144,
                        height: 223
                    },
                    sx: 0, sy: 7, sw: 144, sh: 129,
                    dx: 5, dy: 3, dw: 144, dh: 129,
                    bounds: { x: 5, y: 3, width: 144, height: 129 }
                },
                {
                    type: 'text',
                    text: 'Tauri集成测试',
                    x: 184, y: 13,
                    maxWidth: 168, lineHeight: 36,
                    align: 'left',
                    bounds: { x: 184, y: 13, width: 168, height: 36 }
                }
            ];

            // 添加sprite信息
            testData.spriteInfo = {
                x: 0, y: 0,
                anchorX: 0, anchorY: 0,
                scaleX: 1, scaleY: 1,
                rotation: 0,
                canvasWidth: 400, canvasHeight: 200,
                fontBold: false,
                fontFace: "Arial",
                fontItalic: false,
                fontSize: 18,
                outlineColor: "rgba(0, 0, 0, 0.8)",
                outlineWidth: 2,
                textColor: "#ff0000"
            };

            // 🎯 测试全局字体
            const globalFonts = [
                'Microsoft YaHei',
                'SimHei',
                'KaiTi',
                'FangSong',
                'Comic Sans MS',
                'Impact',
                'Trebuchet MS'
            ];

            addLog('📤 发送测试数据到Sprite Editor（包含全局字体）', 'info');
            iframe.contentWindow.SpriteEditor.setExternalSpriteForEdit(testData, globalFonts);
            addLog('✅ 测试数据发送成功（包含7个全局字体）', 'success');
        }

        // 🎯 专门测试外部字体设置方式
        function testExternalFontSetting() {
            const iframe = document.getElementById('sprite-editor');

            if (!iframe.contentWindow.SpriteEditor) {
                addLog('❌ SpriteEditor对象不可用', 'error');
                return;
            }

            // 模拟外部应用的字体设置方式
            const externalFonts = ["rmmz-mainfont", "Microsoft Yahei", "PingFang SC", "sans-serif"];

            addLog('🔤 测试外部字体设置方式', 'info');
            addLog('📝 模拟外部代码: iframeWindow.SpriteEditor.setGlobalFonts = ["rmmz-mainfont", "Microsoft Yahei", "PingFang SC", "sans-serif"]', 'info');

            // 直接属性赋值（模拟外部应用的调用方式）
            iframe.contentWindow.SpriteEditor.setGlobalFonts = externalFonts;

            addLog('✅ 已设置setGlobalFonts属性，字体应该在TextSettings中显示', 'success');
            addLog('🔍 请检查TextSettings组件的字体下拉列表是否更新', 'info');
        }

        // 保存Sprite
        function saveSprite() {
            const iframe = document.getElementById('sprite-editor');

            if (!iframe.contentWindow.SpriteEditor) {
                addLog('❌ SpriteEditor对象不可用', 'error');
                return;
            }

            try {
                const result = iframe.contentWindow.SpriteEditor.saveCurrentSprite();
                if (result) {
                    addLog('✅ 手动保存成功', 'success');
                } else {
                    addLog('⚠️ 保存返回空结果', 'warning');
                }
            } catch (error) {
                addLog('❌ 保存失败: ' + error.message, 'error');
            }
        }

        // 等待iframe加载完成
        document.getElementById('sprite-editor').addEventListener('load', function () {
            addLog('🚀 Sprite Editor iframe已加载', 'success');

            setTimeout(() => {
                const iframe = document.getElementById('sprite-editor');
                if (iframe.contentWindow.SpriteEditor) {
                    addLog('✅ SpriteEditor对象已可用', 'success');
                    addLog('🎯 可以开始测试Tauri集成功能了！', 'info');

                    // 自动设置默认资源路径
                    const defaultPath = document.getElementById('resource-path').value;
                    if (defaultPath) {
                        iframe.contentWindow.SpriteEditor.setExternalResourcePathForAccess(defaultPath);
                        addLog(`🔧 已自动设置默认资源路径: ${defaultPath}`, 'info');
                    }
                } else {
                    addLog('⚠️ SpriteEditor对象尚未可用，请稍后再试', 'warning');
                }
            }, 2000);
        });
    </script>
</body>

</html>