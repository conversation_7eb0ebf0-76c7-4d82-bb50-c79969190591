<script lang="ts">
  import type { TextElement } from '../../type/bitmap.svelte';
  import LabelInput from '../../components/LabelInput.svelte';
  import { bitmapModel, updateElementProperty } from '../stores/bitmapStore';
  import DataBrowser from '../../dataManage/DataBrowser.svelte';

  // 🔧 Svelte 5: Props
  interface Props {
    element?: TextElement | null;
  }

  let { element }: Props = $props();

  // 🔧 处理属性变化，使用 store 的辅助函数
  function handleChange(key: string, value: any) {
    if (!element || !$bitmapModel) return;

    // 找到当前元素在 elements 数组中的索引
    const elementIndex = $bitmapModel.elements.findIndex(el => el === element);
    if (elementIndex === -1) return;

    // 使用 store 的辅助函数更新元素
    updateElementProperty(elementIndex, { [key]: value });

    console.log(`更新文本元素属性 ${key}:`, value, '索引:', elementIndex);
  }

  // 处理数据选择
  function handleDataSelect(bindingExpression: string, value: any) {
    // 直接设置绑定表达式作为文本内容
    handleChange('text', bindingExpression);
    console.log('设置数据绑定:', bindingExpression, '预览值:', value);
  }

  // 表达式解析方法（与插件中的逻辑保持一致）
  function evaluateExpression(expression: string): string {
    try {
      const parts = expression.split('.');
      if (parts.length === 0) return '';

      const dataType = parts[0];
      const globalVarName = '$data' + dataType.charAt(0).toUpperCase() + dataType.slice(1);

      let value = (window as any)[globalVarName];
      if (!value) return '';

      for (let i = 1; i < parts.length; i++) {
        const part = parts[i];
        if (value === null || value === undefined) return '';

        if (/^\d+$/.test(part)) {
          if (Array.isArray(value)) {
            const index = parseInt(part);
            if (index >= 0 && index < value.length) {
              value = value[index];
            } else {
              return '';
            }
          } else {
            return '';
          }
        } else {
          if (typeof value === 'object' && value !== null && part in value) {
            value = value[part];
          } else {
            return '';
          }
        }
      }

      if (value === null || value === undefined || value === '') {
        return '';
      }

      return String(value);
    } catch (error) {
      return '';
    }
  }

  // 获取显示在输入框中的文本（解析后的实际值）
  const displayText = $derived.by(() => {
    if (!element?.text) return '';

    // 如果是绑定表达式，解析并返回实际值
    if (element.text.startsWith('{{') && element.text.endsWith('}}')) {
      const expression = element.text.slice(2, -2);
      const evaluatedValue = evaluateExpression(expression);
      return evaluatedValue || element.text; // 如果解析失败，显示原始表达式
    }

    // 普通文本直接返回
    return element.text;
  });
</script>

<div class="text-element-panel">
  {#if element}
    <!-- 文本内容 -->
    <div class="property-section">
      <h4>文本内容</h4>

      <!-- 手动文本输入 -->
      <div class="property-group vertical">
        <label for="textContent">文本内容:</label>
        <textarea
          id="textContent"
          value={displayText}
          oninput={(e) => handleChange('text', (e.target as HTMLTextAreaElement).value)}
          rows="3"
          placeholder="请输入文本内容，或使用下方数据浏览器选择数据绑定"
        ></textarea>

        <!-- 如果是绑定表达式，显示绑定信息 -->
        {#if element.text && element.text.startsWith('{{') && element.text.endsWith('}}')}
          <div class="binding-info">
            <span class="binding-label">数据绑定:</span>
            <span class="binding-expression">{element.text}</span>
            <button
              class="clear-binding"
              onclick={() => handleChange('text', '')}
              title="清除数据绑定"
            >
              ✕
            </button>
          </div>
        {/if}
      </div>

      <!-- 数据浏览器 -->
      <div class="property-group vertical">
        <label>数据绑定选择:</label>
        <DataBrowser onSelect={handleDataSelect} />
      </div>
    </div>

    <!-- 位置属性 -->
    <div class="property-section">
      <h4>位置属性</h4>
      <div class="property-row">
        <div class="property-group">
          <label>X坐标:</label>
          <LabelInput
            value={element.x?.toString() || '0'}
            type="number"
            size="sm"
            onChange={(newValue) => handleChange('x', parseInt(newValue))}
          />
        </div>
        <div class="property-group">
          <label>Y坐标:</label>
          <LabelInput
            value={element.y?.toString() || '0'}
            type="number"
            size="sm"
            onChange={(newValue) => handleChange('y', parseInt(newValue))}
          />
        </div>
      </div>
    </div>

    <!-- 文本格式 -->
    <div class="property-section">
      <h4>文本格式</h4>
      <div class="property-row">
        <div class="property-group">
          <label>宽度:</label>
          <LabelInput
            value={element.maxWidth?.toString() || ''}
            type="number"
            size="sm"
            min={1}
            onChange={(newValue) => handleChange('maxWidth', parseInt(newValue))}
          />
        </div>
        <div class="property-group">
          <label>行高:</label>
          <LabelInput
            value={element.lineHeight?.toString() || ''}
            type="number"
            size="sm"
            min={1}
            onChange={(newValue) => handleChange('lineHeight', parseInt(newValue))}
          />
        </div>
      </div>

      <div class="property-group">
        <label>对齐:</label>
        <select
          value={element.align}
          onchange={(e) => handleChange('align', (e.target as HTMLSelectElement).value)}
          class="select-input"
        >
          <option value="left">左对齐</option>
          <option value="center">居中</option>
          <option value="right">右对齐</option>
        </select>
      </div>
    </div>
  {/if}
</div>

<style>
  .text-element-panel {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 100%;
  }

  .property-section {
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .property-section h4 {
    margin: 0;
    padding: 12px 16px;
    background: var(--theme-surface-light, #f9fafb);
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    font-size: 13px;
    font-weight: 600;
    color: var(--theme-text, #111827);
    letter-spacing: 0.025em;
  }

  .property-section > .property-group,
  .property-section > .property-row {
    padding: 12px 16px;
  }

  .property-section > .property-group:not(:last-child),
  .property-section > .property-row:not(:last-child) {
    border-bottom: 1px solid var(--theme-border-light, #f3f4f6);
  }

  .property-group {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }

  .property-group.vertical {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .property-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .property-group label {
    font-size: 12px;
    font-weight: 500;
    color: var(--theme-text-secondary, #6b7280);
    white-space: nowrap;
    flex-shrink: 0;
    min-width: fit-content;
    margin-bottom: 2px;
  }

  .select-input {
    padding: 8px 12px;
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 4px;
    font-size: 13px;
    background: var(--theme-surface, #ffffff);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    min-width: 100px;
  }

  .select-input:focus {
    outline: none;
    border-color: var(--theme-primary, #3b82f6);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* 文本区域样式 */
  textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 4px;
    font-size: 13px;
    background: var(--theme-surface, #ffffff);
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
    line-height: 1.4;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  textarea:focus {
    outline: none;
    border-color: var(--theme-primary, #3b82f6);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* 绑定信息样式 */
  .binding-info {
    margin-top: 10px;
    padding: 10px 12px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #bae6fd;
    border-radius: 6px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  .binding-label {
    color: #0369a1;
    font-weight: 600;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .binding-expression {
    color: #1e293b;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    background: rgba(255, 255, 255, 0.8);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid rgba(59, 130, 246, 0.2);
    flex: 1;
    font-size: 11px;
    backdrop-filter: blur(10px);
  }

  .clear-binding {
    background: #ef4444;
    color: rgb(196, 176, 176);
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  }

  .clear-binding:hover {
    background: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  }

  .clear-binding:active {
    transform: translateY(0);
  }

  /* 响应式优化 */
  @media (max-width: 768px) {
    .property-row {
      grid-template-columns: 1fr;
      gap: 8px;
    }

    .property-section > .property-group,
    .property-section > .property-row {
      padding: 10px 12px;
    }

    .binding-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .binding-expression {
      width: 100%;
    }
  }

  /* 改进的焦点状态 */
  textarea:focus,
  .select-input:focus {
    border-color: var(--theme-primary, #3b82f6);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* 禁用状态 */
  textarea:disabled,
  .select-input:disabled {
    background-color: var(--theme-surface-disabled, #f3f4f6);
    color: var(--theme-text-disabled, #9ca3af);
    cursor: not-allowed;
  }

  /* 滚动条样式 */
  textarea::-webkit-scrollbar {
    width: 8px;
  }

  textarea::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  textarea::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
    border-radius: 4px;
    border: 1px solid rgba(148, 163, 184, 0.3);
    transition: all 0.2s ease-in-out;
  }

  textarea::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
    border-color: rgba(100, 116, 139, 0.5);
    transform: scaleY(1.1);
  }

  textarea::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  }

  /* 滚动条角落 */
  textarea::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* 面板滚动条样式 */
  .text-element-panel::-webkit-scrollbar {
    width: 6px;
  }

  .text-element-panel::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  .text-element-panel::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    border-radius: 3px;
    transition: all 0.2s ease-in-out;
  }

  .text-element-panel::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  }

  /* 通用滚动条样式 - 适用于所有可滚动元素 */
  * {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 transparent;
  }
</style>
