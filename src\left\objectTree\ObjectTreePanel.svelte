<script lang="ts">
  import type { NodeActionEvent, ContextMenuData } from './types';
  import { globalObjectState, setSelectedObject } from '../../stores/objectState';
  import { 
    objectTreeState, 
    hierarchicalTreeData, 
    selectedNode, 
    treeStats 
  } from './objectTreeStore';
  import { ObjectTreeActions } from './objectTreeActions';
  import { ObjectTreeSync } from './objectTreeSync';
  import { getObjectTypeName } from './typeUtils';
  import ObjectTreeNode from './ObjectTreeNode.svelte';

  // 组件内部状态
  let filterText = $state('');
  let debouncedFilterText = $state('');
  let showStats = $state(false);
  let filterTimeout: number | null = null;

  // 右键菜单状态
  let contextMenuData = $state<ContextMenuData>({
    x: 0,
    y: 0,
    visible: false,
    targetNode: null
  });

  // 从新的状态管理系统获取数据
  let treeData = $derived($hierarchicalTreeData);
  let currentSelectedNode = $derived($selectedNode);
  let currentTreeStats = $derived($treeStats);

  // 防抖处理过滤文本
  $effect(() => {
    if (filterTimeout) {
      clearTimeout(filterTimeout);
    }

    filterTimeout = setTimeout(() => {
      debouncedFilterText = filterText;
    }, 300) as unknown as number; // 300ms 防抖延迟
  });

  // 计算过滤后的树数据（使用防抖后的文本）
  let filteredTreeData = $derived(() => {
    if (!debouncedFilterText.trim()) {
      return treeData;
    }
    console.log('执行过滤操作，搜索文本:', debouncedFilterText);
    return filterTreeData(treeData, debouncedFilterText);
  });

  // 过滤缓存
  let filterCache = new Map<string, any[]>();

  // 过滤树数据的函数（带缓存优化）
  function filterTreeData(nodes: any[], searchText: string): any[] {
    const cacheKey = `${searchText}_${nodes.length}_${treeData[0]?.id || 'empty'}`;

    // 检查缓存
    if (filterCache.has(cacheKey)) {
      console.log('使用过滤缓存');
      return filterCache.get(cacheKey)!;
    }

    const lowerSearchText = searchText.toLowerCase();

    function filterNode(node: any): any | null {
      const matchesSearch = node.displayName.toLowerCase().includes(lowerSearchText) ||
        (node.objectType && node.objectType.toLowerCase().includes(lowerSearchText));

      const filteredChildren = node.children
        .map((child: any) => filterNode(child))
        .filter((child: any) => child !== null);

      if (matchesSearch || filteredChildren.length > 0) {
        return {
          ...node,
          children: filteredChildren,
          // 如果有匹配的子节点，自动展开父节点
          expanded: filteredChildren.length > 0 ? true : node.expanded
        };
      }

      return null;
    }

    const result = nodes
      .map(node => filterNode(node))
      .filter(node => node !== null);

    // 缓存结果（限制缓存大小）
    if (filterCache.size > 10) {
      const keys = Array.from(filterCache.keys());
      if (keys.length > 0) {
        filterCache.delete(keys[0]);
      }
    }
    filterCache.set(cacheKey, result);

    console.log('过滤完成，结果数量:', result.length);
    return result;
  }

  // 缓存根对象引用，避免不必要的重新同步
  let lastRootObject: any = null;
  let lastRootObjectType: string | null = null;
  let lastUpdateTime: number = 0;

  // 监听根对象变化，使用新的同步机制（监听根对象和时间戳）
  $effect(() => {
    const rootObject = $globalObjectState.rootObject;
    const rootObjectType = $globalObjectState.rootObjectType;
    const currentUpdateTime = $globalObjectState.lastUpdateTime || 0;

    // 只有根对象真正变化或时间戳变化时才执行同步
    if (rootObject !== lastRootObject || rootObjectType !== lastRootObjectType || currentUpdateTime !== lastUpdateTime) {
      console.log('🔍 === ObjectTreePanel 监听到根对象变化 ===');
      console.log('🔍 根对象:', rootObject);
      console.log('🔍 根对象类型:', rootObjectType);
      console.log('🔍 上次根对象:', lastRootObject);
      console.log('🔍 上次根对象类型:', lastRootObjectType);
      console.log('🔍 根对象子对象数量:', rootObject?.children?.length || 0);
      console.log('🔍 当前更新时间戳:', currentUpdateTime);
      console.log('🔍 上次更新时间戳:', lastUpdateTime);

      // 更新缓存
      lastRootObject = rootObject;
      lastRootObjectType = rootObjectType;
      lastUpdateTime = currentUpdateTime;

      objectTreeState.update(state => {
        if (rootObject && rootObjectType) {
          // 使用新的同步机制
          return ObjectTreeSync.syncRootObject(state, rootObject, rootObjectType);
        } else {
          // 清空对象树
          return ObjectTreeActions.clear(state);
        }
      });
    }
  });

  // 处理节点操作
  function handleNodeAction(event: NodeActionEvent) {
    console.log('节点操作:', event);

    switch (event.type) {
      case 'select':
        handleSelectNode(event.nodeId);
        break;
      case 'expand':
      case 'collapse':
        handleToggleNodeExpanded(event.nodeId);
        break;
      default:
        console.warn('未知的节点操作类型:', event.type);
    }
  }

  // 选择节点
  function handleSelectNode(nodeId: string) {
    console.log('=== 选择节点 ===');
    console.log('节点ID:', nodeId);

    // 更新对象树状态
    objectTreeState.update(state => ObjectTreeActions.selectNode(state, nodeId));

    // 更新全局状态
    if (currentSelectedNode) {
      let actualObject = currentSelectedNode.currentObject;

      // 如果是包装对象，提取 displayObject
      if (actualObject && typeof actualObject === 'object' && actualObject.displayObject) {
        console.log('检测到包装对象，提取 displayObject');
        actualObject = actualObject.displayObject;
      }

      const objectType = currentSelectedNode.objectType || getObjectTypeName(actualObject);
      setSelectedObject([actualObject], [objectType], nodeId);

      console.log('选中节点:', {
        displayName: currentSelectedNode.displayName,
        isRPGMakerType: currentSelectedNode.isRPGMakerType,
        depth: currentSelectedNode.depth,
        objectType: objectType,
        originalObject: currentSelectedNode.currentObject,
        actualObject: actualObject
      });
    }
  }

  // 切换节点展开状态
  function handleToggleNodeExpanded(nodeId: string) {
    console.log('=== 切换节点展开状态 ===');
    console.log('节点ID:', nodeId);

    objectTreeState.update(state => ObjectTreeActions.toggleExpanded(state, nodeId));
  }

  // 展开所有节点
  function handleExpandAll() {
    objectTreeState.update(state => ObjectTreeActions.expandAll(state));
    console.log('所有节点已展开');
  }

  // 收起所有节点
  function handleCollapseAll() {
    objectTreeState.update(state => ObjectTreeActions.collapseAll(state));
    console.log('所有节点已收起');
  }

  // 清空对象树
  function handleClearTree() {
    if (confirm('确定要清空对象树吗？这将删除所有已创建的对象。')) {
      // 清空根对象
      globalObjectState.update(state => {
        state.rootObject = null;
        state.rootObjectType = null;
        return state;
      });

      // 清空选中对象
      setSelectedObject([], []);

      console.log('对象树已清空');
    }
  }

  // 切换统计信息显示
  function toggleStats() {
    showStats = !showStats;
  }

  // 清空过滤文本
  function clearFilter() {
    filterText = '';
  }

  // 处理右键菜单更新
  function handleContextMenuUpdate(newContextMenuData: ContextMenuData) {
    contextMenuData = newContextMenuData;
    console.log('右键菜单状态更新:', contextMenuData);
  }
</script>

<div class="object-tree-panel">
  <!-- 头部工具栏 -->
  <div class="toolbar">
    <h3 class="panel-title">对象树</h3>
    
    <div class="toolbar-actions">
      <button 
        class="toolbar-button" 
        onclick={handleExpandAll}
        title="展开所有节点"
        disabled={treeData.length === 0}
      >
        📂
      </button>

      <button
        class="toolbar-button"
        onclick={handleCollapseAll}
        title="收起所有节点"
        disabled={treeData.length === 0}
      >
        📁
      </button>

      <button
        class="toolbar-button"
        onclick={toggleStats}
        title="显示/隐藏统计信息"
        class:active={showStats}
      >
        📊
      </button>

      <button
        class="toolbar-button danger"
        onclick={handleClearTree}
        title="清空对象树"
        disabled={treeData.length === 0}
      >
        🗑️
      </button>
    </div>
  </div>

  <!-- 过滤器 -->
  <div class="filter-container">
    <div class="filter-input-wrapper">
      <input 
        type="text" 
        class="filter-input"
        placeholder="搜索对象..."
        bind:value={filterText}
      />
      {#if filterText}
        <button 
          class="clear-filter-button"
          onclick={clearFilter}
          title="清空搜索"
        >
          ✕
        </button>
      {/if}
    </div>
  </div>

  <!-- 统计信息 -->
  {#if showStats}
    <div class="stats-container">
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">总节点:</span>
          <span class="stat-value">{currentTreeStats.totalNodes}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">RPG Maker:</span>
          <span class="stat-value rpg-maker">{currentTreeStats.rpgMakerNodes}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">自定义:</span>
          <span class="stat-value custom">{currentTreeStats.customNodes}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">根节点:</span>
          <span class="stat-value">{currentTreeStats.rootNodes}</span>
        </div>
      </div>
    </div>
  {/if}

  <!-- 对象树内容 -->
  <div class="tree-content">
    {#if filteredTreeData().length === 0}
      <div class="empty-state">
        {#if treeData.length === 0}
          <div class="empty-icon">🌳</div>
          <p class="empty-message">暂无对象</p>
          <p class="empty-hint">在类继承图中点击节点来创建对象</p>
        {:else}
          <div class="empty-icon">🔍</div>
          <p class="empty-message">未找到匹配的对象</p>
          <p class="empty-hint">尝试修改搜索条件</p>
        {/if}
      </div>
    {:else}
      <div class="tree-nodes">
        {#each filteredTreeData() as node (node.id)}
          <ObjectTreeNode
            {node}
            isSelected={currentSelectedNode?.id === node.id}
            onNodeAction={handleNodeAction}
            {contextMenuData}
            onContextMenuUpdate={handleContextMenuUpdate}
          />
        {/each}
      </div>
    {/if}
  </div>

  <!-- 底部信息 -->
  {#if filteredTreeData().length > 0}
    <div class="footer-info">
      {#if filterText}
        <span class="filter-result">
          找到 {currentTreeStats.totalNodes} 个对象
        </span>
      {:else}
        <span class="total-count">
          共 {currentTreeStats.totalNodes} 个对象
        </span>
      {/if}
    </div>
  {/if}
</div>

<style>
  .object-tree-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-background);
    border-radius: 8px;
    overflow: hidden;
  }

  .toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: var(--theme-surface);
    border-bottom: 1px solid var(--theme-border);
    flex-shrink: 0;
  }

  .panel-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--theme-text);
  }

  .toolbar-actions {
    display: flex;
    gap: 4px;
  }

  .toolbar-button {
    background: none;
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    padding: 6px 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .toolbar-button:hover:not(:disabled) {
    background-color: var(--theme-surface-hover);
    border-color: var(--theme-primary);
  }

  .toolbar-button.active {
    background-color: var(--theme-primary-light);
    border-color: var(--theme-primary);
    color: var(--theme-primary);
  }

  .toolbar-button.danger:hover:not(:disabled) {
    background-color: rgba(244, 67, 54, 0.1);
    border-color: #f44336;
    color: #f44336;
  }

  .toolbar-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .filter-container {
    padding: 8px 16px;
    background: var(--theme-surface);
    border-bottom: 1px solid var(--theme-border);
    flex-shrink: 0;
  }

  .filter-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }

  .filter-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    font-size: 13px;
    background: var(--theme-background);
    color: var(--theme-text);
  }

  .filter-input:focus {
    outline: none;
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
  }

  .clear-filter-button {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    color: var(--theme-text-secondary);
    font-size: 12px;
    border-radius: 2px;
  }

  .clear-filter-button:hover {
    background-color: var(--theme-surface-hover);
    color: var(--theme-text);
  }

  .stats-container {
    padding: 8px 16px;
    background: var(--theme-surface);
    border-bottom: 1px solid var(--theme-border);
    flex-shrink: 0;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
  }

  .stat-label {
    color: var(--theme-text-secondary);
  }

  .stat-value {
    font-weight: 600;
    color: var(--theme-text);
  }

  .stat-value.rpg-maker {
    color: #1976D2;
  }

  .stat-value.custom {
    color: #388E3C;
  }

  .tree-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
  }

  .tree-nodes {
    padding: 0 8px;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    text-align: center;
    color: var(--theme-text-secondary);
  }

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-message {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px 0;
  }

  .empty-hint {
    font-size: 13px;
    margin: 0;
    opacity: 0.7;
  }

  .footer-info {
    padding: 8px 16px;
    background: var(--theme-surface);
    border-top: 1px solid var(--theme-border);
    font-size: 12px;
    color: var(--theme-text-secondary);
    text-align: center;
    flex-shrink: 0;
  }

  .filter-result {
    color: var(--theme-primary);
    font-weight: 500;
  }
</style>
