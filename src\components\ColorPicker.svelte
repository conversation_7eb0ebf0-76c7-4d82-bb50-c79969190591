<script lang="ts">
  export let value: string = '#ffffff';
  export let label: string = '';
  export let disabled: boolean = false;
  export let onChange: (color: string) => void = () => {};

  // 处理颜色变化
  function handleColorChange(event: Event) {
    const target = event.target as HTMLInputElement;
    value = target.value;
    onChange(value);
  }

  // 处理文本输入变化
  function handleTextChange(event: Event) {
    const target = event.target as HTMLInputElement;
    let newValue = target.value;

    // 确保以#开头
    if (newValue && !newValue.startsWith('#')) {
      newValue = '#' + newValue;
    }

    // 验证颜色格式
    if (/^#[0-9A-Fa-f]{6}$/.test(newValue) || /^#[0-9A-Fa-f]{3}$/.test(newValue)) {
      value = newValue;
      onChange(value);
    }
  }


</script>

<div class="color-picker">
  {#if label}
    <label class="color-label">{label}</label>
  {/if}

  <!-- 颜色选择器 -->
  <input
    type="color"
    bind:value
    on:input={handleColorChange}
    {disabled}
    class="color-input"
    title="选择颜色"
  />

  <!-- 文本输入框 -->
  <input
    type="text"
    bind:value
    on:input={handleTextChange}
    {disabled}
    class="color-text"
    placeholder="#ffffff"
    maxlength="7"
  />
</div>

<style>
  .color-picker {
    display: flex;
    align-items: center;
    gap: var(--spacing-1, 0.25rem);
  }

  .color-label {
    font-size: var(--font-size-xs, 0.75rem);
    font-weight: 500;
    color: var(--theme-text, #111827);
    min-width: fit-content;
  }

  .color-input {
    width: 24px;
    height: 24px;
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 3px;
    cursor: pointer;
    background: none;
    padding: 0;
  }

  .color-input:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .color-text {
    width: 50px;
    padding: 2px 4px;
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 3px;
    font-size: var(--font-size-xs, 0.75rem);
    font-family: monospace;
    background: var(--theme-surface, #ffffff);
  }

  .color-text:focus {
    outline: none;
    border-color: var(--theme-primary, #3b82f6);
    box-shadow: 0 0 0 3px var(--theme-primary-light, #dbeafe);
  }

  .color-text:disabled {
    background: var(--theme-surface-light, #f9fafb);
    color: var(--theme-text-secondary, #6b7280);
    cursor: not-allowed;
  }
</style>
