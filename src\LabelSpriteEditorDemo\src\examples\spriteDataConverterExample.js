/**
 * Sprite数据转换工具使用示例
 * 展示如何在elements数组数据和sprite对象之间进行转换
 */

import { elementsToSprite } from '../utils/spriteDataConverter';

/**
 * 测试数据 - 使用您提供的3个元素
 */
export function createTestElementsData() {
  return [
    // 图片元素 - Actor1.png
    {
      type: 'image',
      source: {
        _url: 'src/assets/04.png',
        width: 144,
        height: 223
      },
      sx: 0,
      sy: 7,
      sw: 144,
      sh: 129,
      dx: 5,
      dy: 3,
      dw: 144,
      dh: 129,
      bounds: { x: 5, y: 3, width: 144, height: 129 }
    },
    // 文本元素 - "里德"
    {
      type: 'text',
      text: '里德',
      x: 184,
      y: 13,
      maxWidth: 168,
      lineHeight: 36,
      align: 'left',
      bounds: { x: 184, y: 13, width: 168, height: 36 }
    },
    // 文本元素 - "等级"
    {
      type: 'text',
      text: '等级',
      x: 184,
      y: 49,
      maxWidth: 48,
      lineHeight: 36,
      align: 'left',
      bounds: { x: 184, y: 49, width: 48, height: 36 }
    }
  ];
}

/**
 * 测试转换功能
 */
export async function testSpriteConversion() {
  console.log('🚀 开始测试sprite转换功能...');

  const testElements = createTestElementsData();
  console.log('测试数据:', testElements);

  try {
    const sprite = await elementsToSprite(testElements, {
      canvasWidth: 400,
      canvasHeight: 200,
      spriteX: 0,
      spriteY: 0,
      anchorX: 0,
      anchorY: 0,
      scaleX: 1,
      scaleY: 1,
      rotation: 0
    });

    console.log('✅ 转换成功!');
    console.log('Sprite对象:', sprite);
    console.log('Bitmap尺寸:', sprite.bitmap.width, 'x', sprite.bitmap.height);
    console.log('Elements数量:', sprite.bitmap.elements.length);
    console.log('Elements详情:', sprite.bitmap.elements);

    return sprite;

  } catch (error) {
    console.error('❌ 转换失败:', error);
    throw error;
  }
}

/**
 * 在浏览器中测试
 */
export function runBrowserTest() {
  console.log('🔧 在浏览器中运行测试...');

  // 检查是否在浏览器环境
  if (typeof window === 'undefined') {
    console.error('❌ 不在浏览器环境中');
    return;
  }

  // 检查SpriteEditor是否可用
  if (!window.SpriteEditor || !window.SpriteEditor.setExternalSpriteForEdit) {
    console.error('❌ SpriteEditor不可用');
    return;
  }

  const testElements = createTestElementsData();
  console.log('发送测试数据到SpriteEditor:', testElements);

  try {
    window.SpriteEditor.setExternalSpriteForEdit(testElements);
    console.log('✅ 数据已发送到SpriteEditor');

    // 等待一段时间后尝试保存
    setTimeout(() => {
      try {
        const result = window.SpriteEditor.saveCurrentSprite();
        console.log('保存结果:', result);
      } catch (error) {
        console.error('保存失败:', error);
      }
    }, 3000);

  } catch (error) {
    console.error('❌ 发送数据失败:', error);
  }
}

// 如果在浏览器环境中，将测试函数添加到全局对象
if (typeof window !== 'undefined') {
  window.SpriteConverterTest = {
    testSpriteConversion,
    runBrowserTest,
    createTestElementsData
  };

  console.log('🔧 SpriteConverterTest已添加到window对象');
  console.log('可以使用以下方法:');
  console.log('- window.SpriteConverterTest.testSpriteConversion()');
  console.log('- window.SpriteConverterTest.runBrowserTest()');
}


