/**
 * 基础属性处理模块
 * 负责处理 Sprite 的基础属性
 */

/**
 * 基础属性列表
 */
export const BASIC_PROPERTIES = [
  'name',
  'x', 'y', 'width', 'height',           // 位置和尺寸 (数值)
  'anchorX', 'anchorY',                  // 锚点 (数值)
  'scaleX', 'scaleY',                    // 缩放 (数值)
  'skewX', 'skewY',                      // 倾斜 (数值)
  'alpha',                               // 透明度 (数值)
  'visible'                              // 可见性 (布尔)
];

/**
 * 默认值映射
 */
const DEFAULT_VALUES: Record<string, any> = {
  'x': 0,
  'y': 0,
  'width': 0,
  'height': 0,
  'anchorX': 0,
  'anchorY': 0,
  'scaleX': 1,
  'scaleY': 1,
  'skewX': 0,
  'skewY': 0,
  'alpha': 1,
  'visible': true
};

/**
 * 处理基础属性
 * @param properties 属性对象
 * @param varName 变量名
 * @param indent 缩进字符串
 * @returns 生成的代码字符串
 */
export function processBasicProperties(
  properties: Record<string, any>,
  varName: string,
  indent: string
): string {
  console.log('=== 开始处理基础属性 ===');
  
  let code = '';
  
  // 过滤出基础属性
  const basicProps = filterBasicProperties(properties);
  
  if (Object.keys(basicProps).length === 0) {
    console.log('没有需要设置的基础属性');
    return code;
  }
  
  code += `${indent}// 设置基础属性\n`;
  
  // 按优先级排序处理属性
  const sortedProps = sortPropertiesByPriority(basicProps);
  
  for (const [propName, value] of sortedProps) {
    const assignmentCode = generatePropertyAssignment(varName, propName, value, indent);
    if (assignmentCode.trim()) {
      code += assignmentCode;
    }
  }
  
  console.log(`基础属性处理完成，生成了 ${sortedProps.length} 个属性设置`);
  return code;
}

/**
 * 过滤基础属性
 */
function filterBasicProperties(properties: Record<string, any>): Record<string, any> {
  const filtered: Record<string, any> = {};
  
  for (const propName of BASIC_PROPERTIES) {
    if (properties.hasOwnProperty(propName)) {
      const value = properties[propName];
      
      // 跳过无效值
      if (value === undefined || value === null) continue;
      
      // 跳过默认值
      if (DEFAULT_VALUES[propName] !== undefined && value === DEFAULT_VALUES[propName]) {
        continue;
      }
      
      // 验证数值合理性
      if (typeof value === 'number' && !isValidNumber(propName, value)) {
        continue;
      }
      
      filtered[propName] = value;
    }
  }
  
  return filtered;
}

/**
 * 验证数值的合理性
 */
function isValidNumber(propName: string, value: number): boolean {
  // 检查是否为有效数字
  if (!isFinite(value) || isNaN(value)) {
    return false;
  }
  
  // 特定属性的范围检查
  switch (propName) {
    case 'scaleX':
    case 'scaleY':
      return value > 0.01 && value <= 10;
    case 'alpha':
      return value >= 0 && value <= 1;
    case 'anchorX':
    case 'anchorY':
      return value >= 0 && value <= 1;
    default:
      return true;
  }
}

/**
 * 按优先级排序属性
 * 确保属性设置的正确顺序（例如：anchor 在 position 之前）
 */
function sortPropertiesByPriority(properties: Record<string, any>): [string, any][] {
  const priorityOrder = [
    'name',
    'anchorX', 'anchorY',    // 锚点优先
    'width', 'height',       // 尺寸其次
    'scaleX', 'scaleY',      // 缩放
    'x', 'y',                // 位置最后（避免与锚点冲突）
    'skewX', 'skewY',        // 倾斜
    'alpha',                 // 透明度
    'visible'                // 可见性
  ];
  
  const entries = Object.entries(properties);
  
  // 按优先级排序
  entries.sort(([a], [b]) => {
    const indexA = priorityOrder.indexOf(a);
    const indexB = priorityOrder.indexOf(b);
    
    // 如果都在优先级列表中，按列表顺序
    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }
    
    // 如果只有一个在列表中，优先处理列表中的
    if (indexA !== -1) return -1;
    if (indexB !== -1) return 1;
    
    // 都不在列表中，按字母顺序
    return a.localeCompare(b);
  });
  
  return entries;
}

/**
 * 生成属性赋值代码
 */
function generatePropertyAssignment(
  varName: string,
  propName: string,
  value: any,
  indent: string
): string {
  switch (propName) {
    case 'name':
      return typeof value === 'string' && value !== '' 
        ? `${indent}${varName}.name = "${value}";\n` 
        : '';
        
    case 'scaleX':
      return value !== 1 ? `${indent}${varName}.scale.x = ${value};\n` : '';
      
    case 'scaleY':
      return value !== 1 ? `${indent}${varName}.scale.y = ${value};\n` : '';
      
    case 'anchorX':
      return value !== 0 ? `${indent}${varName}.anchor.x = ${value};\n` : '';
      
    case 'anchorY':
      return value !== 0 ? `${indent}${varName}.anchor.y = ${value};\n` : '';
      
    case 'skewX':
      return value !== 0 ? `${indent}${varName}.skew.x = ${value};\n` : '';
      
    case 'skewY':
      return value !== 0 ? `${indent}${varName}.skew.y = ${value};\n` : '';
      
    case 'x':
    case 'y':
      return value !== 0 ? `${indent}${varName}.${propName} = ${value};\n` : '';
      
    case 'width':
    case 'height':
      return value > 0 ? `${indent}${varName}.${propName} = ${value};\n` : '';
      
    case 'alpha':
      return value !== 1 ? `${indent}${varName}.${propName} = ${value};\n` : '';
      
    case 'visible':
      return value !== true ? `${indent}${varName}.${propName} = ${value};\n` : '';
      
    default:
      // 通用处理
      if (typeof value === 'string' && value !== '') {
        return `${indent}${varName}.${propName} = "${value}";\n`;
      } else if (typeof value === 'number' && value !== 0) {
        return `${indent}${varName}.${propName} = ${value};\n`;
      } else if (typeof value === 'boolean' && value !== true) {
        return `${indent}${varName}.${propName} = ${value};\n`;
      }
      return '';
  }
}
