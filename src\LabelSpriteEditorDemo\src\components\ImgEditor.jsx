import { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Button,
  Paper,
  TextField,
  Grid,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  Divider,
  FormControlLabel,
  Switch
} from '@mui/material';
import ContentCutIcon from '@mui/icons-material/ContentCut';
import SaveIcon from '@mui/icons-material/Save';
import FolderOpenIcon from '@mui/icons-material/FolderOpen';
import GridOnIcon from '@mui/icons-material/GridOn';
import GridOffIcon from '@mui/icons-material/GridOff';
import useEditorStore from '../store/editorStore';
import NumberInput from './NumberInput';

const ImgEditor = ({ open, onClose }) => {
  // 从 store 中获取 redraw 方法
  const { redraw } = useEditorStore();
  // 状态
  const [images, setImages] = useState([]);
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState(null);
  const [regions, setRegions] = useState([]);
  const [newRegion, setNewRegion] = useState({
    label: '',
    sx: 0,
    sy: 0,
    sw: 64,
    sh: 64
  });
  // 网格设置
  const [gridSettings, setGridSettings] = useState({
    rows: 4,
    columns: 4,
    rowGap: 0,  // 行间距
    columnGap: 0,  // 列间距
    showGrid: true
  });

  // 引用
  const canvasRef = useRef(null);
  const imgRef = useRef(null);
  const fileInputRef = useRef(null);

  // 获取当前sprite
  const getCurrentSprite = useCallback(() => {
    if (window.SpriteEditor && window.SpriteEditor.currentSprite) {
      return window.SpriteEditor.currentSprite;
    }
    return null;
  }, []);

  // 绘制canvas - 绘制网格和选区边框
  const drawCanvas = useCallback(() => {
    if (!canvasRef.current || !selectedImage || !imgRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制选区边框 - 使用淡蓝色虚线
    if (regions && regions.length > 0) {
      ctx.save();
      ctx.strokeStyle = 'rgba(0, 0, 255, 0.3)';
      ctx.lineWidth = 1;
      ctx.setLineDash([3, 3]);

      regions.forEach(region => {
        if (selectedRegion && selectedRegion.id === region.id) {
          // 选中的区域使用实线蓝色边框
          ctx.strokeStyle = '#1976d2';
          ctx.lineWidth = 2;
          ctx.setLineDash([]);
        } else {
          // 未选中的区域使用虚线淡蓝色边框
          ctx.strokeStyle = 'rgba(0, 0, 255, 0.3)';
          ctx.lineWidth = 1;
          ctx.setLineDash([3, 3]);
        }

        ctx.strokeRect(region.sx, region.sy, region.sw, region.sh);
      });

      ctx.restore();
    }

    // 绘制网格
    if (gridSettings.showGrid && imgRef.current) {
      const { rows, columns, rowGap, columnGap } = gridSettings;
      const img = imgRef.current;

      // 计算单元格尺寸，考虑间距（支持负间距）
      const totalColumnGap = columnGap * (columns - 1);
      const totalRowGap = rowGap * (rows - 1);
      // 确保单元格尺寸不会变为负数或过小
      const cellWidth = Math.max(1, (img.width - totalColumnGap) / columns);
      const cellHeight = Math.max(1, (img.height - totalRowGap) / rows);

      ctx.strokeStyle = 'rgba(0, 0, 255, 0.5)';
      ctx.lineWidth = 1;

      // 绘制垂直线
      let xPos = 0;
      for (let i = 0; i <= columns; i++) {
        ctx.beginPath();
        ctx.moveTo(xPos, 0);
        ctx.lineTo(xPos, img.height);
        ctx.stroke();

        // 下一个位置 = 当前位置 + 单元格宽度 + 列间距
        if (i < columns) {
          xPos += cellWidth;
          if (i < columns - 1) {
            xPos += columnGap;
          }
        }
      }

      // 绘制水平线
      let yPos = 0;
      for (let i = 0; i <= rows; i++) {
        ctx.beginPath();
        ctx.moveTo(0, yPos);
        ctx.lineTo(img.width, yPos);
        ctx.stroke();

        // 下一个位置 = 当前位置 + 单元格高度 + 行间距
        if (i < rows) {
          yPos += cellHeight;
          if (i < rows - 1) {
            yPos += rowGap;
          }
        }
      }
    }
  }, [gridSettings, selectedImage, regions, selectedRegion]);



  // 生成所有网格选区
  const generateAllGridRegions = useCallback(() => {
    if (!selectedImage || !imgRef.current) return [];

    const img = imgRef.current;
    const { rows, columns, rowGap, columnGap } = gridSettings;

    // 计算单元格尺寸，考虑间距（支持负间距）
    const totalColumnGap = columnGap * (columns - 1);
    const totalRowGap = rowGap * (rows - 1);
    // 确保单元格尺寸不会变为负数或过小
    const cellWidth = Math.max(1, (img.width - totalColumnGap) / columns);
    const cellHeight = Math.max(1, (img.height - totalRowGap) / rows);

    const allRegions = [];

    // 生成所有网格单元格的选区
    let yPos = 0;
    for (let row = 0; row < rows; row++) {
      let xPos = 0;
      for (let col = 0; col < columns; col++) {
        const gridIndex = row * columns + col;
        const sx = xPos;
        const sy = yPos;
        const sw = cellWidth;
        const sh = cellHeight;

        // 更新下一个水平位置
        xPos += cellWidth + (col < columns - 1 ? columnGap : 0);

        allRegions.push({
          id: `grid_${row}_${col}`,  // 移除时间戳，使ID保持一致
          label: `网格 ${row + 1}x${col + 1}`,
          sx,
          sy,
          sw,
          sh,
          gridIndex
        });
      }
      // 更新下一个垂直位置
      yPos += cellHeight + (row < rows - 1 ? rowGap : 0);
    }

    return allRegions;
  }, [selectedImage, gridSettings]);

  // 生成所有网格选区（清除之前的所有数据）
  const addAllGridRegions = useCallback(() => {
    if (!selectedImage || !imgRef.current) return;

    console.log('开始生成网格选区，清除之前的所有数据');
    // 使用函数式方式获取当前regions数量
    setRegions(currentRegions => {
      console.log('生成前regions数量:', currentRegions.length);
      return currentRegions; // 不改变状态，只是用来获取当前值
    });

    // 生成所有网格选区
    const allGridRegions = generateAllGridRegions();
    console.log('新生成的网格选区数量:', allGridRegions.length);

    if (allGridRegions.length === 0) return;

    // 保存到source
    if (selectedImage.source) {
      // 保存网格设置到source
      selectedImage.source.gridSettings = { ...gridSettings };

      // 完全替换source.regions，清除所有之前的选区
      selectedImage.source.regions = [...allGridRegions];

      console.log('已重新生成所有网格选区:', selectedImage.source.regions);
    }

    // 更新本地状态，完全替换之前的所有选区
    setRegions(allGridRegions);
    console.log('已设置新的regions，数量:', allGridRegions.length);

    // 选中第一个网格选区
    if (allGridRegions.length > 0) {
      setSelectedRegion(allGridRegions[0]);
      setNewRegion({
        label: allGridRegions[0].label,
        sx: allGridRegions[0].sx,
        sy: allGridRegions[0].sy,
        sw: allGridRegions[0].sw,
        sh: allGridRegions[0].sh
      });
      console.log('已选中第一个网格选区:', allGridRegions[0]);
    }

    // 延迟检查更新后的状态
    setTimeout(() => {
      setRegions(currentRegions => {
        console.log('更新后regions数量:', currentRegions.length);
        return currentRegions; // 不改变状态，只是用来获取当前值
      });
    }, 100);
  }, [selectedImage, gridSettings, generateAllGridRegions]);

  // 处理网格单元格点击 - 仅用于选择已有选区
  const handleGridCellClick = useCallback((row, col) => {
    if (!selectedImage || !imgRef.current) return;

    // 只有在已经生成了网格选区的情况下才允许选择
    if (!selectedImage.source || !selectedImage.source.regions) {
      console.log('请先点击"生成选区"按钮生成网格选区');
      return;
    }

    const gridIndex = row * gridSettings.columns + col;

    // 查找对应的网格选区
    const existingRegion = selectedImage.source.regions.find(r =>
      r.gridIndex === gridIndex
    );

    if (existingRegion) {
      // 选中该区域
      setSelectedRegion(existingRegion);
      setNewRegion({
        label: existingRegion.label,
        sx: existingRegion.sx,
        sy: existingRegion.sy,
        sw: existingRegion.sw,
        sh: existingRegion.sh
      });
    } else {
      console.log('未找到对应的网格选区，请先点击"生成选区"按钮');
    }
  }, [selectedImage, gridSettings]);

  // 初始化时加载图片元素
  useEffect(() => {
    if (open) {
      // 内联实现loadImagesFromSprite逻辑，避免循环依赖
      const sprite = getCurrentSprite();
      if (!sprite || !sprite.bitmap) return;

      console.log('当前sprite的bitmap:', sprite.bitmap);

      // 检查bitmap.imgs数组
      const uniqueImages = [];
      const sources = new Set();

      // 如果bitmap.imgs存在，从中提取不同路径的source
      if (sprite.bitmap.imgs && Array.isArray(sprite.bitmap.imgs)) {
        console.log('从bitmap.imgs中提取图片元素:', sprite.bitmap.imgs.length);

        sprite.bitmap.imgs.forEach(imgElement => {
          if (imgElement.source && typeof imgElement.source === 'object' && imgElement.source._url) {
            if (!sources.has(imgElement.source._url)) {
              sources.add(imgElement.source._url);
              uniqueImages.push({
                source: imgElement.source,
                url: imgElement.source._url,
                elements: [imgElement]
              });
              console.log('添加新图片源:', imgElement.source._url);
            } else {
              // 添加到已有图片的elements数组中
              const existingImage = uniqueImages.find(img => img.url === imgElement.source._url);
              if (existingImage) {
                existingImage.elements.push(imgElement);
              }
            }
          }
        });
      }

      // 如果bitmap.elements存在，也从中提取图片元素
      if (sprite.bitmap.elements && Array.isArray(sprite.bitmap.elements)) {
        console.log('从bitmap.elements中提取图片元素:', sprite.bitmap.elements.length);

        // 过滤出图片元素
        const imageElements = sprite.bitmap.elements.filter(el => el.type === 'image' && el.source);

        imageElements.forEach(el => {
          if (el.source && typeof el.source === 'object' && el.source._url) {
            if (!sources.has(el.source._url)) {
              sources.add(el.source._url);
              uniqueImages.push({
                source: el.source,
                url: el.source._url,
                elements: [el]
              });
              console.log('添加新图片源:', el.source._url);
            } else {
              // 添加到已有图片的elements数组中
              const existingImage = uniqueImages.find(img => img.url === el.source._url);
              if (existingImage) {
                existingImage.elements.push(el);
              }
            }
          }
        });
      }

      setImages(uniqueImages);
      if (uniqueImages.length > 0) {
        const firstImage = uniqueImages[0];
        setSelectedImage(firstImage);

        // 内联loadRegionsForImage逻辑
        if (firstImage) {
          let extractedRegions = [];

          // 首先检查source中是否有保存的regions
          if (firstImage.source && firstImage.source.regions && firstImage.source.regions.length > 0) {
            console.log('从source加载区域数据:', firstImage.source.regions);
            // 直接使用source中的regions，不再清除
            extractedRegions = [...firstImage.source.regions];
            console.log('已加载regions数量:', extractedRegions.length);

            // 检查是否有保存的网格设置
            if (firstImage.source.gridSettings) {
              console.log('从source加载网格设置:', firstImage.source.gridSettings);
              setGridSettings(firstImage.source.gridSettings);
            }
          }
          // 如果source中没有regions，则从元素中提取
          else if (firstImage.elements && firstImage.elements.length > 0) {
            console.log('从元素中提取区域数据');
            extractedRegions = firstImage.elements.map((el, index) => ({
              id: el.id || `region_${index}`,
              label: el.label || `区域 ${index + 1}`,
              sx: el.sx || 0,
              sy: el.sy || 0,
              sw: el.sw || 64,
              sh: el.sh || 64,
              element: el
            }));

            // 将提取的区域保存到source中
            if (firstImage.source && extractedRegions.length > 0) {
              // 如果source.regions不存在，则创建
              if (!firstImage.source.regions) {
                firstImage.source.regions = [];
              }

              // 合并新提取的区域，避免重复
              extractedRegions.forEach(region => {
                // 检查是否已存在相同的区域
                const existingRegion = firstImage.source.regions.find(r =>
                  r.sx === region.sx &&
                  r.sy === region.sy &&
                  r.sw === region.sw &&
                  r.sh === region.sh
                );

                if (!existingRegion) {
                  firstImage.source.regions.push(region);
                }
              });

              console.log('已将提取的区域保存到source:', firstImage.source.regions);
              extractedRegions = [...firstImage.source.regions];
            }
          }

          setRegions(extractedRegions);
          if (extractedRegions.length > 0) {
            setSelectedRegion(extractedRegions[0]);
            setNewRegion({
              label: extractedRegions[0].label,
              sx: extractedRegions[0].sx,
              sy: extractedRegions[0].sy,
              sw: extractedRegions[0].sw,
              sh: extractedRegions[0].sh
            });
          }
        }
      }
    }
  }, [open, getCurrentSprite]);


  // 监听图片和区域变化，更新canvas
  useEffect(() => {
    if (canvasRef.current && imgRef.current && imgRef.current.complete) {
      const canvas = canvasRef.current;
      canvas.width = imgRef.current.width;
      canvas.height = imgRef.current.height;
      drawCanvas();
    }
  }, [selectedImage, regions, selectedRegion, gridSettings, drawCanvas]);

  // 不再自动添加网格选区，只有点击"生成选区"按钮才会添加





  // 处理图片选择
  const handleImageSelect = (image) => {
    setSelectedImage(image);
    console.log('选择图片:', image);

    // 内联loadRegionsForImage逻辑
    if (image) {
      let extractedRegions = [];

      // 首先检查source中是否有保存的regions
      if (image.source && image.source.regions && image.source.regions.length > 0) {
        console.log('从source加载区域数据:', image.source.regions);
        // 直接使用source中的regions，不再清除
        extractedRegions = [...image.source.regions];
        console.log('已加载regions数量:', extractedRegions.length);

        // 检查是否有保存的网格设置
        if (image.source.gridSettings) {
          console.log('从source加载网格设置:', image.source.gridSettings);
          setGridSettings(image.source.gridSettings);
        }
      }
      // 如果source中没有regions，则从元素中提取
      else if (image.elements && image.elements.length > 0) {
        console.log('从元素中提取区域数据');
        extractedRegions = image.elements.map((el, index) => ({
          id: el.id || `region_${index}`,
          label: el.label || `区域 ${index + 1}`,
          sx: el.sx || 0,
          sy: el.sy || 0,
          sw: el.sw || 64,
          sh: el.sh || 64,
          element: el
        }));

        // 将提取的区域保存到source中
        if (image.source && extractedRegions.length > 0) {
          // 如果source.regions不存在，则创建
          if (!image.source.regions) {
            image.source.regions = [];
          }

          // 合并新提取的区域，避免重复
          extractedRegions.forEach(region => {
            // 检查是否已存在相同的区域
            const existingRegion = image.source.regions.find(r =>
              r.sx === region.sx &&
              r.sy === region.sy &&
              r.sw === region.sw &&
              r.sh === region.sh
            );

            if (!existingRegion) {
              image.source.regions.push(region);
            }
          });

          console.log('已将提取的区域保存到source:', image.source.regions);
          extractedRegions = [...image.source.regions];
        }
      }

      setRegions(extractedRegions);
      if (extractedRegions.length > 0) {
        setSelectedRegion(extractedRegions[0]);
        setNewRegion({
          label: extractedRegions[0].label,
          sx: extractedRegions[0].sx,
          sy: extractedRegions[0].sy,
          sw: extractedRegions[0].sw,
          sh: extractedRegions[0].sh
        });
      } else {
        setRegions([]);
        setSelectedRegion(null);
      }
    }
  };

  // 处理区域选择
  const handleRegionSelect = (region) => {
    setSelectedRegion(region);
    setNewRegion({
      label: region.label,
      sx: region.sx,
      sy: region.sy,
      sw: region.sw,
      sh: region.sh
    });

    // 更新Canvas绘制
    setTimeout(() => {
      drawCanvas();
    }, 0);
  };
  // 添加到Canvas
  const handleAddToCanvas = () => {
    if (!selectedImage || !selectedRegion) return;

    const sprite = getCurrentSprite();
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
      console.error('无法获取sprite或相关属性');
      return;
    }

    // 计算目标位置（居中）
    const dx = Math.floor((sprite.bitmap.width - selectedRegion.sw) / 2);
    const dy = Math.floor((sprite.bitmap.height - selectedRegion.sh) / 2);
    const dw = selectedRegion.sw;
    const dh = selectedRegion.sh;

    // 打印画布尺寸和图片位置信息
    console.log('画布尺寸:', sprite.bitmap.width, sprite.bitmap.height);
    console.log('图片位置:', dx, dy, dw, dh);

    // 打印源图像信息
    if (selectedImage.source && selectedImage.source._image) {
      console.log('源图像信息:', {
        width: selectedImage.source._image.width,
        height: selectedImage.source._image.height,
        url: selectedImage.source._url
      });
    }

    // 创建新的图片元素
    const newElement = {
      type: 'image',
      source: selectedImage.source,
      sx: selectedRegion.sx,
      sy: selectedRegion.sy,
      sw: selectedRegion.sw,
      sh: selectedRegion.sh,
      dx: dx,
      dy: dy,
      dw: dw,
      dh: dh,
      bounds: {
        x: dx,
        y: dy,
        width: dw,
        height: dh
      },
      label: selectedRegion.label,
      color: 'rgba(255, 0, 0, 0.5)',
      // 保存分割信息
      gridInfo: selectedRegion.gridIndex !== undefined ? {
        rows: gridSettings.rows,
        columns: gridSettings.columns,
        index: selectedRegion.gridIndex || 0
      } : null
    };

    // 添加到elements数组
    if (!sprite.bitmap.elements) {
      console.error('sprite.bitmap.elements不存在，尝试创建');
      sprite.bitmap.elements = [];
    }

    sprite.bitmap.elements.push(newElement);
    console.log('添加到canvas:', newElement);
    console.log('elements数组长度:', sprite.bitmap.elements.length);

    // 直接调用原始的blt方法进行绘制
    if (sprite.bitmap.blt) {
      console.log('直接调用blt方法绘制图像');
      try {
        sprite.bitmap.blt(
          selectedImage.source,
          selectedRegion.sx,
          selectedRegion.sy,
          selectedRegion.sw,
          selectedRegion.sh,
          dx,
          dy,
          dw,
          dh
        );

        console.log('blt方法调用成功');
      } catch (error) {
        console.error('blt方法调用失败:', error);
      }
    } else {
      console.error('sprite.bitmap.blt方法不存在');
    }

    // 重新绘制
    console.log('调用redraw方法');
    redraw();
  };

  // 打开文件选择器
  const handleOpenFileSelector = () => {
    // 检查是否有外部（Tauri）提供的文件选择方法
    if (window.handleFileSelectDialog && typeof window.handleFileSelectDialog === 'function') {
      console.log('检测到外部文件选择方法，使用Tauri文件对话框');

      // 使用外部提供的文件选择方法
      window.handleFileSelectDialog((selectedPath, arrayBuffer) => {
        if (selectedPath) {
          console.log('外部文件选择成功:', selectedPath);
          console.log('ArrayBuffer大小:', arrayBuffer?.byteLength);

          // 如果提供了ArrayBuffer，使用ArrayBuffer加载
          if (arrayBuffer && arrayBuffer.byteLength > 0) {
            console.log('使用ArrayBuffer加载图片:', selectedPath);
            loadImageFromArrayBuffer(arrayBuffer, selectedPath);
          } else {
            console.log('未提供ArrayBuffer数据');
            alert('请在Tauri端传递ArrayBuffer数据以支持图片加载');
          }
        } else {
          console.log('用户取消了文件选择');
        }
      });
      return;
    }

    // 回退到浏览器标准文件选择器
    console.log('使用浏览器标准文件选择器');
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 处理文件选择（用于浏览器环境）
  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件');
      return;
    }

    // 创建文件URL
    const fileUrl = URL.createObjectURL(file);
    console.log('图片加载中:', fileUrl);

    // 在浏览器环境中，使用文件名作为原始路径的标识
    const originalPath = file.name; // 至少保存文件名
    console.log('浏览器环境，使用文件名作为原始路径:', originalPath);

    loadImageFromUrl(fileUrl, originalPath);
  };

  // 从ArrayBuffer加载图片的方法（仅在handleFileSelectDialog回调中使用）
  const loadImageFromArrayBuffer = (arrayBuffer, filePath) => {
    console.log('从ArrayBuffer加载图片:', filePath, 'bufferSize:', arrayBuffer.byteLength);

    try {
      // 创建Blob URL
      const blob = new Blob([arrayBuffer]);
      const blobUrl = URL.createObjectURL(blob);

      // 使用RPG Maker的ImageManager加载
      const bitmap = window.ImageManager.loadBitmapFromUrl(blobUrl);

      // 设置传入的路径到bitmap
      bitmap._url = filePath;

      bitmap.addLoadListener(function (loadedBitmap) {
        console.log('ArrayBuffer bitmap加载完成:', loadedBitmap);

        // 确保regions数组存在
        if (!loadedBitmap.regions) {
          loadedBitmap.regions = [];
        }

        // 添加到images数组
        const newImage = {
          source: loadedBitmap,
          url: blobUrl,
          originalPath: filePath,
          elements: []
        };

        setImages(prev => [...prev, newImage]);
        setSelectedImage(newImage);
        setRegions([]);
      });
    } catch (error) {
      console.error('ArrayBuffer加载失败:', error);
      alert('ArrayBuffer加载失败: ' + error.message);
    }
  };

  // 统一的图片加载方法
  const loadImageFromUrl = (fileUrl, originalPath = null) => {
    try {
      // 检查ImageManager是否可用
      if (!window.ImageManager) {
        throw new Error('ImageManager未定义，请确保RPG Maker引擎已加载');
      }

      // 使用ImageManager加载图片
      const bitmap = window.ImageManager.loadBitmapFromUrl(fileUrl);
      console.log('正在加载bitmap:', bitmap);

      // 使用addLoadListener确保图片加载完成
      bitmap.addLoadListener(function (loadedBitmap) {
        console.log('bitmap加载完成:', loadedBitmap);

        // 确保regions数组存在
        if (!loadedBitmap.regions) {
          loadedBitmap.regions = [];
        }

        // 保存原始路径信息到bitmap
        if (originalPath) {
          loadedBitmap.originalPath = originalPath;
          loadedBitmap._originalUrl = originalPath;

          // 如果不是blob URL（即真实路径），则覆盖_url属性
          if (!fileUrl.startsWith('blob:')) {
            loadedBitmap._url = originalPath;
            console.log('已将bitmap._url设置为原始路径:', originalPath);
          } else {
            // 如果是blob URL，保持原有的_url，但记录原始路径信息
            console.log('保存原始路径信息到bitmap（文件名）:', originalPath);
            console.log('bitmap._url保持为blob URL:', loadedBitmap._url);
          }
        }

        // 添加到images数组
        const newImage = {
          source: loadedBitmap,
          url: fileUrl,
          originalPath: originalPath || fileUrl,
          elements: []
        };

        setImages(prev => [...prev, newImage]);
        setSelectedImage(newImage);
        setRegions([]);
      });
    } catch (error) {
      console.error('加载图片失败:', error);

      // 回退方法：使用原生Image和Bitmap
      console.log('尝试使用回退方法加载图片');
      const img = new Image();

      img.onload = () => {
        console.log('图片加载成功:', {
          width: img.width,
          height: img.height,
          src: fileUrl
        });

        try {
          // 使用RPG Maker的Bitmap类
          if (!window.Bitmap) {
            throw new Error('Bitmap未定义，请确保RPG Maker引擎已加载');
          }

          const bitmap = new window.Bitmap(img.width, img.height);

          // 加载图片到bitmap
          bitmap._image = img;
          bitmap._loadingState = "loaded"; // 直接设置为已加载状态

          // 保存原始路径信息，优先使用原始路径
          if (originalPath && !fileUrl.startsWith('blob:')) {
            bitmap.originalPath = originalPath;
            bitmap._originalUrl = originalPath;
            bitmap._url = originalPath; // 使用原始路径而不是blob URL
            console.log('回退方法：已将bitmap._url设置为原始路径:', originalPath);
          } else {
            bitmap._url = fileUrl; // 如果没有原始路径，使用fileUrl
            if (originalPath) {
              bitmap.originalPath = originalPath;
              bitmap._originalUrl = originalPath;
              console.log('回退方法：保存原始路径信息到bitmap:', originalPath);
            }
          }

          // 确保elements数组存在
          if (!bitmap.elements) {
            bitmap.elements = [];
          }

          // 确保regions数组存在
          if (!bitmap.regions) {
            bitmap.regions = [];
          }

          console.log('创建的bitmap:', bitmap);

          // 添加到images数组
          const newImage = {
            source: bitmap,
            url: fileUrl,
            originalPath: originalPath || fileUrl,
            elements: []
          };

          setImages(prev => [...prev, newImage]);
          setSelectedImage(newImage);
          setRegions([]);
        } catch (bitmapError) {
          console.error('创建bitmap失败:', bitmapError);
          alert('创建bitmap失败，请确保RPG Maker引擎已加载');
        }
      };

      img.onerror = () => {
        console.error('图片加载失败:', fileUrl);
        alert('图片加载失败，请选择有效的图片文件');
        // 只有在使用 URL.createObjectURL 创建的 URL 时才需要释放
        if (fileUrl.startsWith('blob:')) {
          URL.revokeObjectURL(fileUrl);
        }
      };

      img.src = fileUrl;
    }
  };

  // 🎯 获取图片显示ArrayBuffer的方法（完全基于ArrayBuffer）
  const getImageDisplayArrayBuffer = async (image) => {
    // 如果bitmap对象有_image，转换为ArrayBuffer
    if (image.source && image.source._image) {
      // 检查是否已经有缓存的ArrayBuffer
      if (image.source._displayArrayBuffer) {
        return image.source._displayArrayBuffer;
      }

      try {
        // 创建canvas来转换bitmap为ArrayBuffer
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = image.source._image.width;
        canvas.height = image.source._image.height;

        // 绘制图片到canvas
        ctx.drawImage(image.source._image, 0, 0);

        // 转换为ArrayBuffer
        const arrayBuffer = await new Promise((resolve) => {
          canvas.toBlob((blob) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.readAsArrayBuffer(blob);
          }, 'image/png');
        });

        // 缓存ArrayBuffer，避免重复转换
        image.source._displayArrayBuffer = arrayBuffer;

        console.log('已为bitmap创建显示ArrayBuffer:', image.source._url, 'size:', arrayBuffer.byteLength);
        return arrayBuffer;
      } catch (error) {
        console.error('转换bitmap为显示ArrayBuffer失败:', error);
        return null;
      }
    }

    return null;
  };

  // 🎯 从ArrayBuffer创建显示URL的方法
  const createDisplayUrlFromArrayBuffer = (arrayBuffer) => {
    if (!arrayBuffer) {
      return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEw0NCA0NE0yMCA0NEw0NCAyMCIgc3Ryb2tlPSIjQ0NDIiBzdHJva2Utd2lkdGg9IjIiLz4KPC9zdmc+';
    }

    try {
      const blob = new Blob([arrayBuffer]);
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error('从ArrayBuffer创建URL失败:', error);
      return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEw0NCA0NE0yMCA0NEw0NCAyMCIgc3Ryb2tlPSIjQ0NDIiBzdHJva2Utd2lkdGg9IjIiLz4KPC9zdmc+';
    }
  };

  // 🎯 获取图片显示URL的方法（基于ArrayBuffer）
  const getImageDisplayUrl = async (image) => {
    const arrayBuffer = await getImageDisplayArrayBuffer(image);
    return createDisplayUrlFromArrayBuffer(arrayBuffer);
  };

  // 🎯 主图片显示组件（支持异步ArrayBuffer加载）
  const MainImageDisplay = ({ imgRef, selectedImage, onLoad, onClick }) => {
    const [displayUrl, setDisplayUrl] = useState('');

    useEffect(() => {
      const loadDisplayUrl = async () => {
        if (!selectedImage) {
          setDisplayUrl('');
          return;
        }

        try {
          const url = await getImageDisplayUrl(selectedImage);
          setDisplayUrl(url);
        } catch (error) {
          console.error('加载主图片显示URL失败:', error);
          setDisplayUrl('');
        }
      };

      loadDisplayUrl();
    }, [selectedImage]);

    if (!selectedImage || !displayUrl) {
      return null;
    }

    return (
      <img
        ref={imgRef}
        src={displayUrl}
        alt="选中的图片"
        style={{ maxWidth: '100%' }}
        onLoad={onLoad}
        onClick={onClick}
      />
    );
  };

  // 🎯 区域预览图片组件（支持异步ArrayBuffer加载）
  const RegionPreviewImage = ({ selectedImage, region }) => {
    const [displayUrl, setDisplayUrl] = useState('');

    useEffect(() => {
      const loadDisplayUrl = async () => {
        if (!selectedImage) {
          setDisplayUrl('');
          return;
        }

        try {
          const url = await getImageDisplayUrl(selectedImage);
          setDisplayUrl(url);
        } catch (error) {
          console.error('加载区域预览图片URL失败:', error);
          setDisplayUrl('');
        }
      };

      loadDisplayUrl();
    }, [selectedImage]);

    if (!selectedImage || !displayUrl) {
      return null;
    }

    return (
      <img
        src={displayUrl}
        alt="预览"
        style={{
          position: 'absolute',
          top: -region.sy,
          left: -region.sx,
          maxWidth: 'none'
        }}
      />
    );
  };

  // 🎯 图片项组件（支持异步ArrayBuffer加载）
  const ImageItem = ({ image, index, isSelected, onClick }) => {
    const [displayUrl, setDisplayUrl] = useState('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEw0NCA0NE0yMCA0NEw0NCAyMCIgc3Ryb2tlPSIjQ0NDIiBzdHJva2Utd2lkdGg9IjIiLz4KPC9zdmc+');

    useEffect(() => {
      const loadDisplayUrl = async () => {
        try {
          const url = await getImageDisplayUrl(image);
          setDisplayUrl(url);
        } catch (error) {
          console.error('加载图片显示URL失败:', error);
        }
      };

      loadDisplayUrl();
    }, [image]);

    return (
      <Paper
        elevation={1}
        sx={{
          p: 0.5,
          cursor: 'pointer',
          border: isSelected ? '2px solid #1976d2' : '2px solid transparent',
          width: 80,
          height: 80,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative'
        }}
        onClick={onClick}
      >
        <img
          src={displayUrl}
          alt={`图片 ${index + 1}`}
          style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }}
        />
        <Typography
          variant="caption"
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            bgcolor: 'rgba(0,0,0,0.5)',
            color: 'white',
            textAlign: 'center',
            fontSize: '0.7rem'
          }}
        >
          {image.source && image.source.regions ? `${image.source.regions.length} 个区域` : `${image.elements.length} 个元素`}
        </Typography>
      </Paper>
    );
  };

  // 渲染图片列表
  const renderImageList = () => {
    return (
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle1" sx={{ mb: 1 }}>可用图片</Typography>
        <Box sx={{ display: 'flex', gap: 1, overflowX: 'auto', pb: 1, flexWrap: 'nowrap' }}>
          {images.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              没有可用的图片元素
            </Typography>
          ) : (
            images.map((image, index) => (
              <ImageItem
                key={index}
                image={image}
                index={index}
                isSelected={selectedImage && selectedImage === image}
                onClick={() => handleImageSelect(image)}
              />
            ))
          )}
          <Button
            variant="outlined"
            startIcon={<FolderOpenIcon />}
            size="small"
            onClick={handleOpenFileSelector}
            sx={{ height: 80, width: 80 }}
          >
            打开
          </Button>
        </Box>
      </Box>
    );
  };

  // 渲染区域列表
  const renderRegionList = () => {
    return (
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="subtitle1">裁切区域</Typography>
        </Box>
        <Box sx={{
          display: 'flex',
          gap: 1,
          overflowX: 'auto',
          pb: 1,
          flexWrap: 'nowrap',
          '&::-webkit-scrollbar': {
            height: '8px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0,0,0,0.2)',
            borderRadius: '4px',
          }
        }}>
          {regions.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              没有定义的裁切区域
            </Typography>
          ) : (
            regions.map((region, index) => (
              <Paper
                key={index}
                elevation={1}
                sx={{
                  p: 0.5,
                  cursor: 'pointer',
                  border: selectedRegion && selectedRegion.id === region.id
                    ? '2px solid #1976d2'
                    : '2px solid transparent',
                  minWidth: 80,
                  height: 80,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                  flexShrink: 0
                }}
                onClick={() => handleRegionSelect(region)}
              >
                {selectedImage && (
                  <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
                    <Box sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      overflow: 'hidden'
                    }}>
                      <RegionPreviewImage
                        selectedImage={selectedImage}
                        region={region}
                      />
                    </Box>
                    <Typography
                      variant="caption"
                      sx={{
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        bgcolor: 'rgba(0,0,0,0.5)',
                        color: 'white',
                        textAlign: 'center',
                        fontSize: '0.7rem'
                      }}
                    >
                      {region.label || `区域 ${index + 1}`}
                    </Typography>
                  </Box>
                )}
              </Paper>
            ))
          )}
        </Box>
      </Box>
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      disableEnforceFocus
      disableAutoFocus
      aria-labelledby="img-editor-title"
    >
      {/* 隐藏的文件输入元素 */}
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        accept="image/*"
        onChange={handleFileSelect}
      />
      <DialogTitle id="img-editor-title">图片编辑器</DialogTitle>
      <DialogContent>
        <Box sx={{
          display: 'flex',
          width: '100%',
          gap: 2,
          flexWrap: 'nowrap'
        }}>
          <Box sx={{
            flex: '1 1 60%',
            minWidth: 0
          }}>
            {/* 图片预览和裁切区域 */}
            <Paper
              sx={{
                p: 1,
                height: 500,
                display: 'flex',
                flexDirection: 'column',
                position: 'relative',
                overflow: 'hidden'
              }}
            >
              {selectedImage ? (
                <>
                  <Box sx={{ mb: 1, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6">网格分割模式</Typography>
                    <Button
                      variant="contained"
                      color="primary"
                      size="small"
                      onClick={addAllGridRegions}
                      title="生成所有网格选区"
                      sx={{ height: '32px' }}
                    >
                      生成选区
                    </Button>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={gridSettings.showGrid}
                            onChange={(e) => setGridSettings(prev => ({
                              ...prev,
                              showGrid: e.target.checked
                            }))}
                            size="small"
                          />
                        }
                        label="显示网格"
                        sx={{ mr: 1 }}
                      />
                      <IconButton
                        onClick={() => setGridSettings(prev => ({
                          ...prev,
                          showGrid: !prev.showGrid
                        }))}
                        size="small"
                      >
                        {gridSettings.showGrid ? <GridOnIcon /> : <GridOffIcon />}
                      </IconButton>
                    </Box>
                  </Box>

                  <Box sx={{ mb: 1, display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                      <NumberInput
                        label="行数"
                        value={gridSettings.rows}
                        onChange={(value) => {
                          setGridSettings(prev => ({
                            ...prev,
                            rows: value
                          }));
                        }}
                        min={1}
                        max={20}
                      />
                      <NumberInput
                        label="行间距"
                        value={gridSettings.rowGap}
                        onChange={(value) => {
                          setGridSettings(prev => ({
                            ...prev,
                            rowGap: value
                          }));
                        }}
                      />
                    </Box>
                    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                      <NumberInput
                        label="列数"
                        value={gridSettings.columns}
                        onChange={(value) => {
                          setGridSettings(prev => ({
                            ...prev,
                            columns: value
                          }));
                        }}
                        min={1}
                        max={20}
                      />
                      <NumberInput
                        label="列间距"
                        value={gridSettings.columnGap}
                        onChange={(value) => {
                          setGridSettings(prev => ({
                            ...prev,
                            columnGap: value
                          }));
                        }}
                      />
                    </Box>
                  </Box>

                  <Box
                    sx={{ flex: 1, position: 'relative', overflow: 'auto' }}
                  >
                    <MainImageDisplay
                      imgRef={imgRef}
                      selectedImage={selectedImage}
                      onLoad={() => {
                        if (canvasRef.current && imgRef.current) {
                          const canvas = canvasRef.current;
                          canvas.width = imgRef.current.width;
                          canvas.height = imgRef.current.height;
                          drawCanvas();
                        }
                      }}
                      onClick={(e) => {
                        if (imgRef.current) {
                          // 计算点击的网格单元格
                          const rect = e.currentTarget.getBoundingClientRect();
                          const x = e.clientX - rect.left;
                          const y = e.clientY - rect.top;

                          const { rows, columns, rowGap, columnGap } = gridSettings;

                          // 计算单元格尺寸，考虑间距（支持负间距）
                          const totalColumnGap = columnGap * (columns - 1);
                          const totalRowGap = rowGap * (rows - 1);
                          // 确保单元格尺寸不会变为负数或过小
                          const cellWidth = Math.max(1, (imgRef.current.width - totalColumnGap) / columns);
                          const cellHeight = Math.max(1, (imgRef.current.height - totalRowGap) / rows);

                          // 计算点击的单元格
                          let col = -1;
                          let xPos = 0;
                          for (let i = 0; i < columns; i++) {
                            const nextXPos = xPos + cellWidth;
                            if (x >= xPos && x < nextXPos) {
                              col = i;
                              break;
                            }
                            xPos = nextXPos + columnGap;
                          }

                          let row = -1;
                          let yPos = 0;
                          for (let i = 0; i < rows; i++) {
                            const nextYPos = yPos + cellHeight;
                            if (y >= yPos && y < nextYPos) {
                              row = i;
                              break;
                            }
                            yPos = nextYPos + rowGap;
                          }

                          // 如果点击在间隙中，忽略
                          if (col === -1 || row === -1) return;

                          handleGridCellClick(row, col);
                        }
                      }}
                    />
                    <canvas
                      ref={canvasRef}
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        pointerEvents: 'none'
                      }}
                    />
                    {/* 显示所有区域的边框，但不显示文字 */}
                    {regions.map((region, index) => (
                      <Box
                        key={index}
                        sx={{
                          position: 'absolute',
                          top: region.sy,
                          left: region.sx,
                          width: region.sw,
                          height: region.sh,
                          border: selectedRegion && selectedRegion.id === region.id
                            ? '2px solid #1976d2'
                            : '1px dashed rgba(0, 0, 255, 0.5)',
                          backgroundColor: 'transparent',
                          cursor: 'pointer'
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRegionSelect(region);
                        }}
                      />
                    ))}
                  </Box>
                </>
              ) : (
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%'
                }}>
                  <Typography variant="body1" color="text.secondary">
                    请选择一个图片
                  </Typography>
                </Box>
              )}
            </Paper>
          </Box>
          <Box sx={{
            flex: '1 1 40%',
            minWidth: 0
          }}>
            {/* 控制面板 */}
            <Paper sx={{ p: 1, height: 500, overflow: 'auto', width: '100%', display: 'flex', flexDirection: 'column' }}>
              <Box sx={{ flex: 'none' }}>
                {renderImageList()}
              </Box>
              <Divider sx={{ my: 2 }} />
              <Box sx={{ flex: 'none' }}>
                {renderRegionList()}
              </Box>
              <Divider sx={{ my: 2 }} />

              {/* 区域属性显示（只读） */}
              <Box sx={{ mb: 2, flex: 'none' }}>
                <Typography variant="subtitle1" sx={{ mb: 1 }}>区域属性</Typography>

                {/* 使用表格式布局，更加紧凑和对齐 */}
                <Box sx={{
                  p: 1.5,
                  border: '1px solid rgba(0, 0, 0, 0.12)',
                  borderRadius: 1,
                  bgcolor: 'rgba(0, 0, 0, 0.02)',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1
                }}>
                  {/* 标签行 */}
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="caption" color="text.secondary" sx={{ width: 60, flexShrink: 0 }}>
                      标签:
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', ml: 1 }}>
                      {selectedRegion ? newRegion.label : '未选择区域'}
                    </Typography>
                  </Box>

                  {/* 坐标和尺寸信息 - 两行两列布局 */}
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {/* 第一行：X起点和Y起点 */}
                    <Box sx={{ display: 'flex', alignItems: 'center', width: '48%' }}>
                      <Typography variant="caption" color="text.secondary" sx={{ width: 60, flexShrink: 0 }}>
                        X起点:
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 'medium', ml: 1 }}>
                        {selectedRegion ? newRegion.sx : '-'}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', width: '48%' }}>
                      <Typography variant="caption" color="text.secondary" sx={{ width: 60, flexShrink: 0 }}>
                        Y起点:
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 'medium', ml: 1 }}>
                        {selectedRegion ? newRegion.sy : '-'}
                      </Typography>
                    </Box>

                    {/* 第二行：宽度和高度 */}
                    <Box sx={{ display: 'flex', alignItems: 'center', width: '48%' }}>
                      <Typography variant="caption" color="text.secondary" sx={{ width: 60, flexShrink: 0 }}>
                        宽度:
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 'medium', ml: 1 }}>
                        {selectedRegion ? newRegion.sw : '-'}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', width: '48%' }}>
                      <Typography variant="caption" color="text.secondary" sx={{ width: 60, flexShrink: 0 }}>
                        高度:
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 'medium', ml: 1 }}>
                        {selectedRegion ? newRegion.sh : '-'}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Paper>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button
          variant="contained"
          color="secondary"
          startIcon={<ContentCutIcon />}
          onClick={handleAddToCanvas}
          disabled={!selectedRegion}
          size="small"
        >
          添加到画布
        </Button>
        <Button onClick={onClose}>关闭</Button>
      </DialogActions>
    </Dialog>
  );
};

ImgEditor.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired
};

export default ImgEditor;
