<script lang="ts">
  import { onMount } from 'svelte';
  import { PaneGroup, Pane, PaneResizer } from "paneforge";

  // 导入各个面板组件
  import TopPanel from "../top/TopPanel.svelte";
  import LeftPanel from "../left/LeftPanel.svelte";
  import CenterPanel from "../center/CenterPanel.svelte";
  import RightPanel from "../right/RightPanel.svelte";
  import BottomPanel from "../bottom/BottomPanel.svelte";

  // 导入主题提供者
  import ThemeProvider from "../theme/ThemeProvider.svelte";

  // 导入项目加载器
  import { projectLoader } from "../logics/projectManage/projectLoad";

  // 导入快捷键系统
  import { initShortcutKeys } from "../shortcutKey";
    import MenuBarPanel from '../menuBar/menuBarPanel.svelte';

  // 应用启动时检查后端是否有保存的项目信息
  onMount(async () => {
    console.log('=== 应用启动，检查后端项目信息 ===');

    try {
      // 初始化快捷键系统
      console.log('=== 初始化快捷键系统 ===');
      initShortcutKeys();
      console.log('快捷键系统初始化完成');

      // 尝试从后端加载项目信息
      const result = await projectLoader.loadFromBackend();

      if (result.success && result.data) {
        console.log('自动加载项目成功:', result.data.projectName);
        console.log('操作记录将在场景对象创建后自动恢复');
      } else {
        console.log('没有需要自动加载的项目:', result.error);
      }
    } catch (error) {
      console.error('检查后端项目信息时发生错误:', error);
    }
  });
</script>

<ThemeProvider>

  <div class="app-layout">
    <MenuBarPanel />
    <!-- 主要的垂直布局：顶部、中间、底部 -->
    <PaneGroup direction="vertical">
   
    <!-- 顶部面板（标题栏） -->
    <!-- <Pane defaultSize={8} minSize={6} maxSize={10}>
 
      <TopPanel />
    </Pane> -->

    <!-- <PaneResizer /> -->

    <!-- 中间区域：左、中、右的水平布局 -->
    <Pane defaultSize={77}>
      <PaneGroup direction="horizontal">
        <!-- 左侧面板 -->
        <Pane defaultSize={20} minSize={15} maxSize={35}>
          <LeftPanel />
        </Pane>

        <PaneResizer />

        <!-- 中心面板 -->
        <Pane defaultSize={60} minSize={30}>
          <CenterPanel />
        </Pane>

        <PaneResizer />

        <!-- 右侧面板 -->
        <Pane defaultSize={20} minSize={15} maxSize={35}>
          <RightPanel />
        </Pane>
      </PaneGroup>
    </Pane>

    <PaneResizer />

    <!-- 底部面板 -->
    <Pane defaultSize={15} minSize={10} maxSize={30}>
      <BottomPanel />
    </Pane>
  </PaneGroup>
  </div>
</ThemeProvider>

<style>
  :global(html, body) {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    color: #0f0f0f;
    background-color: #f6f6f6;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
  }

  .app-layout {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
  }

  /* PaneForge 样式覆盖 */
  :global([data-pane-group]) {
    height: 100%;
  }

  :global([data-pane]) {
    height: 100%;
    overflow: hidden;
  }

  :global([data-pane-resizer]) {
    background: rgba(0, 0, 0, 0.1);
    transition: background-color 0.2s;
  }

  :global([data-pane-resizer]:hover) {
    background: rgba(0, 0, 0, 0.2);
  }

  :global([data-pane-resizer][data-direction="horizontal"]) {
    width: 4px;
    cursor: col-resize;
  }

  :global([data-pane-resizer][data-direction="vertical"]) {
    height: 4px;
    cursor: row-resize;
  }

  /* 深色模式支持 */
  @media (prefers-color-scheme: dark) {
    :global(html, body) {
      color: #f6f6f6;
      background-color: #2f2f2f;
    }

    :global([data-pane-resizer]) {
      background: rgba(255, 255, 255, 0.1);
    }

    :global([data-pane-resizer]:hover) {
      background: rgba(255, 255, 255, 0.2);
    }
  }
</style>
