interface SceneAnalysisResult {
  // 场景基本信息
  sceneClassName: string;
  sceneProperties: BaseDisplayProperties;
  // mapId -> 路径-> 对象
  maps: Map<string, Map<string, BaseDisplayProperties>> // 场景直接子对象（扁平）
  //  路径-> 对象
  windowLayer: Map<string, BaseDisplayProperties>;     // WindowLayer 数据（独立结构）
}

/**
 * 基础显示对象属性 (PIXI.DisplayObject)
 */
export interface BaseDisplayProperties {
  className: string;               // 对象类名
  // 窗口相关
  isWindow: boolean;               // 是否为窗口对象
  // 地图滚动相关
  needsMapScrollListener: boolean; // 是否需要地图滚动监
  name: string;
  // 位置和变换
  x: number;
  y: number;
  scaleX: number;    // scale.x
  scaleY: number;    // scale.y
  skewX: number;     // skew.x
  skewY: number;     // skew.y
  rotation: number;
  width: number;                  // 宽度
  height: number;                 // 高度
  // 显示属性
  alpha: number;
  visible: boolean;

  // 锚点和轴心
  anchorX: number;   // anchor.x
  anchorY: number;   // anchor.y
  pivotX: number;    // pivot.x
  pivotY: number;    // pivot.y
}


interface spriteProperties extends BaseDisplayProperties {
  // 混合模式
  blendMode: number;
  // 遮罩和滤镜 (复杂对象，需要特殊处理)
  mask?: any;
  filters?: any[];
  // 新增：层级信息
  zIndex?: number;                 // Z轴层级
  bitmap: bitmapProperties;
}



interface bitmapProperties {
  fontBold: boolean,
  fontFace: string,
  fontItalic: boolean,
  fontSize: number,
  outlineColor: string,
  outlineWidth: number,
  textColor: string,
  _paintOpacity: number,
  _smooth: boolean,
  elements?: any[],
  url?: string
}

// 2. 添加类型联合，便于类型检查
export type ObjectProperties = BaseDisplayProperties | spriteProperties;