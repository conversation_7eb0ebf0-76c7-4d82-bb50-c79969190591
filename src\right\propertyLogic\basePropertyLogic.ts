/**
 * 基础属性逻辑处理模块
 * 负责处理显示对象的基础属性修改
 */

import { globalObjectState } from '../../stores/objectState';
import { get } from 'svelte/store';

/**
 * 获取对象字段值（支持嵌套属性）
 */
function getFieldValue(obj: any, fieldName: string): any {
  switch (fieldName) {
    case 'anchorX':
      return obj.anchor?.x || 0;
    case 'anchorY':
      return obj.anchor?.y || 0;
    case 'scaleX':
      return obj.scale?.x || 1;
    case 'scaleY':
      return obj.scale?.y || 1;
    case 'skewX':
      return obj.skew?.x || 0;
    case 'skewY':
      return obj.skew?.y || 0;
    case 'visible':
      // 布尔类型需要特殊处理，避免 false 被转换为 0
      return obj[fieldName] !== undefined ? obj[fieldName] : true;
    default:
      return obj[fieldName] || 0;
  }
}

/**
 * 设置对象字段值（支持嵌套属性）
 */
function setFieldValue(obj: any, fieldName: string, value: any): void {
  switch (fieldName) {
    case 'x':
      // 检查对象是否有地图滚动监听功能
      if (obj.setScreenPosition && typeof obj.setScreenPosition === 'function') {
        // 使用专门的方法设置屏幕坐标，会自动转换为地图坐标
        obj.setScreenPosition(value, obj.y);
        console.log('[地图滚动] 使用 setScreenPosition 设置 X 坐标:', value);
      } else {
        // 普通对象，直接设置
        obj.x = value;
      }
      break;
    case 'y':
      // 检查对象是否有地图滚动监听功能
      if (obj.setScreenPosition && typeof obj.setScreenPosition === 'function') {
        // 使用专门的方法设置屏幕坐标，会自动转换为地图坐标
        obj.setScreenPosition(obj.x, value);
        console.log('[地图滚动] 使用 setScreenPosition 设置 Y 坐标:', value);
      } else {
        // 普通对象，直接设置
        obj.y = value;
      }
      break;
    case 'anchorX':
      if (!obj.anchor) {
        // 保留 RPG Maker MZ 的标准锚点设置
        obj.anchor = { x: 0.5, y: 1 };  // 使用 RPG Maker MZ 标准值
        console.log('[锚点修复] 创建新 anchor 对象，使用 RPG Maker MZ 标准值 (0.5, 1)');
      }
      obj.anchor.x = value;
      console.log('[锚点调试] 设置 anchorX:', value, '当前 anchor:', { x: obj.anchor.x, y: obj.anchor.y });
      break;
    case 'anchorY':
      if (!obj.anchor) {
        // 保留 RPG Maker MZ 的标准锚点设置
        obj.anchor = { x: 0.5, y: 1 };  // 使用 RPG Maker MZ 标准值
        console.log('[锚点修复] 创建新 anchor 对象，使用 RPG Maker MZ 标准值 (0.5, 1)');
      }
      obj.anchor.y = value;
      console.log('[锚点调试] 设置 anchorY:', value, '当前 anchor:', { x: obj.anchor.x, y: obj.anchor.y });
      break;
    case 'scaleX':
      if (!obj.scale) obj.scale = { x: 1, y: 1 };
      obj.scale.x = value;
      break;
    case 'scaleY':
      if (!obj.scale) obj.scale = { x: 1, y: 1 };
      obj.scale.y = value;
      break;
    case 'skewX':
      if (!obj.skew) obj.skew = { x: 0, y: 0 };
      obj.skew.x = value;
      break;
    case 'skewY':
      if (!obj.skew) obj.skew = { x: 0, y: 0 };
      obj.skew.y = value;
      break;
    default:
      obj[fieldName] = value;
      break;
  }
}

/**
 * 触发全局状态更新
 */
function triggerStateUpdate(): void {
  const currentState = get(globalObjectState);

  if (currentState.selectedObject && currentState.selectedObject.length > 0) {
    // 触发响应式更新
    globalObjectState.update(state => {
      // 通过重新设置来触发响应式更新
      const temp = state.selectedObject;
      state.selectedObject = null;
      state.selectedObject = temp;
      return state;
    });

    console.log('全局状态已更新');
  }
}

/**
 * 修改显示对象的基础属性 - 支持多对象选择
 * @param targetObjects 目标对象数组
 * @param fieldName 字段名
 * @param newValue 新值
 */
export function updateObjectProperty(
  targetObjects: any | any[],
  fieldName: string,
  newValue: any
): void {
  // 确保 targetObjects 是数组
  const objectsArray = Array.isArray(targetObjects) ? targetObjects : [targetObjects];

  if (!objectsArray.length || !objectsArray[0]) {
    console.warn('updateObjectProperty: 目标对象为空');
    return;
  }

  console.log(`=== 更新多对象属性 ===`);
  console.log('目标对象数组:', objectsArray);
  console.log('字段名:', fieldName);
  console.log('新值:', newValue);

  try {
    // 布尔类型字段，使用统一赋值
    const booleanFields = ['visible'];

    if (booleanFields.includes(fieldName)) {
      // 布尔类型，统一赋值
      console.log(`布尔字段统一赋值: ${fieldName} = ${newValue}`);

      objectsArray.forEach((obj, index) => {
        const oldValue = getFieldValue(obj, fieldName);
        setFieldValue(obj, fieldName, newValue);

        console.log(`对象 ${index}: ${oldValue} -> ${newValue}`);
      });
    } else {
      // 数值类型字段，使用增量修改
      // 计算增量（基于第一个对象的当前值）
      const firstObject = objectsArray[0];
      const currentValue = getFieldValue(firstObject, fieldName);
      const deltaValue = newValue - currentValue;

      console.log(`数值字段增量修改: ${fieldName}`);
      console.log('基准值:', currentValue);
      console.log('目标值:', newValue);
      console.log('增量:', deltaValue);

      // 对所有对象应用增量
      objectsArray.forEach((obj, index) => {
        const oldValue = getFieldValue(obj, fieldName);
        const newObjValue = oldValue + deltaValue;
        setFieldValue(obj, fieldName, newObjValue);

        console.log(`对象 ${index}: ${oldValue} -> ${newObjValue}`);
      });
    }

    // 触发全局状态更新
    triggerStateUpdate();

    console.log('多对象属性更新成功');

  } catch (error) {
    console.error('更新多对象属性失败:', error);
    console.error('目标对象数组:', objectsArray);
    console.error('字段名:', fieldName);
    console.error('新值:', newValue);
  }
}

/**
 * 批量更新对象属性
 * @param targetObject 目标对象
 * @param properties 属性键值对对象
 */
export function updateObjectProperties(
  targetObject: any,
  properties: Record<string, any>
): void {
  if (!targetObject) {
    console.warn('updateObjectProperties: 目标对象为空');
    return;
  }

  console.log(`=== 批量更新对象属性 ===`);
  console.log('目标对象:', targetObject);
  console.log('属性列表:', properties);

  try {
    // 批量更新属性
    Object.entries(properties).forEach(([fieldName, fieldValue]) => {
      const oldValue = targetObject[fieldName];
      console.log(`更新 ${fieldName}: ${oldValue} -> ${fieldValue}`);
      targetObject[fieldName] = fieldValue;
    });

    console.log('批量属性更新成功');

    // 触发全局状态更新
    const currentState = get(globalObjectState);

    if (currentState.selectedObject === targetObject) {
      // 如果修改的是当前选中的对象，触发状态更新
      globalObjectState.update(state => {
        // 通过重新设置来触发响应式更新
        const temp = state.selectedObject;
        state.selectedObject = null;
        state.selectedObject = temp;
        return state;
      });

      console.log('全局状态已更新');
    }

  } catch (error) {
    console.error('批量更新对象属性失败:', error);
    console.error('目标对象:', targetObject);
    console.error('属性列表:', properties);
  }
}

/**
 * 获取对象属性值
 * @param targetObject 目标对象
 * @param fieldName 字段名
 * @returns 属性值
 */
export function getObjectProperty(targetObject: any, fieldName: string): any {
  if (!targetObject) {
    console.warn('getObjectProperty: 目标对象为空');
    return undefined;
  }

  return targetObject[fieldName];
}

/**
 * 检查对象是否具有指定属性
 * @param targetObject 目标对象
 * @param fieldName 字段名
 * @returns 是否具有该属性
 */
export function hasObjectProperty(targetObject: any, fieldName: string): boolean {
  if (!targetObject) {
    return false;
  }

  return fieldName in targetObject;
}

/**
 * 获取对象的所有可编辑属性
 * @param targetObject 目标对象
 * @returns 可编辑属性列表
 */
export function getEditableProperties(targetObject: any): string[] {
  if (!targetObject) {
    return [];
  }

  // 基础显示对象属性
  // 注意：除了 visible (布尔类型) 外，其他都是数值类型
  const baseProperties = [
    "name",
    'x', 'y', 'width', 'height',           // 位置和尺寸 (数值)
    'anchorX', 'anchorY',                  // 锚点 (数值)
    'scaleX', 'scaleY',                    // 缩放 (数值)
    'skewX', 'skewY',                      // 倾斜 (数值)
    'alpha',                               // 透明度 (数值)
    'visible'                              // 可见性 (布尔)
  ];

  // 过滤出对象实际具有的属性
  return baseProperties.filter(prop => hasObjectProperty(targetObject, prop));
}