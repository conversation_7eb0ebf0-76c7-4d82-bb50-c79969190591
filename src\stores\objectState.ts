/**
 * 全局对象状态管理
 */

import { writable } from 'svelte/store';
import type { GlobalObjectState } from '../left/objectTree/types';

import { getCurrentProjectState } from './projectStore';

/**
 * 全局对象状态 - 简化版本，只保存根对象
 */
const initialState: GlobalObjectState = {
  rootObject: null,
  rootObjectType: null,
  objectTreeData: [],
  selectedNodeId: null,
  selectedObject: [],
  selectedObjectType: null,
  lastUpdateTime: 0
};

export const globalObjectState = writable<GlobalObjectState>(initialState);

/**
 * 触发界面更新
 */
export function triggerUpdate() {
  globalObjectState.update(state => {
    state.lastUpdateTime = Date.now();
    return state;
  });
}

/**
 * 设置根对象 - 简化版本
 * @param obj 创建的对象
 * @param typeName 对象类型名称
 */
export async function setRootObject(obj: any, typeName: string) {
  console.log('=== 设置根对象 ===');
  console.log('对象:', obj);
  console.log('类型:', typeName);

  // 提取实际的对象
  let actualObject = obj;

  // 如果是包装对象，提取 displayObject
  if (actualObject && typeof actualObject === 'object' && actualObject.displayObject) {
    console.log('检测到包装对象，提取 displayObject');
    actualObject = actualObject.displayObject;
  }

  globalObjectState.update(state => {
    // 设置根对象（使用原始对象）
    state.rootObject = obj;
    state.rootObjectType = typeName;

    // 同时设置为选中对象（使用实际对象），这样 basePropertyPanel 就能正常工作
    state.selectedObject = [actualObject];
    state.selectedObjectType = [typeName];

    // 更新时间戳，强制触发对象树更新
    state.lastUpdateTime = Date.now();

    console.log('根对象已设置:', {
      rootObjectType: typeName,
      rootObject: obj,
      selectedObject: [actualObject],
      selectedObjectType: [typeName],
      lastUpdateTime: state.lastUpdateTime
    });

    return state;
  });

  // 根对象设置完成后，尝试恢复操作记录
  // await tryRestoreOperationRecords(obj);
}



/**
 * 设置选中对象
 * @param objects 选中的对象数组
 * @param objectTypes 选中对象的类型数组
 * @param nodeId 选中的节点ID（可选）
 */
export function setSelectedObject(objects: any[], objectTypes: string[], nodeId?: string) {
  console.log('=== 设置选中对象 ===');
  console.log('选中对象数组:', objects);
  console.log('对象类型数组:', objectTypes);
  console.log('节点ID:', nodeId);

  globalObjectState.update(state => {
    state.selectedObject = objects;
    state.selectedObjectType = objectTypes;

    if (nodeId !== undefined) {
      state.selectedNodeId = nodeId;
    }

    console.log('选中对象已设置:', {
      selectedObject: objects,
      selectedObjectType: objectTypes,
      selectedNodeId: nodeId
    });

    return state;
  });
}


