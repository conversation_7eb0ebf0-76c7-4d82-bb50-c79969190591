# 序列化器系统

基于继承的对象序列化器系统，参考 `operationNote/spritePro` 的处理方式实现。

## 核心设计原则

### 1. 序列化时：只提取必要属性
- **Bitmap对象**：只保存URL字符串，不保存整个bitmap对象
- **Elements数组**：保存RPGEditor_BitmapTracker插件的elements数组
- **路径优先级**：优先使用 `_originalPath`（CustomResourcePath插件），其次使用 `_url`

### 2. 反序列化时：创建真正的RPG Maker MZ对象
- **Sprite对象**：使用 `new Sprite()` 创建真正的RPG Maker MZ Sprite对象
- **Bitmap对象**：使用 `ImageManager.loadBitmap()` 或 `ImageManager.loadBitmapFromUrl()` 创建
- **Elements处理**：使用 `new Bitmap(Graphics.width, Graphics.height)` 创建支持elements的bitmap

## 文件结构

```
src/generators/
├── type.ts              // 接口定义
├── baseSerializer.ts    // 基类序列化器（处理BaseDisplayProperties）
├── spriteSerializer.ts  // Sprite序列化器（处理spriteProperties）
├── usage.ts            // 使用示例
└── README.md           // 说明文档
```

## 架构设计

### 基类序列化器 (`BaseObjectSerializer`)
- **只有2个方法**：`serialize()` 和 `deserialize()`
- **处理基础属性**：完整实现 `BaseDisplayProperties` 接口
- **创建真正对象**：在RPG Maker MZ环境中创建真正的对象，测试环境中创建模拟对象

### Sprite序列化器 (`SpriteObjectSerializer`)
- **继承基类**：先调用 `super.serialize()` 和 `super.deserialize()`
- **处理扩展属性**：添加 `blendMode`、`zIndex`、`bitmap` 等
- **Bitmap处理**：参考spritePro的处理方式

## 使用方式

### 基础用法

```typescript
import { BaseObjectSerializer } from './baseSerializer';
import { SpriteObjectSerializer } from './spriteSerializer';

// 基础对象序列化
const baseSerializer = new BaseObjectSerializer();
const baseData = baseSerializer.serialize(displayObj);
const baseObj = baseSerializer.deserialize(baseData);

// Sprite对象序列化
const spriteSerializer = new SpriteObjectSerializer();
const spriteData = spriteSerializer.serialize(spriteObj);
const spriteObj = spriteSerializer.deserialize(spriteData);
```

### 序列化流程

```typescript
// 1. 序列化：提取必要属性
const spriteObj = {
  // ... 基础属性
  bitmap: {
    _originalPath: 'img/characters/Actor1.png',
    _url: 'img/characters/Actor1.png',
    fontBold: true,
    // ... 其他属性
  }
};

const serializedData = spriteSerializer.serialize(spriteObj);
// 结果：{ ..., bitmap: { url: 'img/characters/Actor1.png', fontBold: true, ... } }
```

### 反序列化流程

```typescript
// 2. 反序列化：创建真正的RPG Maker MZ对象
const deserializedObj = spriteSerializer.deserialize(serializedData);
// 结果：真正的Sprite对象，bitmap通过ImageManager.loadBitmap创建
```

## 特殊处理

### RPGEditor_BitmapTracker插件
- **Elements数组**：完整保存elements数组
- **图片元素**：只保存URL字符串，不保存整个bitmap对象
- **创建方式**：使用 `new Bitmap(Graphics.width, Graphics.height)` 创建支持elements的bitmap

### CustomResourcePath插件
- **原始路径**：优先使用 `_originalPath` 属性
- **路径解析**：自动分离文件夹和文件名
- **加载方式**：使用 `ImageManager.loadBitmap(folder, filename)` 或 `ImageManager.loadBitmapFromUrl(url)`

## 扩展性

### 添加新的序列化器

```typescript
// 1. 创建新文件：windowSerializer.ts
import { BaseObjectSerializer } from './baseSerializer';
import { WindowProperties } from './type';

export class WindowObjectSerializer extends BaseObjectSerializer {
  serialize(obj: any): WindowProperties {
    // 先调用父类方法
    const baseData = super.serialize(obj);
    
    // 处理Window特有属性
    return {
      ...baseData,
      windowskin: this.extractBitmapUrl(obj._windowskin),
      contents: this.extractBitmapUrl(obj.contents),
      // ... 其他Window属性
    };
  }

  deserialize(data: WindowProperties): any {
    // 先调用父类方法
    const obj = super.deserialize(data);
    
    // 设置Window特有属性
    if (data.windowskin) {
      obj.windowskin = this.createBitmapFromUrl(data.windowskin);
    }
    // ... 设置其他Window属性
    
    return obj;
  }
}
```

### 添加新的接口

```typescript
// 2. 在type.ts中添加新接口
export interface WindowProperties extends BaseDisplayProperties {
  windowskin?: string;
  contents?: string;
  contentsOpacity: number;
  openness: number;
  // ... 其他Window属性
}
```

## 测试

运行测试示例：

```typescript
import { runTests } from './usage';
runTests();
```

测试包括：
- 基础序列化器测试
- Sprite序列化器测试
- 继承调用顺序验证
- Elements数组处理测试

## 注意事项

1. **环境检测**：代码会自动检测是否在RPG Maker MZ环境中运行
2. **模拟对象**：在测试环境中会创建模拟对象，保持接口一致性
3. **类型安全**：使用TypeScript确保类型安全
4. **内存管理**：只保存必要数据，避免内存泄漏
5. **插件兼容**：兼容RPGEditor_BitmapTracker和CustomResourcePath插件
