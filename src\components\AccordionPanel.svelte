<script lang="ts">
  // 组件属性
  export let title: string = '';
  export let icon: string = '';
  export let badge: string = '';
  export let badgeVariant: 'active' | 'inactive' | 'info' = 'active';
  export let expanded: boolean = true;
  export let disabled: boolean = false;

  // 切换展开状态
  function toggle() {
    if (!disabled) {
      expanded = !expanded;
    }
  }

  // 键盘事件处理
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggle();
    }
  }
</script>

<div class="accordion-panel">
  <!-- 手风琴头部 -->
  <!-- svelte-ignore a11y-no-noninteractive-tabindex -->
  <!-- svelte-ignore a11y-click-events-have-key-events -->
  <!-- svelte-ignore a11y-no-static-element-interactions -->
  <button
    class="panel-accordion-header"
    class:expanded
    class:disabled
    onclick={toggle}
    onkeydown={handleKeydown}
    {disabled}
    tabindex={disabled ? -1 : 0}
    aria-expanded={expanded}
    aria-label={`${title} 面板，${expanded ? '已展开' : '已折叠'}`}
  >
    {#if icon}
      <span class="panel-icon">{icon}</span>
    {/if}
    
    <span class="panel-title">{title}</span>
    
    {#if badge}
      <span class="panel-badge" class:active={badgeVariant === 'active'} class:inactive={badgeVariant === 'inactive'} class:info={badgeVariant === 'info'}>
        {badge}
      </span>
    {/if}
    
    <span class="panel-expand-icon" class:expanded>
      {expanded ? '▼' : '▶'}
    </span>
  </button>

  <!-- 手风琴内容 -->
  {#if expanded}
    <div class="panel-accordion-content">
      <slot />
    </div>
  {/if}
</div>

<style>
  .accordion-panel {
    border: 1px solid var(--theme-border, #e2e8f0);
    border-radius: var(--border-radius, 4px);
    overflow: hidden;
    margin-bottom: var(--spacing-2, 0.5rem);
  }

  .panel-accordion-header {
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--spacing-2, 0.5rem);
    padding: var(--spacing-2, 0.5rem) var(--spacing-3, 0.75rem);
    background: var(--theme-surface-light, #f8f9fa);
    border: none;
    cursor: pointer;
    transition: all var(--transition-base, 0.2s ease);
    font-size: var(--font-size-xs, 0.75rem);
    color: var(--theme-text, #1a202c);
    font-family: inherit;
  }

  .panel-accordion-header:hover:not(.disabled) {
    background: var(--theme-surface-hover, rgba(0, 0, 0, 0.05));
  }

  .panel-accordion-header:focus:not(.disabled) {
    outline: 2px solid var(--theme-primary, #3182ce);
    outline-offset: -2px;
  }

  .panel-accordion-header.expanded {
    background: var(--theme-primary-light, rgba(49, 130, 206, 0.1));
    border-bottom: 1px solid var(--theme-border, #e2e8f0);
  }

  .panel-accordion-header.disabled {
    cursor: not-allowed;
    opacity: 0.6;
    background: var(--theme-surface-disabled, #f1f3f4);
  }

  .panel-icon {
    font-size: var(--font-size-sm, 0.875rem);
    width: 16px;
    text-align: center;
    flex-shrink: 0;
  }

  .panel-title {
    font-weight: 600;
    flex: 1;
    text-align: left;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .panel-badge {
    font-size: var(--font-size-xs, 0.75rem);
    font-weight: 500;
    padding: var(--spacing-1, 0.25rem) var(--spacing-2, 0.5rem);
    border-radius: var(--border-radius-small, 2px);
    border: 1px solid;
    min-width: 60px;
    text-align: center;
    flex-shrink: 0;
  }

  .panel-badge.active {
    background: var(--theme-success-light, #e8f5e8);
    color: var(--theme-success-dark, #2e7d32);
    border-color: var(--theme-success, #4caf50);
  }

  .panel-badge.inactive {
    background: var(--theme-warning-light, #fff3e0);
    color: var(--theme-warning-dark, #f57c00);
    border-color: var(--theme-warning, #ff9800);
  }

  .panel-badge.info {
    background: var(--theme-surface-dark, #f7fafc);
    color: var(--theme-text-secondary, #718096);
    border-color: var(--theme-border-light, #e9ecef);
  }

  .panel-expand-icon {
    font-size: var(--font-size-xs, 0.75rem);
    color: var(--theme-text-secondary, #718096);
    transition: transform var(--transition-base, 0.2s ease);
    flex-shrink: 0;
    width: 12px;
    text-align: center;
  }

  .panel-expand-icon.expanded {
    transform: rotate(0deg);
  }

  .panel-accordion-content {
    padding: var(--spacing-1, 0.25rem);
    background: var(--theme-surface, #ffffff);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1, 0.25rem);
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .panel-accordion-header {
      padding: var(--spacing-2, 0.5rem);
      font-size: var(--font-size-xs, 0.75rem);
      gap: var(--spacing-1, 0.25rem);
    }

    .panel-badge {
      font-size: 10px;
      min-width: 50px;
      padding: 2px var(--spacing-1, 0.25rem);
    }

    .panel-accordion-content {
      padding: var(--spacing-2, 0.5rem);
    }
  }

  /* 深色主题支持 */
  @media (prefers-color-scheme: dark) {
    .accordion-panel {
      border-color: var(--theme-border-dark, rgba(255, 255, 255, 0.2));
    }

    .panel-accordion-header {
      background: var(--theme-surface-dark, #2d3748);
      color: var(--theme-text-dark, #ffffff);
    }

    .panel-accordion-header:hover:not(.disabled) {
      background: var(--theme-surface-hover-dark, rgba(255, 255, 255, 0.1));
    }

    .panel-accordion-header.expanded {
      background: var(--theme-primary-dark, rgba(66, 153, 225, 0.2));
      border-bottom-color: var(--theme-border-dark, rgba(255, 255, 255, 0.2));
    }

    .panel-accordion-content {
      background: var(--theme-surface-dark, #1a202c);
    }
  }
</style>
