/**
 * Scene对象模板代码生成器
 */

import { type SpriteData, processMultipleSpriteData } from './spritePro';
import {
  handleWindowLayer
} from './windowLayer';

// 注意：windowTemplate 已移除，不再支持窗口重写

export interface SceneTemplateData {
  sceneClassName: string;
  sprites: SpriteData[];
  containers: SpriteData[];
  tilingSprites: SpriteData[];
  others: SpriteData[];
  allSceneObjects?: any[];  // 完整的场景对象数据（包含WindowLayer）
}

/**
 * 生成Scene类的重写代码
 * @param sceneData Scene数据
 * @returns 生成的代码字符串
 */
export function generateSceneCode(sceneData: SceneTemplateData): string {
  const { sceneClassName, sprites, containers, tilingSprites, others, allSceneObjects } = sceneData;

  let code = `// === 重写 ${sceneClassName} 类 ===\n\n`;

  // 重写create方法
  code += generateSceneCreateCode(sceneClassName, sprites, containers, tilingSprites, others, allSceneObjects);

  // 不再添加特殊方法重写

  return code;
}

/**
 * 生成Scene的create方法重写
 */
function generateSceneCreateCode(
  sceneClassName: string,
  sprites: SpriteData[],
  containers: SpriteData[],
  tilingSprites: SpriteData[],
  others: SpriteData[],
  allSceneObjects?: any[]  // 完整的场景对象数据
): string {
  let code = `// 重写 ${sceneClassName}.prototype.create\n`;
  code += `${sceneClassName}.prototype.create = function() {\n`;
  code += `    Scene_Base.prototype.create.call(this);\n\n`;

  // 创建编辑器对象
  code += `    // === 编辑器对象 ===\n\n`;

  // 精灵对象
  if (sprites.length > 0) {
    code += `    // 精灵对象\n`;
    code += processMultipleSpriteData(sprites, '    ');
  }

  // 容器对象
  if (containers.length > 0) {
    code += `    // 容器对象\n`;
    code += processMultipleSpriteData(containers, '    ');
  }

  // 平铺精灵对象
  if (tilingSprites.length > 0) {
    code += `    // 平铺精灵对象\n`;
    code += processMultipleSpriteData(tilingSprites, '    ');
  }

  // 其他对象
  if (others.length > 0) {
    code += `    // 其他对象\n`;
    code += processMultipleSpriteData(others, '    ');
  }

  // === WindowLayer UI处理 ===
  if (allSceneObjects) {
    console.log(`开始处理 ${sceneClassName} 的WindowLayer UI对象`);

    // 查找 WindowLayer 对象
    const windowLayerObject = allSceneObjects.find(obj => obj.className === 'WindowLayer');

    if (windowLayerObject) {
      console.log('找到 WindowLayer 对象，调用专门的处理器');
      // 直接调用 windowLayer 文件中的方法处理
      const windowLayerCode = handleWindowLayer(windowLayerObject, '    ');
      code += windowLayerCode;
    } else {
      console.log(`${sceneClassName} 中没有 WindowLayer 对象`);
    }
  } else {
    console.log(`${sceneClassName} 没有提供完整的场景对象数据，跳过WindowLayer处理`);
  }

  code += `};\n\n`;

  // 为特定场景添加必要的方法重写
  code += generateSceneSpecificMethods(sceneClassName, allSceneObjects);

  return code;
}

/**
 * 生成场景特定的方法重写
 */
function generateSceneSpecificMethods(sceneClassName: string, allSceneObjects?: any[]): string {
  let code = '';

  // Scene_Title 特定的方法重写
  if (sceneClassName === 'Scene_Title') {
    code += `// === Scene_Title 特定方法重写 ===\n\n`;

    // 重写 adjustBackground - 处理局部变量背景精灵
    code += `// 重写 Scene_Title.prototype.adjustBackground - 处理局部变量背景精灵\n`;
    code += `Scene_Title.prototype.adjustBackground = function() {\n`;
    code += `    // 由于使用局部变量创建背景精灵，这里不需要调整背景\n`;
    code += `    // 原生方法期望this._backSprite1和this._backSprite2，但我们使用的是局部变量\n`;
    code += `    console.log('adjustBackground: 跳过背景调整，使用编辑器设置的属性');\n`;
    code += `};\n\n`;

    // 检查是否有命令窗口（从WindowLayer中检查）
    let hasCommandWindow = false;
    if (allSceneObjects) {
      const windowLayerObject = allSceneObjects.find(obj => obj.className === 'WindowLayer');
      if (windowLayerObject && windowLayerObject.children) {
        hasCommandWindow = windowLayerObject.children.some((window: any) =>
          window.className === 'Window_TitleCommand' ||
          window.referenceName === '_commandWindow'
        );
      }
    }

    if (hasCommandWindow) {
      // 如果有命令窗口，重写相关方法
      code += `// 重写 Scene_Title.prototype.update - 处理命令窗口\n`;
      code += `Scene_Title.prototype.update = function() {\n`;
      code += `    if (!this.isBusy() && this._commandWindow) {\n`;
      code += `        this._commandWindow.open();\n`;
      code += `    }\n`;
      code += `    Scene_Base.prototype.update.call(this);\n`;
      code += `};\n\n`;

      code += `// 重写 Scene_Title.prototype.isBusy - 处理命令窗口\n`;
      code += `Scene_Title.prototype.isBusy = function() {\n`;
      code += `    return (\n`;
      code += `        (this._commandWindow && this._commandWindow.isClosing()) ||\n`;
      code += `        Scene_Base.prototype.isBusy.call(this)\n`;
      code += `    );\n`;
      code += `};\n\n`;
    } else {
      // 如果没有命令窗口，提供简化的方法重写
      code += `// 重写 Scene_Title.prototype.update - 无命令窗口版本\n`;
      code += `Scene_Title.prototype.update = function() {\n`;
      code += `    Scene_Base.prototype.update.call(this);\n`;
      code += `};\n\n`;

      code += `// 重写 Scene_Title.prototype.isBusy - 无命令窗口版本\n`;
      code += `Scene_Title.prototype.isBusy = function() {\n`;
      code += `    return Scene_Base.prototype.isBusy.call(this);\n`;
      code += `};\n\n`;
    }
  }

  // 可以在这里添加其他场景的特定方法重写
  // if (sceneClassName === 'Scene_Menu') { ... }
  // if (sceneClassName === 'Scene_Battle') { ... }

  return code;
}

/**
 * 生成完整的插件代码
 * @param sceneData Scene数据
 * @param windowClassCodes Window类重写代码
 * @returns 完整的插件代码
 */
export function generateCompletePluginCode(sceneData: SceneTemplateData, windowClassCodes: string = ''): string {
  let code = `// 场景完全重写代码 - ${sceneData.sceneClassName}\n`;
  code += `// 基于编辑器对象结构生成\n`;
  code += `// 生成时间: ${new Date().toISOString()}\n\n`;

  code += `(() => {\n`;
  code += `    'use strict';\n\n`;

  // Window类重写代码
  if (windowClassCodes) {
    code += `    ${windowClassCodes.replace(/\n/g, '\n    ')}\n`;
  }

  // Scene类重写代码
  const sceneCode = generateSceneCode(sceneData);
  code += `    ${sceneCode.replace(/\n/g, '\n    ')}\n`;

  code += `})();\n`;

  return code;
}

/**
 * 过滤有意义的对象
 */
export function filterMeaningfulObjects(objects: any[]): {
  sprites: SpriteData[];
  containers: SpriteData[];
  tilingSprites: SpriteData[];
  others: SpriteData[];
} {
  console.log('=== filterMeaningfulObjects 开始执行 ===');
  console.log('输入对象数量:', objects.length);
  console.log('输入对象列表:', objects.map(obj => obj.className));

  const result = {
    sprites: [] as SpriteData[],
    containers: [] as SpriteData[],
    tilingSprites: [] as SpriteData[],
    others: [] as SpriteData[]
  };

  for (const obj of objects) {
    console.log(`处理对象: ${obj.className}`);

    // 分类所有对象，跳过Window对象和WindowLayer
    if (obj.className === 'WindowLayer') {
      console.log(`⚠️ 跳过WindowLayer对象: ${obj.className} (将由专门的WindowLayer处理器处理)`);
    } else if (obj.className.startsWith('Window_')) {
      console.log(`⚠️ 跳过Window对象: ${obj.className} (系统窗口不允许编辑)`);
    } else if (obj.className === 'TilingSprite' || obj.className === 'PIXI.TilingSprite') {
      result.tilingSprites.push(obj);
      console.log(`添加TilingSprite对象到分类: ${obj.className}`);
    } else if (obj.className.includes('Container') || obj.className === 'PIXI.Container') {
      result.containers.push(obj);
      console.log(`添加Container对象到分类: ${obj.className}`);
    } else if (obj.className.startsWith('Sprite')) {
      result.sprites.push(obj);
      console.log(`添加Sprite对象到分类: ${obj.className}`);
    } else {
      result.others.push(obj);
      console.log(`添加其他对象到分类: ${obj.className}`);
    }
  }

  console.log('=== filterMeaningfulObjects 执行完成 ===');
  console.log('分类结果:', {
    sprites: result.sprites.length,
    containers: result.containers.length,
    tilingSprites: result.tilingSprites.length,
    others: result.others.length
  });

  return result;
}

// 注意：旧的系统窗口创建代码已移除，现在统一通过 WindowLayer 处理

/**
 * 创建包含WindowLayer数据的SceneTemplateData
 * @param sceneClassName 场景类名
 * @param allSceneObjects 完整的场景对象数据
 * @returns SceneTemplateData
 */
export function createSceneTemplateWithWindowLayer(
  sceneClassName: string,
  allSceneObjects: any[]
): SceneTemplateData {
  console.log(`=== 创建包含WindowLayer的SceneTemplateData: ${sceneClassName} ===`);

  // 过滤出非WindowLayer对象
  const nonWindowLayerObjects = allSceneObjects.filter(obj => obj.className !== 'WindowLayer');

  // 使用现有的过滤逻辑
  const { sprites, containers, tilingSprites, others } = filterMeaningfulObjects(nonWindowLayerObjects);

  // 检查WindowLayer对象
  const windowLayerObject = allSceneObjects.find(obj => obj.className === 'WindowLayer');
  const windowLayerChildrenCount = windowLayerObject?.children?.length || 0;

  console.log(`场景对象统计:`, {
    sprites: sprites.length,
    containers: containers.length,
    tilingSprites: tilingSprites.length,
    others: others.length,
    windowLayerChildren: windowLayerChildrenCount
  });

  return {
    sceneClassName,
    sprites,
    containers,
    tilingSprites,
    others,
    allSceneObjects // 传递完整的场景对象数据
  };
}




