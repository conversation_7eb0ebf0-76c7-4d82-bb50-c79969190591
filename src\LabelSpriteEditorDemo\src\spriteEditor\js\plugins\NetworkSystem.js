//=============================================================================
// RPG Maker MZ - Network System
//=============================================================================

/*:
 * @target MZ
 * @plugindesc WebSocket网络系统 - 实现多人在线游戏功能
 * <AUTHOR> AI
 *
 * @param serverUrl
 * @text 服务器地址
 * @type string
 * @default ws://localhost:3000
 * @desc WebSocket服务器的地址
 *
 * @param autoConnect
 * @text 自动连接
 * @type boolean
 * @default true
 * @desc 是否在游戏启动时自动连接到服务器
 *
 * @param reconnectInterval
 * @text 重连间隔
 * @type number
 * @min 1000
 * @default 5000
 * @desc 断线重连的时间间隔（毫秒）
 *
 * @command connect
 * @text 连接服务器
 * @desc 连接到WebSocket服务器
 *
 * @command disconnect
 * @text 断开连接
 * @desc 断开与服务器的连接
 *
 * @command sendMessage
 * @text 发送消息
 * @desc 向服务器发送消息
 *
 * @arg type
 * @type string
 * @text 消息类型
 * @desc 要发送的消息类型
 *
 * @arg data
 * @type string
 * @text 消息数据
 * @desc 要发送的消息数据(JSON格式)
 *
 * @help NetworkSystem.js
 *
 * 这个插件提供WebSocket网络功能，用于实现多人在线游戏：
 * 1. 自动连接到WebSocket服务器
 * 2. 事件派发系统
 * 3. 处理断线重连
 * 4. 提供统一的消息发送接口
 */

(() => {
  const pluginName = "NetworkSystem";
  const parameters = PluginManager.parameters(pluginName);
  const serverUrl = String(parameters.serverUrl || "ws://localhost:3000");
  const autoConnect = parameters.autoConnect !== "false";
  const reconnectInterval = Number(parameters.reconnectInterval || 5000);

  let ws = null;
  let isConnecting = false;
  let reconnectTimer = null;

  // 网络系统管理器
  window.NetworkManager = class NetworkManager {
    static initialize() {
      this._isConnected = false;
      this._eventListeners = new Map();
      this._oneTimeCallbacks = new Map(); // 存储一次性回调函数
      if (autoConnect) {
        this.connect();
      }
    }

    static connect() {
      if (isConnecting || this._isConnected) return;
      isConnecting = true;

      try {
        ws = new WebSocket(serverUrl);

        ws.onopen = () => {
          console.log("WebSocket连接成功");
          this._isConnected = true;
          isConnecting = false;
          // NetworkManager.sendMessage(1, {
          //   userId: "000",
          //   pwd: "111",
          //   email: "222",
          // });
          // console.log("NetworkSystem initialized");
          // NetworkManager.sendMessage("register", {
          //   data: "register666",
          //   id: "123456",
          // });
          if (reconnectTimer) {
            clearInterval(reconnectTimer);
            reconnectTimer = null;
          }
        };

        ws.onclose = () => {
          console.log("WebSocket连接断开");
          this._isConnected = false;
          isConnecting = false;
          this._startReconnect();
        };

        ws.onerror = (error) => {
          console.error("WebSocket错误:", error);
          isConnecting = false;
        };

        ws.onmessage = (event) => {
          this._handleMessage(JSON.parse(event.data));
        };
      } catch (error) {
        console.error("WebSocket连接失败:", error);
        isConnecting = false;
        this._startReconnect();
      }
    }

    static disconnect() {
      if (ws) {
        ws.close();
        ws = null;
      }
      this._isConnected = false;
      if (reconnectTimer) {
        clearInterval(reconnectTimer);
        reconnectTimer = null;
      }
    }

    static _startReconnect() {
      if (!reconnectTimer) {
        reconnectTimer = setInterval(() => {
          if (!this._isConnected && !isConnecting) {
            this.connect();
          }
        }, reconnectInterval);
      }
    }

    static _handleMessage(data1) {
      const { type, data } = data1;
      // 处理一次性回调
      if (this._oneTimeCallbacks.has(type)) {
        const callback = this._oneTimeCallbacks.get(type);
        callback(JSON.parse(data));
        this._oneTimeCallbacks.delete(type); // 执行后立即删除回调
      }
      // 处理常规事件监听器
      if (this._eventListeners.has(type)) {
        const callbacks = this._eventListeners.get(type);
        callbacks.forEach((callback) => callback(JSON.parse(data)));
      }
    }

    static addEventListener(type, callback) {
      if (!this._eventListeners.has(type)) {
        this._eventListeners.set(type, new Set());
      }
      this._eventListeners.get(type).add(callback);
    }

    static removeEventListener(type, callback) {
      if (this._eventListeners.has(type)) {
        this._eventListeners.get(type).delete(callback);
      }
    }

    static sendMessage(type, data, callback) {
      if (!this._isConnected || !ws) return;

      // 如果提供了回调函数，将其注册为一次性回调
      if (callback && typeof callback === "function") {
        this._oneTimeCallbacks.set(type, callback);
      }

      ws.send(JSON.stringify({ type: type, data: data }));
    }
  };

  // 注册插件命令
  PluginManager.registerCommand(pluginName, "connect", () => {
    NetworkManager.connect();
  });

  PluginManager.registerCommand(pluginName, "disconnect", () => {
    NetworkManager.disconnect();
  });

  PluginManager.registerCommand(pluginName, "sendMessage", (args) => {
    const type = args.type;
    const data = JSON.parse(args.data || "{}");
    NetworkManager.sendMessage(type, data);
  });

  // 初始化网络系统
  const _Scene_Boot_start = Scene_Boot.prototype.start;
  Scene_Boot.prototype.start = function () {
    _Scene_Boot_start.call(this);
    NetworkManager.initialize();
  };
})();
