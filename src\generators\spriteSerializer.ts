import { BaseObjectSerializer } from './baseSerializer';
import { type spriteProperties, type bitmapProperties } from './type';
import { processElements } from '../operationNote/spritePro/elements';
import { processUrl } from '../operationNote/spritePro/url';

/**
 * Sprite对象序列化器
 * 继承基类序列化器，添加Sprite特有属性处理
 */
export class SpriteObjectSerializer extends BaseObjectSerializer {

  /**
   * 序列化Sprite对象为spriteProperties
   * 先调用父类方法获取基础属性，再处理sprite特有属性
   */
  serialize(obj: any): spriteProperties {
    // 先调用父类方法获取基础属性
    const baseData = super.serialize(obj);

    // 再处理sprite特有属性
    return {
      ...baseData,
      blendMode: obj.blendMode || 0,
      // mask: obj.mask, // 复杂对象，暂时直接保存
      // filters: obj.filters, // 复杂对象，暂时直接保存
      zIndex: obj.zIndex || 0,
      bitmap: this.serializeBitmap(obj.bitmap)
    };
  }

  /**
   * 反序列化spriteProperties为Sprite对象
   * 先调用父类方法创建基础对象，再设置sprite特有属性
   */
  deserialize(data: spriteProperties): any {
    // 先调用父类方法创建基础对象
    const obj = super.deserialize(data);

    // 再设置sprite特有属性
    obj.blendMode = data.blendMode;
    // obj.mask = data.mask;
    // obj.filters = data.filters;
    obj.zIndex = data.zIndex || 0;

    // 处理bitmap
    obj.bitmap = this.deserializeBitmap(data.bitmap);

    return obj;
  }

  /**
   * 序列化Bitmap对象 - 只提取必要属性
   * 参考spritePro的extractBitmapInfo方法
   */
  protected serializeBitmap(bitmap: any): bitmapProperties {
    if (!bitmap) {
      return this.getDefaultBitmapProperties();
    }

    // 提取URL信息（参考spritePro的处理方式）
    const url = this.extractBitmapUrl(bitmap);

    return {
      fontBold: bitmap.fontBold || false,
      fontFace: bitmap.fontFace || 'GameFont',
      fontItalic: bitmap.fontItalic || false,
      fontSize: bitmap.fontSize || 28,
      outlineColor: bitmap.outlineColor || 'rgba(0, 0, 0, 0.5)',
      outlineWidth: bitmap.outlineWidth || 4,
      textColor: bitmap.textColor || '#ffffff',
      _paintOpacity: bitmap._paintOpacity || 255,
      _smooth: bitmap._smooth !== undefined ? bitmap._smooth : true,
      elements: bitmap.elements, // RPGEditor_BitmapTracker插件的elements数组
      url: url // 只保存URL字符串，不保存整个bitmap对象
    };
  }

  /**
   * 提取位图URL信息（参考operationNote/objectReconstructor.ts的extractBitmapInfo方法）
   */
  protected extractBitmapUrl(bitmap: any): string | undefined {
    if (!bitmap) return undefined;

    // 优先使用原始路径（CustomResourcePath 插件保存的）
    if (bitmap._originalPath) {
      return bitmap._originalPath;
    }

    // 如果是 Bitmap 对象，尝试获取 URL
    if (bitmap._url) {
      return bitmap._url;
    }

    // 如果是字符串路径
    if (typeof bitmap === 'string') {
      return bitmap;
    }

    // 其他情况返回 undefined
    return undefined;
  }

  /**
   * 反序列化Bitmap对象 - 创建真正的RPG Maker MZ Bitmap对象
   * 参考spritePro的处理方式
   */
  protected deserializeBitmap(data: bitmapProperties): any {
    if (!data) {
      return null;
    }

    let bitmap: any = null;

    // 根据数据类型创建相应的Bitmap对象
    if (data.elements && Array.isArray(data.elements)) {
      // RPGEditor_BitmapTracker插件的elements数组处理
      bitmap = this.createBitmapWithElements(data);
    } else if (data.url) {
      // URL图片处理
      bitmap = this.createBitmapFromUrl(data.url);
    } else {
      // 创建空白bitmap
      bitmap = this.createEmptyBitmap();
    }

    // 设置bitmap文字属性
    if (bitmap) {
      bitmap.fontBold = data.fontBold;
      bitmap.fontFace = data.fontFace;
      bitmap.fontItalic = data.fontItalic;
      bitmap.fontSize = data.fontSize;
      bitmap.outlineColor = data.outlineColor;
      bitmap.outlineWidth = data.outlineWidth;
      bitmap.textColor = data.textColor;
      bitmap._paintOpacity = data._paintOpacity;
      bitmap._smooth = data._smooth;

      // 保存elements数组（如果存在）
      if (data.elements) {
        bitmap.elements = data.elements;
      }
    }

    return bitmap;
  }

  /**
   * 创建带有elements的Bitmap对象（RPGEditor_BitmapTracker插件）
   * 参考spritePro/elements.ts的处理方式
   */
  protected createBitmapWithElements(data: bitmapProperties): any {
    // 在RPG Maker MZ环境中创建真正的Bitmap对象
    if (typeof window !== 'undefined' && (window as any).Bitmap && (window as any).Graphics) {
      const bitmap = new (window as any).Bitmap((window as any).Graphics.width, (window as any).Graphics.height);
      bitmap.elements = data.elements;
      return bitmap;
    }

    // 模拟环境下返回模拟对象
    return this.createMockBitmapWithElements(data.elements || []);
  }

  /**
   * 创建从URL加载的Bitmap对象
   * 参考spritePro/url.ts的处理方式
   */
  protected createBitmapFromUrl(url: string): any {
    // 在RPG Maker MZ环境中使用ImageManager加载
    if (typeof window !== 'undefined' && (window as any).ImageManager) {
      const { folder, filename } = this.parseBitmapPath(url);

      if (folder && filename) {
        // 使用 ImageManager.loadBitmap 方法
        return (window as any).ImageManager.loadBitmap(folder, filename);
      } else {
        // 使用 ImageManager.loadBitmapFromUrl 方法
        return (window as any).ImageManager.loadBitmapFromUrl(url);
      }
    }

    // 模拟环境下返回模拟对象
    return this.createMockBitmapFromUrl(url);
  }

  /**
   * 创建空白Bitmap对象
   */
  protected createEmptyBitmap(): any {
    // 在RPG Maker MZ环境中创建真正的Bitmap对象
    if (typeof window !== 'undefined' && (window as any).Bitmap && (window as any).Graphics) {
      return new (window as any).Bitmap((window as any).Graphics.width, (window as any).Graphics.height);
    }

    // 模拟环境下返回模拟对象
    return this.createMockEmptyBitmap();
  }

  /**
   * 解析位图路径，分离文件夹和文件名
   * 参考spritePro/url.ts的parseBitmapPath方法
   */
  protected parseBitmapPath(bitmapPath: string): { folder: string; filename: string } {
    // 移除 .png 扩展名
    const pathWithoutExt = bitmapPath.replace(/\.png$/i, '');

    // 查找最后一个斜杠
    const lastSlashIndex = pathWithoutExt.lastIndexOf('/');

    if (lastSlashIndex === -1) {
      // 没有斜杠，整个路径就是文件名
      return { folder: '', filename: pathWithoutExt };
    }

    // 分离文件夹和文件名
    const folder = pathWithoutExt.substring(0, lastSlashIndex + 1); // 包含最后的斜杠
    const filename = pathWithoutExt.substring(lastSlashIndex + 1);

    return { folder, filename };
  }

  // 模拟对象创建方法（用于测试环境）
  protected createMockBitmapWithElements(elements: any[]): any {
    return {
      elements: elements,
      fontBold: false,
      fontFace: 'GameFont',
      fontItalic: false,
      fontSize: 28,
      outlineColor: 'rgba(0, 0, 0, 0.5)',
      outlineWidth: 4,
      textColor: '#ffffff',
      _paintOpacity: 255,
      _smooth: true,
      width: 816,
      height: 624
    };
  }

  protected createMockBitmapFromUrl(url: string): any {
    return {
      _url: url,
      fontBold: false,
      fontFace: 'GameFont',
      fontItalic: false,
      fontSize: 28,
      outlineColor: 'rgba(0, 0, 0, 0.5)',
      outlineWidth: 4,
      textColor: '#ffffff',
      _paintOpacity: 255,
      _smooth: true,
      width: 816,
      height: 624
    };
  }

  protected createMockEmptyBitmap(): any {
    return {
      fontBold: false,
      fontFace: 'GameFont',
      fontItalic: false,
      fontSize: 28,
      outlineColor: 'rgba(0, 0, 0, 0.5)',
      outlineWidth: 4,
      textColor: '#ffffff',
      _paintOpacity: 255,
      _smooth: true,
      width: 816,
      height: 624
    };
  }

  /**
   * 获取默认Bitmap属性
   */
  protected getDefaultBitmapProperties(): bitmapProperties {
    return {
      fontBold: false,
      fontFace: 'GameFont',
      fontItalic: false,
      fontSize: 28,
      outlineColor: 'rgba(0, 0, 0, 0.5)',
      outlineWidth: 4,
      textColor: '#ffffff',
      _paintOpacity: 255,
      _smooth: true
    };
  }

  // ===== 代码生成方法 =====

  /**
   * 生成Sprite对象的完整创建代码
   * @param path 对象路径
   * @param properties Sprite属性
   * @param indent 缩进字符串
   * @returns 生成的代码字符串
   */
  generateObjectCode(path: string, properties: spriteProperties, indent: string): string {
    const varName = `obj_${path.replace(/\./g, '_')}`;
    const codes: string[] = [];

    // 1. 对象创建
    codes.push(`${indent}const ${varName} = new Sprite();`);

    // 2. 基础属性设置
    codes.push(this.generateBasicProperties(varName, properties, indent));

    // 3. Sprite特有属性设置
    codes.push(this.generateSpriteProperties(varName, properties, indent));

    // 4. Bitmap处理
    if (properties.bitmap) {
      codes.push(this.generateBitmapCode(varName, properties.bitmap, indent));
    }

    // 5. 添加到父容器（传入路径用于判断父对象）
    codes.push(this.generateAddToParent(varName, path, indent));

    return codes.filter(code => code.trim()).join('\n');
  }

  /**
   * 生成基础属性设置代码
   */
  protected generateBasicProperties(varName: string, properties: spriteProperties, indent: string): string {
    const codes: string[] = [];

    // 位置属性
    if (properties.x !== undefined) codes.push(`${indent}${varName}.x = ${properties.x};`);
    if (properties.y !== undefined) codes.push(`${indent}${varName}.y = ${properties.y};`);

    // 尺寸属性
    if (properties.width !== undefined) codes.push(`${indent}${varName}.width = ${properties.width};`);
    if (properties.height !== undefined) codes.push(`${indent}${varName}.height = ${properties.height};`);

    // 锚点属性
    if (properties.anchorX !== undefined) codes.push(`${indent}${varName}.anchor.x = ${properties.anchorX};`);
    if (properties.anchorY !== undefined) codes.push(`${indent}${varName}.anchor.y = ${properties.anchorY};`);

    // 缩放属性
    if (properties.scaleX !== undefined) codes.push(`${indent}${varName}.scale.x = ${properties.scaleX};`);
    if (properties.scaleY !== undefined) codes.push(`${indent}${varName}.scale.y = ${properties.scaleY};`);

    // 其他属性
    if (properties.alpha !== undefined) codes.push(`${indent}${varName}.alpha = ${properties.alpha};`);
    if (properties.visible !== undefined) codes.push(`${indent}${varName}.visible = ${properties.visible};`);
    if (properties.rotation !== undefined) codes.push(`${indent}${varName}.rotation = ${properties.rotation};`);

    return codes.join('\n');
  }

  /**
   * 生成Sprite特有属性设置代码
   */
  protected generateSpriteProperties(varName: string, properties: spriteProperties, indent: string): string {
    const codes: string[] = [];

    // Sprite特有属性
    if (properties.blendMode !== undefined) codes.push(`${indent}${varName}.blendMode = ${properties.blendMode};`);
    if (properties.zIndex !== undefined) codes.push(`${indent}${varName}.zIndex = ${properties.zIndex};`);

    return codes.join('\n');
  }

  /**
   * 生成bitmap相关代码
   * 使用spritePro的实现
   */
  protected generateBitmapCode(varName: string, bitmap: bitmapProperties, indent: string): string {
    if (!bitmap) return '';

    // 情况1：如果有URL，使用spritePro的processUrl
    if (bitmap.url) {
      return processUrl(bitmap.url, varName, indent);
    }

    // 情况2：如果有elements数组，使用spritePro的processElements
    if (bitmap.elements && Array.isArray(bitmap.elements)) {
      // 构造properties对象，包含bitmap的所有属性
      const properties = {
        ...bitmap,
        elements: bitmap.elements
      };
      return processElements(bitmap.elements, properties, varName, indent);
    }

    return '';
  }

  /**
   * 生成添加到父容器的代码
   * 根据路径判断添加到哪个父对象
   */
  protected generateAddToParent(varName: string, path: string, indent: string): string {
    // 解析路径，确定父对象
    const pathParts = path.split('.');

    if (pathParts.length === 1) {
      // 根级对象，添加到 this
      return `${indent}this.addChild(${varName});`;
    } else {
      // 嵌套对象，添加到父对象
      const parentPath = pathParts.slice(0, -1).join('_');
      const parentVarName = `obj_${parentPath}`;
      return `${indent}${parentVarName}.addChild(${varName});`;
    }
  }
}
