import { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid
} from '@mui/material';
import TextFieldsIcon from '@mui/icons-material/TextFields';

const AddTextButton = ({ onAddText }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [textData, setTextData] = useState({
    text: '',
    x: 100,
    y: 100,
    maxWidth: 200,
    lineHeight: 24,
    align: 'left'
  });

  // 打开添加文本的模态框
  const openModal = () => {
    setIsModalOpen(true);
  };

  // 关闭模态框
  const closeModal = () => {
    setIsModalOpen(false);
  };

  // 处理表单输入变化
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setTextData({
      ...textData,
      [name]: name === 'text' ? value : Number(value)
    });
  };

  // 处理对齐方式变化
  const handleAlignChange = (e) => {
    setTextData({
      ...textData,
      align: e.target.value
    });
  };

  // 处理表单提交
  const handleSubmit = (e) => {
    e.preventDefault();
    onAddText(textData);
    closeModal();
    // 重置表单
    setTextData({
      text: '',
      x: 100,
      y: 100,
      maxWidth: 200,
      lineHeight: 24,
      align: 'left'
    });
  };

  return (
    <>
      <Button
        variant="contained"
        color="primary"
        onClick={openModal}
        startIcon={<TextFieldsIcon />}
        size="small"
        sx={{ mr: 0.5, fontSize: '0.8rem', py: 0.5 }}
      >
        添加文字
      </Button>

      <Dialog open={isModalOpen} onClose={closeModal} maxWidth="sm" fullWidth>
        <DialogTitle>添加文字</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="文字内容"
                name="text"
                value={textData.text}
                onChange={handleInputChange}
                required
                variant="outlined"
                size="small"
              />
            </Grid>

            <Grid item xs={6}>
              <TextField
                fullWidth
                label="X坐标"
                name="x"
                type="number"
                value={textData.x}
                onChange={handleInputChange}
                required
                variant="outlined"
                size="small"
              />
            </Grid>

            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Y坐标"
                name="y"
                type="number"
                value={textData.y}
                onChange={handleInputChange}
                required
                variant="outlined"
                size="small"
              />
            </Grid>

            <Grid item xs={6}>
              <TextField
                fullWidth
                label="最大宽度"
                name="maxWidth"
                type="number"
                value={textData.maxWidth}
                onChange={handleInputChange}
                required
                variant="outlined"
                size="small"
              />
            </Grid>

            <Grid item xs={6}>
              <TextField
                fullWidth
                label="行高"
                name="lineHeight"
                type="number"
                value={textData.lineHeight}
                onChange={handleInputChange}
                required
                variant="outlined"
                size="small"
              />
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth size="small">
                <InputLabel id="align-label">对齐方式</InputLabel>
                <Select
                  labelId="align-label"
                  id="align"
                  name="align"
                  value={textData.align}
                  onChange={handleAlignChange}
                  label="对齐方式"
                >
                  <MenuItem value="left">左对齐</MenuItem>
                  <MenuItem value="center">居中</MenuItem>
                  <MenuItem value="right">右对齐</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeModal} color="inherit">
            取消
          </Button>
          <Button onClick={handleSubmit} color="primary" variant="contained">
            添加
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

AddTextButton.propTypes = {
  onAddText: PropTypes.func.isRequired
};

export default AddTextButton;
