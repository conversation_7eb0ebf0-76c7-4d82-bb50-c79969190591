/**
 * WindowLayer UI管理器
 * 专门处理UI层的数据保存和代码生成
 */

import { generateSystemWindowCode } from './systemWindowTemplate';
import { type SpriteData, processMultipleSpriteData, processSpriteData } from './spritePro';

/**
 * WindowLayer中的对象信息
 */
export interface WindowLayerObjectInfo {
  className: string;
  referenceName?: string;
  properties: Record<string, any>;
  constructorParams?: any;
  children: WindowLayerObjectInfo[];
  isSystemWindow: boolean;  // 是否为系统窗口
}

/**
 * WindowLayer处理结果
 */
export interface WindowLayerResult {
  code: string;
  hasSystemWindows: boolean;
  hasUIObjects: boolean;
  systemWindowCount: number;
  uiObjectCount: number;
}

/**
 * 处理WindowLayer对象，生成UI创建代码
 * 这是主入口函数，当遍历对象树遇到WindowLayer时调用
 * @param windowLayerData WindowLayer对象的完整数据
 * @param indent 缩进字符串
 * @returns 处理结果
 */
export function processWindowLayer(
  windowLayerData: any,
  indent: string = '    '
): WindowLayerResult {
  console.log('=== 开始处理WindowLayer对象 ===');
  console.log('WindowLayer数据:', windowLayerData);

  let code = '';
  let systemWindowCount = 0;
  let uiObjectCount = 0;

  // 确保WindowLayer存在
  code += `${indent}// === WindowLayer UI对象创建 ===\n`;
  code += `${indent}// 确保WindowLayer存在\n`;
  code += `${indent}if (!this._windowLayer) {\n`;
  code += `${indent}    this.createWindowLayer();\n`;
  code += `${indent}}\n\n`;

  // 处理WindowLayer的子对象
  if (windowLayerData.children && Array.isArray(windowLayerData.children)) {
    console.log(`WindowLayer包含 ${windowLayerData.children.length} 个子对象`);

    for (const child of windowLayerData.children) {
      console.log(`处理子对象: ${child.className}`);

      if (isSystemWindow(child.className)) {
        // 系统窗口
        console.log(`识别为系统窗口: ${child.className}`);
        code += generateSingleSystemWindowCode(child, indent);
        systemWindowCount++;
      } else {
        // 其他UI对象
        console.log(`识别为UI对象: ${child.className}`);
        code += generateSingleUIObjectCode(child, indent);
        uiObjectCount++;
      }
    }
  } else {
    console.log('WindowLayer没有子对象');
  }

  const result: WindowLayerResult = {
    code,
    hasSystemWindows: systemWindowCount > 0,
    hasUIObjects: uiObjectCount > 0,
    systemWindowCount,
    uiObjectCount
  };

  console.log('=== WindowLayer处理完成 ===');
  console.log(`处理结果: 系统窗口 ${systemWindowCount} 个, UI对象 ${uiObjectCount} 个`);
  console.log('生成的代码长度:', code.length);

  return result;
}

/**
 * 分离系统窗口和UI对象
 */
function separateWindowLayerObjects(objects: WindowLayerObjectInfo[]): {
  systemWindows: WindowLayerObjectInfo[],
  uiObjects: WindowLayerObjectInfo[]
} {
  const systemWindows: WindowLayerObjectInfo[] = [];
  const uiObjects: WindowLayerObjectInfo[] = [];

  for (const obj of objects) {
    if (obj.isSystemWindow || obj.className.startsWith('Window_')) {
      systemWindows.push(obj);
      console.log(`识别为系统窗口: ${obj.className}`);
    } else {
      uiObjects.push(obj);
      console.log(`识别为UI对象: ${obj.className}`);
    }
  }

  return { systemWindows, uiObjects };
}

/**
 * 生成系统窗口创建代码
 */
function generateSystemWindowsCode(
  systemWindows: WindowLayerObjectInfo[],
  indent: string
): string {
  let code = `${indent}// === 系统窗口创建 ===\n`;

  for (const window of systemWindows) {
    console.log(`生成系统窗口代码: ${window.className}`);

    // 调用systemWindowTemplate中的方法
    const windowCode = generateSystemWindowCode(window.className, indent);
    code += windowCode;

    // 如果有引用名称，添加引用赋值
    if (window.referenceName) {
      code += `${indent}// 保存窗口引用\n`;
      code += `${indent}this.${window.referenceName} = this._windowLayer.children[this._windowLayer.children.length - 1];\n`;
    }

    code += '\n';
  }

  return code;
}

/**
 * 生成UI对象创建代码
 */
function generateUIObjectsCode(
  uiObjects: WindowLayerObjectInfo[],
  indent: string
): string {
  let code = `${indent}// === UI对象创建 ===\n`;

  // 转换为SpriteData格式
  const spriteData: SpriteData[] = uiObjects.map(obj => ({
    className: obj.className,
    referenceName: obj.referenceName,
    properties: obj.properties,
    constructorParams: obj.constructorParams,
    children: []  // UI对象通常不需要处理子对象
  }));

  // 使用新的SpritePro生成代码，指定 WindowLayer 上下文
  const uiCode = processMultipleSpriteData(spriteData, indent, 'windowLayer');
  code += uiCode;

  // 注意：不需要额外的添加代码，因为 SpritePro 已经在 windowLayer 上下文中生成了正确的添加代码

  return code;
}

/**
 * 从场景对象中提取WindowLayer信息
 * @param sceneObjects 场景中的所有对象
 * @returns WindowLayer对象信息
 */
export function extractWindowLayerObjects(sceneObjects: any[]): WindowLayerObjectInfo[] {
  console.log('=== 提取WindowLayer对象 ===');

  const windowLayerObjects: WindowLayerObjectInfo[] = [];

  // 查找WindowLayer对象
  const windowLayer = sceneObjects.find(obj => obj.className === 'WindowLayer');

  if (!windowLayer) {
    console.log('未找到WindowLayer对象');
    return windowLayerObjects;
  }

  console.log('找到WindowLayer对象，子对象数量:', windowLayer.children?.length || 0);

  // 处理WindowLayer的子对象
  if (windowLayer.children && Array.isArray(windowLayer.children)) {
    for (const child of windowLayer.children) {
      const objInfo: WindowLayerObjectInfo = {
        className: child.className,
        referenceName: child.referenceName,
        properties: child.properties || {},
        constructorParams: child.constructorParams,
        children: child.children || [],
        isSystemWindow: isSystemWindow(child.className)
      };

      windowLayerObjects.push(objInfo);
      console.log(`添加WindowLayer子对象: ${objInfo.className} (系统窗口: ${objInfo.isSystemWindow})`);
    }
  }

  console.log(`WindowLayer对象提取完成，共 ${windowLayerObjects.length} 个对象`);
  return windowLayerObjects;
}

/**
 * 判断是否为系统窗口
 */
function isSystemWindow(className: string): boolean {
  const systemWindowTypes = [
    'Window_TitleCommand',
    'Window_MenuCommand',
    'Window_Gold',
    'Window_MenuStatus',
    'Window_Help',
    'Window_ItemCategory',
    'Window_ItemList',
    'Window_SkillType',
    'Window_SkillList',
    'Window_SkillStatus',
    'Window_Status',
    'Window_Options',
    'Window_SavefileList',
    'Window_Message',
    'Window_ScrollText',
    'Window_MapName',
    'Window_ChoiceList',
    'Window_NumberInput',
    'Window_EventItem',
    'Window_NameBox'
  ];

  return systemWindowTypes.includes(className) || className.startsWith('Window_');
}

/**
 * 生成WindowLayer相关的完整代码（兼容旧接口）
 * @param windowLayerObjects WindowLayer中的对象
 * @param indent 缩进
 * @returns 完整的WindowLayer代码
 */
export function generateWindowLayerCode(
  windowLayerObjects: WindowLayerObjectInfo[],
  indent: string = '    '
): string {
  // 这是兼容旧接口的函数，实际应该使用 processWindowLayer
  console.warn('使用了兼容接口 generateWindowLayerCode，建议使用 processWindowLayer');

  // 模拟WindowLayer数据结构
  const windowLayerData = {
    className: 'WindowLayer',
    children: windowLayerObjects
  };

  return processWindowLayer(windowLayerData, indent).code;
}

/**
 * 主要的WindowLayer处理函数（推荐使用）
 * 当遍历对象树遇到WindowLayer时，直接调用这个函数
 * @param windowLayerData WindowLayer对象的完整数据
 * @param indent 缩进
 * @returns 生成的代码字符串
 */
export function handleWindowLayer(
  windowLayerData: any,
  indent: string = '    '
): string {
  const result = processWindowLayer(windowLayerData, indent);
  return result.code;
}

/**
 * 生成单个系统窗口的创建代码
 * @param windowData 窗口对象数据
 * @param indent 缩进
 * @returns 生成的代码
 */
function generateSingleSystemWindowCode(windowData: any, indent: string): string {
  console.log(`生成系统窗口代码: ${windowData.className}`);

  // 调用systemWindowTemplate中的方法
  const windowCode = generateSystemWindowCode(windowData.className, indent);

  // 如果有引用名称，添加引用赋值
  let code = windowCode;
  if (windowData.referenceName) {
    code += `${indent}// 保存窗口引用\n`;
    code += `${indent}this.${windowData.referenceName} = this._windowLayer.children[this._windowLayer.children.length - 1];\n\n`;
  }

  return code;
}

/**
 * 生成单个UI对象的创建代码
 * @param objectData 对象数据
 * @param indent 缩进
 * @returns 生成的代码
 */
function generateSingleUIObjectCode(objectData: any, indent: string): string {
  console.log(`生成UI对象代码: ${objectData.className}`);

  // 使用新的 SpritePro 处理器
  const spriteData = {
    className: objectData.className,
    referenceName: objectData.referenceName,
    properties: objectData.properties || {},
    constructorParams: objectData.constructorParams,
    children: objectData.children || []
  };

  // 调用 SpritePro 处理器生成代码，指定 WindowLayer 上下文
  const code = processSpriteData(spriteData, indent, 'windowLayer');

  return code;
}

// ===== 使用示例 =====
/*
// 在遍历对象树时，当遇到WindowLayer对象时：

if (objectData.className === 'WindowLayer') {
  // 直接调用WindowLayer处理函数
  const windowLayerCode = handleWindowLayer(objectData, '    ');

  // windowLayerCode 包含完整的WindowLayer处理代码：
  // 1. 确保WindowLayer存在
  // 2. 遍历子对象
  // 3. 系统窗口 -> 调用systemWindowTemplate
  // 4. 其他对象 -> 创建并添加到WindowLayer

  return windowLayerCode;
}

// 示例WindowLayer数据结构：
const windowLayerData = {
  className: 'WindowLayer',
  referenceName: '_windowLayer',
  properties: { x: 4, y: 4 },
  children: [
    {
      className: 'Window_TitleCommand',
      referenceName: '_commandWindow',
      properties: { ... }
    },
    {
      className: 'Sprite',
      referenceName: '_uiSprite',
      properties: { x: 100, y: 50 }
    }
  ]
};

// 调用处理函数
const code = handleWindowLayer(windowLayerData);
console.log(code);
// 输出：
// // === WindowLayer UI对象创建 ===
// // 确保WindowLayer存在
// if (!this._windowLayer) {
//     this.createWindowLayer();
// }
//
// // 创建标题命令窗口（调用系统方法）
// this.createCommandWindow();
// // 保存窗口引用
// this._commandWindow = this._windowLayer.children[this._windowLayer.children.length - 1];
//
// // 创建 Sprite
// const _uiSprite = new Sprite();
// _uiSprite.x = 100;
// _uiSprite.y = 50;
// this.addWindow(_uiSprite);
*/
