<script lang="ts">
  import { globalObjectState, triggerUpdate } from '../../../stores/objectState';
  import AccordionPanel from '../../../components/AccordionPanel.svelte';
  import { LabelSpriteEditorModal } from '../../../LabelSpriteEditor';
  import type { bitmapProperties } from '../../../generators/type';

  // 监听选中的对象数组，默认显示第一个元素
  let selectedObjectArray = $derived($globalObjectState.selectedObject);
  let selectedObject = $derived(selectedObjectArray?.[0] || null);
  let selectedObjectType = $derived($globalObjectState.selectedObjectType?.[0] || null);

  // 判断是否为sprite对象且有elements
  let isSprite = $derived(selectedObjectType === 'Sprite');
  let spriteData = $derived(isSprite ? selectedObject as any : null);
  let hasElements = $derived(() => {
    if (!spriteData) return false;

    // 检查 bitmap 或 _bitmap 字段
    const bitmap = spriteData.bitmap || spriteData._bitmap;
    if (!bitmap?.elements) return false;

    return Array.isArray(bitmap.elements) && bitmap.elements.length > 0;
  });

  // 手风琴展开状态
  let isExpanded = $state(true);

  // 获取elements中的文本元素
  let textElements = $derived(() => {
    if (!hasElements() || !spriteData) return [];

    // 检查 bitmap 或 _bitmap 字段
    const bitmap = spriteData.bitmap || spriteData._bitmap;
    if (!bitmap?.elements) return [];

    return bitmap.elements.filter((el: any) => el.type === 'text');
  });

  // 编辑器状态
  let isEditorOpen = $state(false);
  let currentBitmapData = $state<bitmapProperties | null>(null);

  // 获取舞台大小
  let stageSize = $derived(() => {
    // 尝试从全局Graphics对象获取舞台大小
    if (typeof window !== 'undefined' && window.Graphics) {
      return {
        width: window.Graphics.width || 816,
        height: window.Graphics.height || 624
      };
    }
    // 默认大小
    return { width: 816, height: 624 };
  });

  // 获取bitmap数据
  function getBitmapData(): bitmapProperties | null {
    if (!spriteData) return null;

    const bitmap = spriteData.bitmap || spriteData._bitmap;
    if (!bitmap) return null;

    // 🔧 处理 elements 数组，特别是 image 元素的 source
    const processedElements = (bitmap.elements || []).map((element: any) => {
      if (element.type === 'image' && element.source) {
        // 如果 source 是 bitmap 对象，需要提取其 regions 数据
        const processedElement = { ...element };

        if (element.source.regions) {
          // 将 source bitmap 的 regions 附加到元素上
          processedElement.regions = element.source.regions;
          console.log('🔧 从 image.source 提取 regions:', processedElement.regions);
        }

        return processedElement;
      }
      return element;
    });

    return {
      fontBold: bitmap.fontBold || false,
      fontFace: bitmap.fontFace || 'Arial',
      fontItalic: bitmap.fontItalic || false,
      fontSize: bitmap.fontSize || 16,
      outlineColor: bitmap.outlineColor || '#000000',
      outlineWidth: bitmap.outlineWidth || 0,
      textColor: bitmap.textColor || '#ffffff',
      _paintOpacity: bitmap._paintOpacity || 255,
      _smooth: bitmap._smooth || false,
      elements: processedElements,
      url: bitmap.url || undefined,
      regions: bitmap.regions || []  // 🔧 添加 regions 字段
    };
  }

  // 打开编辑器
  function openEditor() {
    const bitmapData = getBitmapData();
    if (!bitmapData) {
      console.error('无法获取bitmap数据');
      return;
    }

    currentBitmapData = bitmapData;
    isEditorOpen = true;
  }

  // 处理编辑器保存
  function handleEditorSave(updatedBitmapData: bitmapProperties) {
    console.log('编辑器保存数据:', updatedBitmapData);

    if (spriteData) {
      const bitmap = spriteData.bitmap || spriteData._bitmap;
      if (bitmap) {
        // 更新bitmap属性
        bitmap.fontBold = updatedBitmapData.fontBold;
        bitmap.fontFace = updatedBitmapData.fontFace;
        bitmap.fontItalic = updatedBitmapData.fontItalic;
        bitmap.fontSize = updatedBitmapData.fontSize;
        bitmap.outlineColor = updatedBitmapData.outlineColor;
        bitmap.outlineWidth = updatedBitmapData.outlineWidth;
        bitmap.textColor = updatedBitmapData.textColor;
        bitmap._paintOpacity = updatedBitmapData._paintOpacity;
        bitmap._smooth = updatedBitmapData._smooth;
        // 更新elements数组，确保数据结构正确
        if (updatedBitmapData.elements) {
          // 转换elements数组，确保符合插件期望的格式
          bitmap.elements = updatedBitmapData.elements.map(element => {
            if (element.type === 'text') {
              return {
                type: 'text',
                text: element.text,
                x: element.x,
                y: element.y,
                maxWidth: element.maxWidth || 0xffffffff,
                lineHeight: element.lineHeight || 36,
                align: element.align || 'left'
              };
            } else if (element.type === 'image') {
              const imageElement = {
                type: 'image',
                source: element.source,
                sx: element.sx || 0,
                sy: element.sy || 0,
                sw: element.sw || 100,
                sh: element.sh || 100,
                dx: element.dx,
                dy: element.dy,
                dw: element.dw,
                dh: element.dh
              };

              // 🔧 如果元素有 regions 数据，需要保存到 source bitmap 中
              if (element.regions && element.source) {
                console.log('🔧 保存 regions 数据到 image.source:', element.regions);
                element.source.regions = element.regions;
              }

              return imageElement;
            }
            return element;
          });
        } else {
          bitmap.elements = [];
        }

        if (updatedBitmapData.url) {
          bitmap.url = updatedBitmapData.url;
        }

        // 🔧 更新 regions 数据
        if (updatedBitmapData.regions) {
          bitmap.regions = [...updatedBitmapData.regions]; // 深拷贝 regions 数组
          console.log('更新 bitmap.regions:', bitmap.regions);
        }

        console.log('Sprite数据已更新');

        // 调用redrawing()方法重新绘制
        if (bitmap.redrawing && typeof bitmap.redrawing === 'function') {
          console.log('调用bitmap.redrawing()重新绘制');
          bitmap.redrawing();
        } else {
          console.warn('bitmap.redrawing方法不存在');
        }

        // 触发界面更新
        triggerUpdate();
      }
    }

    isEditorOpen = false;
  }

  // 处理编辑器取消
  function handleEditorCancel() {
    console.log('编辑器已取消');
    isEditorOpen = false;
  }



  // 监听对象变化，输出调试信息
  $effect(() => {
    if (selectedObject) {
      console.log('=== 标签属性面板更新 ===');
      console.log('选中对象:', selectedObject);
      console.log('对象类型:', selectedObjectType);
      console.log('是否为Sprite:', isSprite);
      console.log('是否有Elements:', hasElements());
      console.log('文本元素数量:', textElements().length);

      if (spriteData) {
        const bitmap = spriteData.bitmap || spriteData._bitmap;
        if (bitmap?.elements) {
          console.log('Elements数组:', bitmap.elements);
        }
      }
    }
  });
</script>

{#if selectedObject && hasElements()}
  <AccordionPanel
    title="标签属性"
    icon="📝"
    badge={`${textElements().length}个标签`}
    badgeVariant="info"
    bind:expanded={isExpanded}
  >
    <!-- 编辑器按钮 -->
    <div class="editor-controls">
      <button class="edit-button" onclick={openEditor}>
        <span class="edit-icon">✏️</span>
        <span class="edit-text">编辑标签</span>
      </button>
    </div>

    <!-- 只显示文本内容 -->
    {#if textElements().length > 0}
      <div class="text-content-list">
        {#each textElements() as element}
          <div class="text-item">
            <span class="text-content">{(element as any).text || '(空文本)'}</span>
          </div>
        {/each}
      </div>
    {:else}
      <div class="no-text">
        <span class="no-text-message">没有找到文本内容</span>
      </div>
    {/if}
  </AccordionPanel>
{/if}


<!-- Sprite编辑器模态框 -->
<LabelSpriteEditorModal
  bind:open={isEditorOpen}
  bitmapData={currentBitmapData}
  stageSize={stageSize()}
  spritePosition={{ x: spriteData?.x || 0, y: spriteData?.y || 0 }}
  onSave={handleEditorSave}
  onClose={handleEditorCancel}
/>

<style>
  .editor-controls {
    margin-bottom: var(--spacing-3, 0.75rem);
    display: flex;
    justify-content: center;
  }

  .edit-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-2, 0.5rem);
    padding: var(--spacing-2, 0.5rem) var(--spacing-3, 0.75rem);
    background: var(--theme-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: var(--border-radius, 4px);
    cursor: pointer;
    font-size: var(--font-size-sm, 0.875rem);
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .edit-button:hover {
    background: var(--theme-primary-dark, #2563eb);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .edit-icon {
    font-size: 1rem;
  }

  .edit-text {
    white-space: nowrap;
  }

  .text-content-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2, 0.5rem);
  }

  .text-item {
    padding: var(--spacing-2, 0.5rem);
    background: var(--theme-surface-dark, #1a202c);
    border-radius: var(--border-radius, 4px);
    border: 1px solid var(--theme-border, rgba(255, 255, 255, 0.2));
  }

  .text-content {
    font-size: var(--font-size-sm, 0.875rem);
    color: var(--theme-text, #ffffff);
    font-weight: 500;
    word-break: break-all;
    line-height: 1.4;
  }

  .no-text {
    text-align: center;
    padding: var(--spacing-4, 1rem);
    color: var(--theme-text-secondary, rgba(255, 255, 255, 0.8));
  }

  .no-text-message {
    font-size: var(--font-size-sm, 0.875rem);
  }
</style>