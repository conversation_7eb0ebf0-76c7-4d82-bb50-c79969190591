import React from 'react';
import { Box, Typography, Paper, Button } from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import ElementItem from './ElementItem';
import useEditorStore from '../store/editorStore';
import AddTextButton from './AddTextButton';
import AddImageButton from './AddImageButton';

// 获取当前sprite的辅助函数
const getCurrentSprite = () => {
  if (window.SpriteEditor && window.SpriteEditor.currentSprite) {
    return window.SpriteEditor.currentSprite;
  }
  console.warn('无法获取currentSprite，window.SpriteEditor:', window.SpriteEditor);
  return null;
};

// 使用React.memo包装组件，避免不必要的重新渲染
const ElementsList = React.memo(() => {
  console.log('ElementsList渲染');

  // 只订阅需要的状态，避免拖拽时的重新渲染
  const elements = useEditorStore((state) => state.elements);
  const redraw = useEditorStore((state) => state.redraw);
  const updateElements = useEditorStore((state) => state.updateElements);
  const saveSprite = useEditorStore((state) => state.saveSprite);
  const isSaving = useEditorStore((state) => state.isSaving);

  // 组件挂载时更新一次元素列表
  React.useEffect(() => {
    updateElements();
  }, [updateElements]);

  // 保存当前sprite的处理函数
  const handleSave = async () => {
    console.log('保存按钮被点击');

    try {
      const result = await saveSprite();
      if (result) {
        console.log('保存成功，导出的elements:', result);
        // 可以在这里添加成功提示
      } else {
        console.warn('保存失败，没有可保存的内容');
        // 可以在这里添加警告提示
      }
    } catch (error) {
      console.error('保存过程中发生错误:', error);
      // 可以在这里添加错误提示
    }
  };

  // 添加文本元素的处理函数
  const handleAddText = (textData) => {
    console.log('handleAddText被调用，textData:', textData);

    const sprite = getCurrentSprite();
    console.log('获取到的sprite:', sprite);

    if (!sprite) {
      console.error('无法获取sprite');
      return;
    }

    if (!sprite.bitmap) {
      console.error('sprite.bitmap不存在');
      return;
    }

    if (!sprite.bitmap.elements) {
      console.log('sprite.bitmap.elements不存在，创建一个空数组');
      sprite.bitmap.elements = [];
    }

    if (!sprite.bitmap.redrawing) {
      console.error('sprite.bitmap.redrawing方法不存在');
      return;
    }

    try {
      // 创建文本元素对象
      const textElement = {
        type: 'text',  // 添加类型标识
        text: textData.text,
        x: textData.x,
        y: textData.y,
        maxWidth: textData.maxWidth || 200,
        lineHeight: textData.lineHeight || 36,
        align: textData.align || 'left',
      };

      console.log('准备添加文本元素:', textElement);

      // 直接添加到elements数组
      sprite.bitmap.elements.push(textElement);
      console.log('文本元素已添加到bitmap.elements，当前elements:', sprite.bitmap.elements);

      // 重新绘制
      redraw();
      console.log('已调用redraw方法');

      console.log('文本已添加到bitmap.elements并重新绘制');
    } catch (e) {
      console.error('添加文本失败:', e);
      console.error('错误详情:', e.message, e.stack);
    }
  };
  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{
        display: 'flex',
        p: 0.5,
        borderBottom: '1px solid #ddd',
        bgcolor: '#f5f5f5',
        gap: 0.5
      }}>
        <AddTextButton onAddText={handleAddText} />
        <AddImageButton />
        <Button
          variant="contained"
          color="primary"
          size="small"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          disabled={isSaving}
          sx={{
            minWidth: 'auto',
            px: 1,
            fontSize: '0.75rem'
          }}
        >
          {isSaving ? '保存中...' : '保存'}
        </Button>
      </Box>
      <Box sx={{ flex: 1, overflow: 'auto', p: 0.5 }}>
        <Typography
          variant="subtitle1"
          sx={{
            mb: 1,
            pb: 0.5,
            borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
            fontSize: '1rem',
            fontWeight: 'bold'
          }}
        >
          元素列表
        </Typography>

        {/* 如果没有元素，显示提示信息 */}
        {(!elements || elements.length === 0) ? (
          <Paper
            elevation={0}
            sx={{
              p: 3,
              textAlign: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.03)',
              borderRadius: 2,
              mt: 2
            }}
          >
            <Typography color="text.secondary">
              暂无元素，请使用上方按钮添加
            </Typography>
          </Paper>
        ) : (
          // 如果有元素，显示元素列表
          elements.map((element, index) => (
            <ElementItem
              key={element.id || index}
              element={element}
              index={index}
            />
          ))
        )}
      </Box>
    </Box>
  );
});

// 不再需要 PropTypes，因为我们不再从外部接收 elements 属性

export default ElementsList;
