/**
 * RPG Maker MZ 基础对象必须保存的属性定义
 * 用于对象重建时确保所有关键属性都被正确保存和恢复
 */

/**
 * 基础显示对象属性 (PIXI.DisplayObject)
 */
export interface BaseDisplayProperties {
  // 位置和变换
  x: number;
  y: number;
  scaleX: number;    // scale.x
  scaleY: number;    // scale.y
  skewX: number;     // skew.x
  skewY: number;     // skew.y
  rotation: number;

  // 显示属性
  alpha: number;
  visible: boolean;

  // 锚点和轴心
  anchorX: number;   // anchor.x
  anchorY: number;   // anchor.y
  pivotX: number;    // pivot.x
  pivotY: number;    // pivot.y

  // 混合模式
  blendMode: number;

  // 遮罩和滤镜 (复杂对象，需要特殊处理)
  mask?: any;
  filters?: any[];
}

/**
 * 容器对象属性 (PIXI.Container)
 */
export interface ContainerProperties extends BaseDisplayProperties {
  // Container 特有属性
  interactiveChildren: boolean;
  sortableChildren: boolean;
}

/**
 * 精灵对象必须保存的属性
 */
export interface SpriteEssentialProperties extends ContainerProperties {
  // 图片和尺寸
  bitmap?: string;           // 图片路径或特殊标识
  width: number;            // _frame.width
  height: number;           // _frame.height
  opacity: number;          // alpha * 255 (0-255)

  // 帧设置
  frameX: number;           // _frame.x
  frameY: number;           // _frame.y
  frameWidth: number;       // _frame.width
  frameHeight: number;      // _frame.height

  // 色彩效果
  hue: number;              // _hue (-360 to 360)
  blendColor: number[];     // _blendColor [r,g,b,a]
  colorTone: number[];      // _colorTone [r,g,b,gray]

  // 特殊状态
  hidden: boolean;          // _hidden

  // RPG Maker MZ 特有
  spriteId?: number;        // 精灵ID
}

/**
 * 窗口对象必须保存的属性
 */
export interface WindowEssentialProperties extends ContainerProperties {
  // 窗口尺寸
  width: number;            // _width
  height: number;           // _height

  // 窗口样式
  windowskin?: string;      // 窗口皮肤图片路径
  padding: number;          // _padding
  margin: number;           // _margin

  // 透明度设置
  opacity: number;          // 窗口整体透明度 (0-255)
  backOpacity: number;      // 背景透明度 (0-255)
  contentsOpacity: number;  // 内容透明度 (0-255)
  openness: number;         // 开启度 (0-255)

  // 颜色调整
  colorTone: number[];      // _colorTone [r,g,b,0]

  // 窗口状态
  active: boolean;          // 激活状态
  frameVisible: boolean;    // 边框可见性
  cursorVisible: boolean;   // 光标可见性

  // 滚动箭头
  downArrowVisible: boolean;
  upArrowVisible: boolean;
  pause: boolean;           // 暂停标志

  // 光标位置
  cursorRectX: number;      // _cursorRect.x
  cursorRectY: number;      // _cursorRect.y
  cursorRectWidth: number;  // _cursorRect.width
  cursorRectHeight: number; // _cursorRect.height

  // 滚动原点
  originX: number;          // origin.x
  originY: number;          // origin.y

  // 内容位图
  contents?: string;        // 内容位图路径
  contentsBack?: string;    // 背景内容位图路径

  // 开关状态
  opening: boolean;         // _opening
  closing: boolean;         // _closing
}

/**
 * 场景对象必须保存的属性
 */
export interface SceneEssentialProperties extends ContainerProperties {
  // 场景状态
  started: boolean;         // _started
  active: boolean;          // _active

  // 淡入淡出
  fadeSign: number;         // _fadeSign
  fadeDuration: number;     // _fadeDuration
  fadeWhite: number;        // _fadeWhite
  fadeOpacity: number;      // _fadeOpacity
}

/**
 * 瓦片地图必须保存的属性
 */
export interface TilemapEssentialProperties extends ContainerProperties {
  // 地图尺寸
  width: number;            // _width
  height: number;           // _height

  // 瓦片设置
  tileWidth: number;        // 瓦片宽度
  tileHeight: number;       // 瓦片高度

  // 地图数据
  mapWidth: number;         // _mapWidth
  mapHeight: number;        // _mapHeight
  mapData?: number[];       // _mapData (可能很大，需要特殊处理)

  // 滚动设置
  originX: number;          // origin.x
  originY: number;          // origin.y

  // 循环设置
  horizontalWrap: boolean;
  verticalWrap: boolean;

  // 动画
  animationCount: number;

  // 瓦片集 (需要特殊处理)
  bitmaps?: string[];       // 瓦片集图片路径
  flags?: number[];         // 瓦片标志
}

/**
 * 构造函数参数定义
 */
export interface ConstructorParams {
  // Sprite 构造参数
  Sprite: {
    bitmap?: string;        // 可选的初始位图
  };

  // Window 构造参数
  Window: {
    // 无参数构造
  };

  Window_Base: {
    rect: {                 // Rectangle 对象
      x: number;
      y: number;
      width: number;
      height: number;
    };
  };

  // Scene 构造参数
  Scene_Base: {
    // 通常无参数
  };

  // Container 构造参数
  Container: {
    // 无参数构造
  };

  // Tilemap 构造参数
  Tilemap: {
    // 无参数构造
  };
}

/**
 * 对象类型到属性映射
 */
export const ESSENTIAL_PROPERTIES_MAP = {
  'PIXI.Container': 'ContainerProperties',
  'Container': 'ContainerProperties',
  'Sprite': 'SpriteEssentialProperties',
  'Window': 'WindowEssentialProperties',
  'Window_Base': 'WindowEssentialProperties',
  'Scene_Base': 'SceneEssentialProperties',
  'Scene_Title': 'SceneEssentialProperties',
  'Scene_Map': 'SceneEssentialProperties',
  'Scene_Menu': 'SceneEssentialProperties',
  'Tilemap': 'TilemapEssentialProperties'
} as const;

/**
 * 获取对象类型对应的必须属性列表
 */
export function getEssentialProperties(className: string): string[] {
  switch (className) {
    case 'Sprite':
      return [
        // 基础显示属性
        'x', 'y', 'scaleX', 'scaleY', 'skewX', 'skewY', 'rotation',
        'alpha', 'visible', 'anchorX', 'anchorY', 'pivotX', 'pivotY', 'blendMode',
        // Sprite 特有
        'bitmap', 'width', 'height', 'opacity',
        'frameX', 'frameY', 'frameWidth', 'frameHeight',
        'hue', 'blendColor', 'colorTone', 'hidden',
        // RPGEditor_BitmapTracker 插件支持 - bitmap 文字属性
        'elements', 'fontBold', 'fontFace', 'fontItalic', 'fontSize',
        'outlineColor', 'outlineWidth', 'textColor', '_paintOpacity', '_smooth'
      ];

    case 'Window':
    case 'Window_Base':
    case 'Window_Command':
    case 'Window_TitleCommand':
    case 'Window_Selectable':
    case 'Window_Scrollable':
      return [
        // 基础显示属性（继承自 PIXI.Container）
        'x', 'y', 'alpha', 'visible', 'rotation',
        'scaleX', 'scaleY', 'skewX', 'skewY',
        'anchorX', 'anchorY', 'pivotX', 'pivotY', 'blendMode',

        // 窗口核心内部属性
        '_isWindow', '_width', '_height', '_windowskin',
        '_padding', '_margin', '_colorTone', '_openness', '_animationCount',

        // 窗口状态属性
        'active', 'frameVisible', 'cursorVisible',
        'downArrowVisible', 'upArrowVisible', 'pause',

        // 光标和滚动
        '_cursorRect', 'origin',

        // Window_Base 特有
        '_opening', '_closing',

        // Window_Command 特有
        '_list',

        // 通过属性访问器的属性
        'width', 'height', 'padding', 'margin',
        'opacity', 'backOpacity', 'contentsOpacity', 'openness'
      ];

    case 'Scene_Base':
    case 'Scene_Title':
    case 'Scene_Map':
    case 'Scene_Menu':
      return [
        // 基础显示属性
        'x', 'y', 'alpha', 'visible',
        // 场景特有
        'started', 'active', 'fadeSign', 'fadeDuration', 'fadeWhite', 'fadeOpacity'
      ];

    case 'PIXI.Container':
    case 'Container':
      return [
        // 基础显示属性
        'x', 'y', 'scaleX', 'scaleY', 'skewX', 'skewY', 'rotation',
        'alpha', 'visible', 'anchorX', 'anchorY', 'pivotX', 'pivotY', 'blendMode',
        // 容器特有
        'interactiveChildren', 'sortableChildren'
      ];

    case 'Tilemap':
      return [
        // 基础显示属性
        'x', 'y', 'alpha', 'visible',
        // 瓦片地图特有
        'width', 'height', 'tileWidth', 'tileHeight',
        'mapWidth', 'mapHeight', 'originX', 'originY',
        'horizontalWrap', 'verticalWrap', 'animationCount'
      ];

    default:
      // 默认返回基础显示属性
      return [
        'x', 'y', 'scaleX', 'scaleY', 'rotation',
        'alpha', 'visible', 'anchorX', 'anchorY', 'pivotX', 'pivotY'
      ];
  }
}

/**
 * 检查属性是否需要特殊处理
 */
export function needsSpecialHandling(propertyName: string): boolean {
  const specialProperties = [
    'bitmap',        // 需要处理图片路径
    'windowskin',    // 需要处理图片路径
    'contents',      // 需要处理位图内容
    'contentsBack',  // 需要处理位图内容
    'mapData',       // 数据量可能很大
    'bitmaps',       // 数组类型
    'flags',         // 数组类型
    'mask',          // 复杂对象
    'filters'        // 复杂对象数组
  ];

  return specialProperties.includes(propertyName);
}
