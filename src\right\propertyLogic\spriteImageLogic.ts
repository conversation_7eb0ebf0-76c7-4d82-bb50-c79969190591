/**
 * Sprite图片切换逻辑处理模块
 * 负责处理Sprite对象的bitmap资源切换功能
 */

import { globalObjectState } from '../../stores/objectState';
import { get } from 'svelte/store';
import type { spriteProperties } from '../../generators/type';

/**
 * 图片文件选择结果接口
 */
export interface ImageSelectionResult {
  success: boolean;
  filePath?: string;
  relativePath?: string;
  error?: string;
}

/**
 * 图片切换操作结果接口
 */
export interface ImageChangeResult {
  success: boolean;
  oldImageUrl?: string;
  newImageUrl?: string;
  error?: string;
}

/**
 * 获取当前项目路径
 */
function getCurrentProjectPath(): string | null {
  try {
    // 方法1: 从window全局变量获取
    if ((window as any).currentProjectPath) {
      return (window as any).currentProjectPath;
    }

    // 方法2: 从项目状态store获取
    try {
      const { getCurrentProjectState } = require('../../stores/projectStore');
      const projectState = getCurrentProjectState();
      if (projectState && projectState.projectPath) {
        return projectState.projectPath;
      }
    } catch (e) {
      console.log('无法从projectStore获取项目路径:', e);
    }

    // 方法3: 从localStorage获取
    const savedProject = localStorage.getItem('currentProject');
    if (savedProject) {
      const projectData = JSON.parse(savedProject);
      if (projectData.projectPath) {
        return projectData.projectPath;
      }
    }

    // 方法4: 使用硬编码的默认路径（临时测试用）
    const defaultPath = 'D:\\gpgmake\\Project6';
    console.warn('无法获取当前项目路径，使用默认路径:', defaultPath);
    return defaultPath;
  } catch (error) {
    console.error('获取项目路径失败:', error);
    return null;
  }
}

/**
 * 将绝对路径转换为相对于项目的路径
 */
function convertToRelativePath(absolutePath: string, projectPath: string): string {
  try {
    // 使用简单的字符串替换来计算相对路径
    // 确保路径格式正确
    const normalizedAbsolute = absolutePath.replace(/\\/g, '/');
    const normalizedProject = projectPath.replace(/\\/g, '/');

    // 如果绝对路径包含项目路径，则计算相对路径
    if (normalizedAbsolute.startsWith(normalizedProject)) {
      let relativePath = normalizedAbsolute.substring(normalizedProject.length);
      // 移除开头的斜杠
      if (relativePath.startsWith('/')) {
        relativePath = relativePath.substring(1);
      }
      return relativePath;
    }

    // 如果不在项目目录内，返回原路径
    return absolutePath;
  } catch (error) {
    console.error('路径转换失败:', error);
    return absolutePath;
  }
}

/**
 * 获取默认的图片目录
 */
function getDefaultImageDirectory(currentBitmapUrl: string | undefined, projectPath: string): string {
  try {
    // 总是默认打开项目根目录
    return projectPath.replace(/\\/g, '/');

    // 注释掉原来的逻辑，如果需要可以恢复
    /*
    if (currentBitmapUrl && currentBitmapUrl.includes('/')) {
      // 从当前bitmap URL推断目录
      const pathParts = currentBitmapUrl.split('/');
      pathParts.pop(); // 移除文件名
      const currentDir = pathParts.join('/');
      return `${projectPath}/${currentDir}`.replace(/\\/g, '/');
    }

    // 默认打开img目录
    return `${projectPath}/img`.replace(/\\/g, '/');
    */
  } catch (error) {
    console.error('获取默认图片目录失败:', error);
    return projectPath;
  }
}

/**
 * 打开图片文件选择对话框
 */
export async function selectImageFile(currentBitmapUrl?: string): Promise<ImageSelectionResult> {
  try {
    console.log('=== 开始选择图片文件 ===');

    // 1. 获取项目路径
    const projectPath = getCurrentProjectPath();
    if (!projectPath) {
      return {
        success: false,
        error: '无法获取当前项目路径'
      };
    }

    console.log('项目路径:', projectPath);
    console.log('当前bitmap URL:', currentBitmapUrl);

    // 2. 确定默认打开目录
    const defaultDir = getDefaultImageDirectory(currentBitmapUrl, projectPath);
    console.log('默认目录:', defaultDir);

    // 3. 打开文件选择对话框
    const { open } = await import('@tauri-apps/plugin-dialog');
    const result = await open({
      directory: false,
      multiple: false,
      title: '选择图片文件',
      defaultPath: defaultDir,
      filters: [
        {
          name: 'PNG Images',
          extensions: ['png']
        },
        {
          name: 'JPEG Images',
          extensions: ['jpg', 'jpeg']
        },
        {
          name: 'GIF Images',
          extensions: ['gif']
        },
        {
          name: 'BMP Images',
          extensions: ['bmp']
        },
        {
          name: 'All Images',
          extensions: ['png', 'jpg', 'jpeg', 'gif', 'bmp']
        }
      ]
    });

    if (!result || typeof result !== 'string') {
      return {
        success: false,
        error: '用户取消选择'
      };
    }

    // 4. 转换为相对路径
    const relativePath = convertToRelativePath(result, projectPath);

    console.log('选择的文件:', result);
    console.log('相对路径:', relativePath);

    return {
      success: true,
      filePath: result,
      relativePath: relativePath
    };

  } catch (error) {
    console.error('选择图片文件失败:', error);
    return {
      success: false,
      error: `选择图片文件失败: ${error}`
    };
  }
}

/**
 * 切换Sprite的bitmap资源
 */
export async function changeSpriteImage(sprite: any, newImagePath: string): Promise<ImageChangeResult> {
  try {
    console.log('=== 开始切换Sprite图片 ===');
    console.log('目标Sprite:', sprite);
    console.log('新图片路径:', newImagePath);

    // 1. 保存旧的图片URL
    const oldImageUrl = sprite.bitmap?._originalPath || sprite.bitmap?._url || '';
    console.log('旧图片URL:', oldImageUrl);

    // 2. 检查ImageManager是否可用
    if (typeof window === 'undefined' || !(window as any).ImageManager) {
      return {
        success: false,
        error: 'ImageManager不可用'
      };
    }

    // 3. 使用ImageManager加载新bitmap（会被CustomResourcePath插件拦截处理）
    const ImageManager = (window as any).ImageManager;
    const newBitmap = ImageManager.loadBitmapFromUrl(newImagePath);

    console.log('新bitmap对象:', newBitmap);

    // 4. 设置到sprite（会触发_onBitmapChange）
    sprite.bitmap = newBitmap;

    // 5. 更新sprite的序列化数据（如果存在）
    if (sprite._serializedData && sprite._serializedData.bitmap) {
      sprite._serializedData.bitmap.url = newImagePath;
      console.log('已更新序列化数据');
    }

    // 6. 确保bitmap对象的_originalPath被正确设置
    if (newBitmap._originalPath) {
      console.log('新bitmap的_originalPath:', newBitmap._originalPath);
    }

    // 7. 等待新bitmap加载完成后再更新UI
    if (typeof newBitmap.addLoadListener === 'function') {
      newBitmap.addLoadListener(() => {
        console.log('新bitmap加载完成，URL:', newBitmap._url);
        triggerGlobalStateUpdate();
      });
    } else {
      // 如果没有加载监听器，延迟一点再更新
      setTimeout(() => {
        triggerGlobalStateUpdate();
      }, 100);
    }

    // 8. 立即触发一次更新（用于显示加载状态）
    triggerGlobalStateUpdate();

    console.log('Sprite图片切换成功');

    return {
      success: true,
      oldImageUrl: oldImageUrl,
      newImageUrl: newImagePath
    };

  } catch (error) {
    console.error('切换Sprite图片失败:', error);
    return {
      success: false,
      error: `切换图片失败: ${error}`
    };
  }
}

/**
 * 处理图片点击事件（完整流程）
 */
export async function handleImageClick(sprite: any): Promise<ImageChangeResult> {
  try {
    console.log('=== 处理图片点击事件 ===');

    // 1. 获取当前bitmap URL
    const currentBitmapUrl = sprite.bitmap?._originalPath || sprite.bitmap?._url;

    // 2. 选择新图片
    const selectionResult = await selectImageFile(currentBitmapUrl);

    if (!selectionResult.success || !selectionResult.relativePath) {
      return {
        success: false,
        error: selectionResult.error || '未选择图片'
      };
    }

    // 3. 切换图片
    const changeResult = await changeSpriteImage(sprite, selectionResult.relativePath);

    return changeResult;

  } catch (error) {
    console.error('处理图片点击事件失败:', error);
    return {
      success: false,
      error: `处理失败: ${error}`
    };
  }
}

/**
 * 触发全局状态更新
 */
function triggerGlobalStateUpdate(): void {
  try {
    globalObjectState.update(state => {
      // 强制触发重新渲染
      state.lastUpdateTime = Date.now();

      // 强制更新selectedObject数组，触发响应式更新
      if (state.selectedObject && Array.isArray(state.selectedObject)) {
        state.selectedObject = [...state.selectedObject];
      }

      return state;
    });
    console.log('全局状态已更新，时间戳:', Date.now());
  } catch (error) {
    console.error('触发全局状态更新失败:', error);
  }
}

/**
 * 检查对象是否为Sprite且有bitmap
 */
export function isSpriteWithBitmap(obj: any): boolean {
  return obj && obj.bitmap && typeof obj.bitmap === 'object';
}

/**
 * 获取Sprite的当前图片信息
 */
export function getSpriteImageInfo(sprite: any): { url?: string; isReady: boolean; width?: number; height?: number } {
  if (!isSpriteWithBitmap(sprite)) {
    return { isReady: false };
  }

  const bitmap = sprite.bitmap;
  const url = bitmap._originalPath || bitmap._url || '';
  const isReady = typeof bitmap.isReady === 'function' ? bitmap.isReady() : false;
  const width = bitmap.width || 0;
  const height = bitmap.height || 0;

  return { url, isReady, width, height };
}

/**
 * 验证图片文件是否有效
 */
export function validateImageFile(filePath: string): boolean {
  const validExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp'];
  const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
  return validExtensions.includes(extension);
}

/**
 * 获取图片文件的扩展名
 */
export function getImageExtension(filePath: string): string {
  const lastDotIndex = filePath.lastIndexOf('.');
  return lastDotIndex !== -1 ? filePath.substring(lastDotIndex) : '';
}

/**
 * 检查路径是否在项目目录内
 */
export function isPathInProject(filePath: string, projectPath: string): boolean {
  try {
    const normalizedFile = filePath.replace(/\\/g, '/');
    const normalizedProject = projectPath.replace(/\\/g, '/');
    return normalizedFile.startsWith(normalizedProject);
  } catch (error) {
    console.error('检查路径失败:', error);
    return false;
  }
}

/**
 * 创建图片切换的操作记录（用于撤销/重做）
 */
export interface ImageChangeOperation {
  type: 'image_change';
  spriteId: string | number;
  oldImageUrl: string;
  newImageUrl: string;
  timestamp: number;
}

/**
 * 创建图片切换操作记录
 */
export function createImageChangeOperation(
  sprite: any,
  oldImageUrl: string,
  newImageUrl: string
): ImageChangeOperation {
  return {
    type: 'image_change',
    spriteId: sprite.spriteId || sprite.id || Date.now(),
    oldImageUrl,
    newImageUrl,
    timestamp: Date.now()
  };
}

/**
 * 批量切换多个Sprite的图片
 */
export async function changeMutipleSpriteImages(
  sprites: any[],
  newImagePath: string
): Promise<ImageChangeResult[]> {
  const results: ImageChangeResult[] = [];

  for (const sprite of sprites) {
    const result = await changeSpriteImage(sprite, newImagePath);
    results.push(result);
  }

  return results;
}

/**
 * 预加载图片资源
 */
export async function preloadImage(imagePath: string): Promise<boolean> {
  try {
    if (typeof window === 'undefined' || !(window as any).ImageManager) {
      return false;
    }

    const ImageManager = (window as any).ImageManager;
    const bitmap = ImageManager.loadBitmapFromUrl(imagePath);

    // 等待图片加载完成
    return new Promise((resolve) => {
      if (typeof bitmap.addLoadListener === 'function') {
        bitmap.addLoadListener(() => {
          resolve(bitmap.isReady());
        });
      } else {
        // 如果没有加载监听器，直接返回true
        resolve(true);
      }
    });
  } catch (error) {
    console.error('预加载图片失败:', error);
    return false;
  }
}
