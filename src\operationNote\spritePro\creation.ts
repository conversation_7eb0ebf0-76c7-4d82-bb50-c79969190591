/**
 * Sprite 对象创建模块
 * 负责生成对象创建的代码
 */

import type { SpriteData } from './index';

/**
 * 创建结果接口
 */
export interface CreationResult {
  code: string;
  varName: string;
}

/**
 * 生成 Sprite 对象创建代码
 * @param spriteData Sprite 数据
 * @param indent 缩进字符串
 * @returns 创建结果
 */
export function generateSpriteCreation(
  spriteData: SpriteData,
  indent: string
): CreationResult {
  const { className, referenceName, properties } = spriteData;
  
  // 生成变量名
  const varName = referenceName || generateUniqueVarName(className);
  
  let code = `${indent}// 创建 ${className}\n`;
  
  // 特殊处理：WindowLayer 只保存位置信息
  if (className === 'WindowLayer') {
    code += `${indent}// WindowLayer 位置信息（将在窗口创建前应用）\n`;
    code += `${indent}// 保存 WindowLayer 位置信息\n`;
    code += `${indent}this._windowLayerPosition = {\n`;
    code += `${indent}    x: ${properties?.x || 0},\n`;
    code += `${indent}    y: ${properties?.y || 0}\n`;
    code += `${indent}};\n`;
    code += `${indent}console.log('保存 WindowLayer 位置信息:', this._windowLayerPosition);\n`;
    
    return { code, varName };
  }
  
  // 特殊处理：Window 类型需要 Rectangle 参数
  if (className.startsWith('Window_')) {
    code += generateWindowCreation(className, varName, properties, indent);
  } else {
    // 普通 Sprite 对象使用无参构造函数
    const actualClassName = getActualClassName(className);
    code += `${indent}const ${varName} = new ${actualClassName}();\n`;
  }
  
  return { code, varName };
}

/**
 * 生成 Window 对象创建代码
 */
function generateWindowCreation(
  className: string,
  varName: string,
  properties: Record<string, any>,
  indent: string
): string {
  let code = '';
  
  if (className === 'Window_TitleCommand') {
    // 特殊处理标题命令窗口
    code += `${indent}const rect = this.commandWindowRect();\n`;
    code += `${indent}const ${varName} = new ${className}(rect);\n`;
    code += `${indent}${varName}.setBackgroundType($dataSystem.titleCommandWindow.background);\n`;
    code += `${indent}${varName}.setHandler("newGame", this.commandNewGame.bind(this));\n`;
    code += `${indent}${varName}.setHandler("continue", this.commandContinue.bind(this));\n`;
    code += `${indent}${varName}.setHandler("options", this.commandOptions.bind(this));\n`;
  } else {
    // 其他 Window 类型使用属性创建 Rectangle
    const x = properties?.x || 0;
    const y = properties?.y || 0;
    const width = properties?.width || 240;
    const height = properties?.height || 120;
    code += `${indent}const ${varName} = new ${className}(new Rectangle(${x}, ${y}, ${width}, ${height}));\n`;
  }
  
  return code;
}

/**
 * 获取实际的类名（处理 PIXI 命名空间）
 */
function getActualClassName(className: string): string {
  const pixiClassMap: Record<string, string> = {
    'Container': 'PIXI.Container',
    'TilingSprite': 'PIXI.TilingSprite',
    'Graphics': 'PIXI.Graphics',
    'Text': 'PIXI.Text',
    'BitmapText': 'PIXI.BitmapText'
  };
  
  return pixiClassMap[className] || className;
}

/**
 * 生成唯一变量名
 */
function generateUniqueVarName(className: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${className.toLowerCase()}_${timestamp}_${random}`;
}
