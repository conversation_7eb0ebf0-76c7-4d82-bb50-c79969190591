/**
 * 插件代码生成器
 * 基于操作记录数据生成RPG Maker MZ插件代码
 */

import type { SceneAnalysisResult, BaseDisplayProperties, spriteProperties } from './type';
import { SpriteObjectSerializer } from './spriteSerializer';
import { BaseObjectSerializer } from './baseSerializer';

/**
 * 操作记录数据接口
 */
export interface OperationRecordsData {
  version: string;
  timestamp: number;
  author: string;
  records: Record<string, SceneAnalysisResult>;
}

/**
 * 插件生成配置
 */
export interface PluginGenerationConfig {
  pluginName?: string;
  pluginDescription?: string;
  author?: string;
  version?: string;
}

/**
 * 主要的插件代码生成方法
 * @param data 操作记录数据
 * @param config 插件生成配置
 * @returns 生成的插件代码
 */
export function generatePluginCode(
  data: OperationRecordsData,
  config: PluginGenerationConfig = {}
): string {
  console.log('=== 开始生成插件代码 ===');

  const {
    pluginName = 'RPGEditor_GeneratedPlugin',
    pluginDescription = 'Auto-generated plugin from RPG Editor',
    author = 'RPG Editor',
    version = '1.0.0'
  } = config;

  // 生成插件头部
  const header = generatePluginHeader(pluginName, pluginDescription, author, version);

  // 生成场景处理代码
  const sceneCode = generateSceneCode(data.records);

  // 组装完整插件代码
  const fullCode = [
    header,
    '',
    '(() => {',
    '  "use strict";',
    '',
    '  // 插件初始化',
    '  console.log("RPG Editor Generated Plugin loaded");',
    '',
    sceneCode,
    '',
    '})();'
  ].join('\n');

  console.log('=== 插件代码生成完成 ===');
  return fullCode;
}

/**
 * 生成插件头部注释
 */
function generatePluginHeader(
  name: string,
  description: string,
  author: string,
  version: string
): string {
  return `/*:
 * @target MZ
 * @plugindesc ${name} v${version}
 * <AUTHOR>
 * @version ${version}
 * @description ${description}
 *
 * @help ${name}.js
 *
 * This plugin was automatically generated by RPG Editor.
 * It recreates the scene objects and UI elements based on saved data.
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 * Free for commercial and non-commercial use.
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 * Version ${version}: Initial release
 */`;
}

/**
 * 生成场景处理代码
 */
function generateSceneCode(sceneRecords: Record<string, SceneAnalysisResult>): string {
  const sceneCodes: string[] = [];

  for (const [sceneId, sceneData] of Object.entries(sceneRecords)) {
    console.log(`生成场景代码: ${sceneId}`);
    const code = generateSingleSceneCode(sceneId, sceneData);
    sceneCodes.push(code);
  }

  return sceneCodes.join('\n\n');
}

/**
 * 生成单个场景的代码
 */
function generateSingleSceneCode(_sceneId: string, sceneData: SceneAnalysisResult): string {
  const { sceneClassName, uiObjects } = sceneData;

  const code = [
    `  // ===== ${sceneClassName} 场景处理 =====`,
    `  ${sceneClassName}.prototype.create = function() {`,
    `    Scene_Base.prototype.create.call(this);`,
    `    console.log('RPG Editor: 开始创建 ${sceneClassName} 的自定义对象');`,
    '',
    `    // 创建UI对象`,
    generateUIObjectsCode(uiObjects),
    '',
    `    console.log('RPG Editor: ${sceneClassName} 自定义对象创建完成');`,
    `  };`,
    '',
    // 生成场景特定的方法重写
    generateSceneSpecificMethodOverrides(sceneClassName)
  ];

  return code.join('\n');
}

/**
 * 生成UI对象代码
 * 根据对应的路径直接添加对象
 */
function generateUIObjectsCode(uiObjects: Map<string, BaseDisplayProperties> | Record<string, BaseDisplayProperties>): string {
  const codes: string[] = [];

  // 处理Map或普通对象
  const objectsEntries = uiObjects instanceof Map ? uiObjects.entries() : Object.entries(uiObjects);

  for (const [path, properties] of objectsEntries) {
    // 根据对象类型选择合适的序列化器进行代码生成
    if (properties.className === 'Sprite') {
      const spriteSerializer = new SpriteObjectSerializer();
      codes.push(spriteSerializer.generateObjectCode(path, properties as spriteProperties, '    '));
    } else {
      // 其他类型使用基础序列化器
      const baseSerializer = new BaseObjectSerializer();
      codes.push(baseSerializer.generateObjectCode(path, properties, '    '));
    }
  }

  return codes.join('\n');
}







/**
 * 生成场景特定的方法重写
 */
function generateSceneSpecificMethodOverrides(sceneClassName: string): string {
  if (sceneClassName === 'Scene_Title') {
    return generateSceneTitleMethodOverrides();
  }

  if (sceneClassName === 'Scene_Map') {
    return generateSceneMapMethodOverrides();
  }

  return '';
}

/**
 * 生成 Scene_Title 特定的方法重写
 */
function generateSceneTitleMethodOverrides(): string {
  const code = [
    `  // ===== Scene_Title 方法重写 =====`,
    `  // 重写 createBackground - 跳过原生背景创建`,
    `  Scene_Title.prototype.createBackground = function() {`,
    `    console.log('RPG Editor: 跳过原生背景创建，使用编辑器对象');`,
    `  };`,
    '',
    `  // 重写 createForeground - 跳过原生前景创建`,
    `  Scene_Title.prototype.createForeground = function() {`,
    `    console.log('RPG Editor: 跳过原生前景创建，使用编辑器对象');`,
    `  };`,
    '',
    `  // 重写 adjustBackground - 避免访问不存在的背景精灵`,
    `  Scene_Title.prototype.adjustBackground = function() {`,
    `    console.log('RPG Editor: 跳过背景调整，使用编辑器设置的属性');`,
    `  };`,
    '',
    `  // 重写 createCommandWindow - 跳过原生命令窗口创建`,
    `  Scene_Title.prototype.createCommandWindow = function() {`,
    `    console.log('RPG Editor: 跳过原生命令窗口创建，使用编辑器对象');`,
    `  };`,
    '',
    `  // 重写 isBusy - 安全检查命令窗口状态`,
    `  Scene_Title.prototype.isBusy = function() {`,
    `    if (this._commandWindow && this._commandWindow.isClosing) {`,
    `      return this._commandWindow.isClosing();`,
    `    }`,
    `    return Scene_Base.prototype.isBusy.call(this);`,
    `  };`,
    '',
    `  // 重写 update - 安全检查命令窗口`,
    `  Scene_Title.prototype.update = function() {`,
    `    if (!this.isBusy() && this._commandWindow && this._commandWindow.open) {`,
    `      this._commandWindow.open();`,
    `    }`,
    `    Scene_Base.prototype.update.call(this);`,
    `  };`,
    '',
    `  // 重写 terminate - 安全检查游戏标题精灵`,
    `  Scene_Title.prototype.terminate = function() {`,
    `    Scene_Base.prototype.terminate.call(this);`,
    `    SceneManager.snapForBackground();`,
    `    if (this._gameTitleSprite && this._gameTitleSprite.bitmap) {`,
    `      this._gameTitleSprite.bitmap.destroy();`,
    `    }`,
    `  };`,
    '',
    `  // 重写 scaleSprite - 安全检查精灵对象`,
    `  Scene_Title.prototype.scaleSprite = function(sprite) {`,
    `    if (sprite && sprite.bitmap) {`,
    `      Scene_Base.prototype.scaleSprite.call(this, sprite);`,
    `    }`,
    `  };`,
    '',
    `  // 重写 centerSprite - 安全检查精灵对象`,
    `  Scene_Title.prototype.centerSprite = function(sprite) {`,
    `    if (sprite && sprite.bitmap) {`,
    `      Scene_Base.prototype.centerSprite.call(this, sprite);`,
    `    }`,
    `  };`
  ];

  return code.join('\n');
}

/**
 * 生成 Scene_Map 特定的方法重写
 */
function generateSceneMapMethodOverrides(): string {
  const code = [
    `  // ===== Scene_Map 方法重写 =====`,
    `  // 重写 createSpriteset - 跳过原生精灵集创建`,
    `  Scene_Map.prototype.createSpriteset = function() {`,
    `    console.log('RPG Editor: 跳过原生精灵集创建，使用编辑器对象');`,
    `  };`,
    '',
    `  // 重写 createAllWindows - 跳过原生窗口创建`,
    `  Scene_Map.prototype.createAllWindows = function() {`,
    `    console.log('RPG Editor: 跳过原生窗口创建，使用编辑器对象');`,
    `  };`,
    '',
    `  // 重写 update - 安全检查精灵集`,
    `  Scene_Map.prototype.update = function() {`,
    `    Scene_Base.prototype.update.call(this);`,
    `    if (this._spriteset && this._spriteset.update) {`,
    `      this._spriteset.update();`,
    `    }`,
    `  };`,
    '',
    `  // 重写 updateTransferPlayer - 安全检查`,
    `  Scene_Map.prototype.updateTransferPlayer = function() {`,
    `    if ($gamePlayer.isTransferring()) {`,
    `      SceneManager.goto(Scene_Map);`,
    `    }`,
    `  };`
  ];

  return code.join('\n');
}
