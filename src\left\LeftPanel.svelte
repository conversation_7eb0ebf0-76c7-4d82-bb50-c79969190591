<script lang="ts">
  import ObjectTreePanel from './objectTree/ObjectTreePanel.svelte';

  // 左侧面板组件 - 重新实现为对象树面板
  console.log('LeftPanel 组件已加载 - 对象树版本');
</script>

<div class="left-panel">
  <ObjectTreePanel />
</div>

<style>
  .left-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-background);
    overflow: hidden;
  }

  /* 确保对象树面板占满整个左侧面板 */
  .left-panel :global(.object-tree-panel) {
    height: 100%;
    flex: 1;
  }
</style>
