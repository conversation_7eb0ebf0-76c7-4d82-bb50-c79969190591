<script lang="ts">
  /**
   * 中心面板组件
   * 显示主要内容区域，监听项目状态变化并动态加载脚本
   */

  import { onMount, onDestroy } from 'svelte';
  import { projectStore, subscribeToProjectPath } from '../stores/projectStore';
  import type { ProjectResources } from '../logics/projectManage/projectLoad';

  // 状态管理
  let canvasContainer: HTMLDivElement;
  let isEngineLoaded = $state(false);
  let loadingMessage = $state('等待项目加载...');
  let currentProjectPath = $state<string | null>(null);

  // 监听项目配置变化
  let unsubscribe: (() => void) | null = null;
  let projectConfigCheckInterval: number | null = null;

  // 检查项目配置变化
  function checkProjectConfig() {
    const projectConfig = (window as any).PROJECT_CONFIG;

    if (projectConfig && projectConfig.useExternalResources) {
      // 检查是否是新的项目配置
      if (currentProjectPath !== projectConfig.projectPath) {
        console.log('CenterPanel 检测到新的项目配置:');
        console.log('- 项目名称:', projectConfig.projectName);
        console.log('- 项目路径:', projectConfig.projectPath);
        console.log('- 脚本数量:', projectConfig.scripts.length);

        currentProjectPath = projectConfig.projectPath;
        loadEngineWithProject(projectConfig);
      }
    } else if (currentProjectPath) {
      // 项目配置被清除
      console.log('CenterPanel 检测到项目配置被清除');
      currentProjectPath = null;
      clearEngine();
    }
  }

  // 组件挂载时设置监听
  onMount(() => {
    console.log('CenterPanel 组件已挂载');

    // 立即检查一次项目配置
    checkProjectConfig();

    // 定期检查项目配置变化（每500ms检查一次）
    projectConfigCheckInterval = setInterval(checkProjectConfig, 500);

    // 保留原有的项目路径订阅作为备用
    unsubscribe = subscribeToProjectPath(async (projectPath) => {
      console.log('CenterPanel 检测到项目路径变化（备用）:', projectPath);

      // 如果没有项目配置，使用传统方式
      if (!((window as any).PROJECT_CONFIG) && projectPath && projectPath !== currentProjectPath) {
        currentProjectPath = projectPath;
        await loadEngineWithProject({ projectPath, useExternalResources: false });
      }
    });
  });

  // 组件销毁时清理
  onDestroy(() => {
    if (unsubscribe) {
      unsubscribe();
    }
    if (projectConfigCheckInterval) {
      clearInterval(projectConfigCheckInterval);
    }
    if (styleMonitorInterval) {
      clearInterval(styleMonitorInterval);
    }
  });

  /**
   * 加载引擎和项目脚本
   */
  async function loadEngineWithProject(projectConfig: any) {
    try {
      loadingMessage = '正在设置项目配置...';

      if (projectConfig.useExternalResources) {
        console.log('使用新的项目配置系统');
        console.log('项目配置已设置，main.js 将自动加载项目脚本');
      } else {
        console.log('使用传统项目路径模式');
        // 兼容旧的方式
        (window as any).PROJECT_BASE_PATH = projectConfig.projectPath;
      }

      loadingMessage = '正在加载引擎脚本...';
      console.log('开始动态加载 main.js');

      // 动态创建并加载 main.js
      await loadMainScript();

      loadingMessage = '引擎加载完成';
      isEngineLoaded = true;

      console.log('引擎加载完成，Canvas 应该已显示');

    } catch (error) {
      console.error('加载引擎失败:', error);
      loadingMessage = `加载失败: ${error}`;
      isEngineLoaded = false;
    }
  }

  /**
   * 动态加载 main.js 脚本
   */
  async function loadMainScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载过
      const existingScript = document.querySelector('script[src="src/engine/js/main.js"]');
      if (existingScript) {
        console.log('main.js 已经加载，移除旧的脚本');
        existingScript.remove();

        // 清理全局对象
        if ((window as any).RPGMakerEngine) {
          delete (window as any).RPGMakerEngine;
        }
        if ((window as any).RPGMakerMainInstance) {
          delete (window as any).RPGMakerMainInstance;
        }
      }

      console.log('创建新的 main.js 脚本标签');
      const script = document.createElement('script');
      script.type = 'module';  // 设置为模块以支持 ES6 import
      script.src = 'src/engine/js/main.js';
      script.async = false;
      script.defer = true;

      script.onload = () => {
        console.log('main.js 加载完成');

        // 等待一下让引擎初始化
        setTimeout(() => {
          console.log('引擎初始化完成');
          resolve();
        }, 1000);
      };

      script.onerror = (error) => {
        console.error('main.js 加载失败:', error);
        reject(new Error('Failed to load main.js'));
      };

      // 添加到 head 而不是 body，确保优先加载
      document.head.appendChild(script);
    });
  }

  /**
   * 清理引擎
   */
  function clearEngine() {
    console.log('清理引擎状态');

    // 清理全局变量
    delete (window as any).PROJECT_BASE_PATH;
    delete (window as any).PROJECT_CONFIG;
    delete (window as any).RPGMakerMainInstance;

    // 清理 Canvas
    if (canvasContainer) {
      canvasContainer.innerHTML = '';
    }

    // 重置 Canvas 处理标志
    canvasProcessed = false;

    isEngineLoaded = false;
    loadingMessage = '等待项目加载...';

    console.log('引擎状态已清理');
  }

  // 标记是否已经处理过 Canvas
  let canvasProcessed = false;
  let styleMonitorInterval: number | null = null;

  /**
   * 启动 Canvas 样式监控，防止被其他代码覆盖
   */
  function startCanvasStyleMonitoring() {
    if (styleMonitorInterval) {
      clearInterval(styleMonitorInterval);
    }

    styleMonitorInterval = setInterval(() => {
      const canvas = document.getElementById('gameCanvas');
      const rpgGameContainer = canvasContainer?.querySelector('.rpg-game-container');

      if (canvas && rpgGameContainer && canvas.parentElement === rpgGameContainer) {
        // 检查关键样式是否被修改
        const needsReset =
          canvas.style.position !== 'absolute' ||
          canvas.style.top !== '0px' ||
          canvas.style.left !== '0px' ||
          canvas.style.width !== '816px' ||
          canvas.style.height !== '624px' ||
          canvas.style.opacity !== '1' ||
          canvas.style.filter !== 'none' ||
          canvas.style.display !== 'block';

        if (needsReset) {
          console.log('检测到 Canvas 样式被修改，重新应用正确样式');
          applyCanvasStyles(canvas);
        }
      }
    }, 500); // 每500ms检查一次
  }

  /**
   * 应用正确的 Canvas 样式
   */
  function applyCanvasStyles(canvas: HTMLElement) {
    // 为 RPG 游戏容器中的 Canvas 设置样式
    canvas.style.setProperty('opacity', '1', 'important');
    canvas.style.setProperty('filter', 'none', 'important');
    canvas.style.setProperty('position', 'absolute', 'important');
    canvas.style.setProperty('top', '0', 'important');
    canvas.style.setProperty('left', '0', 'important');
    canvas.style.setProperty('transform', 'none', 'important');
    canvas.style.setProperty('margin', '0', 'important');
    canvas.style.setProperty('width', '816px', 'important');
    canvas.style.setProperty('height', '624px', 'important');
    canvas.style.setProperty('display', 'block', 'important');
    canvas.style.setProperty('pointer-events', 'auto', 'important');
    canvas.style.setProperty('border', 'none', 'important');
    canvas.style.setProperty('outline', 'none', 'important');
    canvas.style.setProperty('z-index', '1', 'important');

    console.log('Canvas 样式已应用：RPG 游戏容器内定位');
  }

  function applyRpgElementStyles(element: HTMLElement, zIndex: string = '1') {
    // 为 RPG 游戏容器中的其他元素设置样式
    element.style.setProperty('position', 'absolute', 'important');
    element.style.setProperty('top', '0', 'important');
    element.style.setProperty('left', '0', 'important');
    element.style.setProperty('z-index', zIndex, 'important');

    console.log(`RPG 元素样式已应用: ${element.id || element.tagName}`);
  }

  /**
   * 检查并移动所有 RPG Maker 相关的 DOM 元素到 RPG 游戏容器中（只执行一次）
   */
  function checkCanvasStatus() {
    if (!canvasContainer || canvasProcessed) return;

    // 获取 RPG 游戏容器
    const rpgGameContainer = canvasContainer.querySelector('.rpg-game-container');
    if (!rpgGameContainer) {
      console.error('未找到 RPG 游戏容器');
      return;
    }

    // 查找所有可能的 RPG Maker DOM 元素
    const canvas = document.getElementById('gameCanvas');
    const errorPrinter = document.getElementById('errorPrinter');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const gameVideo = document.getElementById('gameVideo');

    // 查找所有可能的 RPG Maker 容器
    const gameElements = document.querySelectorAll('[id*="game"], [class*="game"], [id*="rpg"], [class*="rpg"]');

    if (canvas) {
      console.log('首次找到 Canvas 元素，开始处理:', canvas);
      console.log('Canvas 当前父元素:', canvas.parentElement);

      // 将 Canvas 移动到 RPG 游戏容器中
      if (canvas.parentElement !== rpgGameContainer) {
        console.log('将 Canvas 移动到 RPG 游戏容器中');
        rpgGameContainer.appendChild(canvas);
        applyCanvasStyles(canvas);
      }

      // 移动视频元素到 RPG 游戏容器中
      if (gameVideo && gameVideo.parentElement !== rpgGameContainer) {
        console.log('移动 gameVideo 到 RPG 游戏容器');
        rpgGameContainer.appendChild(gameVideo);
        applyRpgElementStyles(gameVideo, '2');
      }

      // 移动错误显示器到 RPG 游戏容器中（如果存在且有内容的话）
      if (errorPrinter && errorPrinter.parentElement !== rpgGameContainer) {
        console.log('移动 errorPrinter 到 RPG 游戏容器');
        rpgGameContainer.appendChild(errorPrinter);
        // 只设置位置和层级，不改变其他样式
        errorPrinter.style.setProperty('position', 'absolute', 'important');
        errorPrinter.style.setProperty('top', '0', 'important');
        errorPrinter.style.setProperty('left', '0', 'important');
        errorPrinter.style.setProperty('z-index', '10', 'important');

        // 如果 errorPrinter 是空的或者没有错误内容，隐藏它
        if (!errorPrinter.innerHTML || errorPrinter.innerHTML.trim() === '') {
          errorPrinter.style.setProperty('display', 'none', 'important');
          console.log('errorPrinter 为空，已隐藏');
        } else {
          console.log('errorPrinter 已移动，保持原有样式');
        }
      }

      // 移动加载器到 RPG 游戏容器中
      if (loadingSpinner && loadingSpinner.parentElement !== rpgGameContainer) {
        console.log('移动 loadingSpinner 到 RPG 游戏容器');
        rpgGameContainer.appendChild(loadingSpinner);
        applyRpgElementStyles(loadingSpinner, '5');
      }

      // 检查其他可能的游戏元素
      gameElements.forEach((element) => {
        if (element.parentElement && element.parentElement.tagName === 'BODY' &&
            element !== canvas && element !== gameVideo && element !== errorPrinter && element !== loadingSpinner) {
          console.log('移动游戏元素到 RPG 游戏容器:', element);
          rpgGameContainer.appendChild(element);
          applyRpgElementStyles(element as HTMLElement);
        }
      });

      // 隐藏占位符
      const placeholder = canvasContainer.querySelector('.canvas-placeholder');
      if (placeholder) {
        (placeholder as HTMLElement).style.display = 'none';
      }

      // 标记为已处理，避免重复执行
      canvasProcessed = true;
      console.log('所有 RPG Maker 元素已移动到游戏容器中，停止定期检查');

      // 启动样式监控，防止被其他代码覆盖
      startCanvasStyleMonitoring();

      // 调试信息：显示最终状态
      setTimeout(() => {
        console.log('=== RPG 游戏容器最终状态 ===');
        console.log('RPG 游戏容器:', rpgGameContainer);
        console.log('容器内元素:', rpgGameContainer.children);
        console.log('Canvas 位置:', canvas.getBoundingClientRect());
        console.log('RPG 游戏容器位置:', rpgGameContainer.getBoundingClientRect());
      }, 500);
    }
  }

  // 定期检查 Canvas 状态（直到找到为止）
  $effect(() => {
    if (isEngineLoaded && canvasContainer && !canvasProcessed) {
      console.log('开始定期检查 Canvas 状态...');
      const interval = setInterval(() => {
        checkCanvasStatus();
        // 如果已经处理完成，清除定时器
        if (canvasProcessed) {
          clearInterval(interval);
        }
      }, 500); // 减少检查频率到500ms

      return () => {
        clearInterval(interval);
      };
    }
  });
</script>

<div class="center-panel">
  <div class="header">
    <h2>游戏画面</h2>
    <div class="status">
      {#if currentProjectPath}
        <span class="project-info">项目: {currentProjectPath.split(/[/\\]/).pop()}</span>
      {/if}
      <span class="engine-status" class:loaded={isEngineLoaded}>
        {isEngineLoaded ? '引擎已加载' : '引擎未加载'}
      </span>
    </div>
  </div>

  <div class="content">
    {#if !currentProjectPath}
      <div class="no-project">
        <div class="message">
          <h3>没有加载项目</h3>
          <p>请通过菜单栏的"文件 → 打开"来选择一个 .rmmzproject 文件</p>
        </div>
      </div>
    {:else if !isEngineLoaded}
      <div class="loading">
        <div class="spinner"></div>
        <p>{loadingMessage}</p>
      </div>
    {:else}
      <!-- Canvas 容器 -->
      <div class="canvas-container" bind:this={canvasContainer}>
        <!-- RPG Maker 游戏容器 - 所有游戏元素都会被放在这里 -->
        <div class="rpg-game-container">
          <div class="canvas-placeholder">
            <p>Canvas 将在这里显示</p>
            <button onclick={checkCanvasStatus}>检查 Canvas 状态</button>
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .center-panel {
    height: 100%;
    background: var(--theme-background);
    color: var(--theme-text);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .header {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--theme-border);
    background: var(--theme-surface);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header h2 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--theme-primary);
  }

  .status {
    display: flex;
    gap: var(--spacing-3);
    align-items: center;
  }

  .project-info {
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
    font-family: var(--font-family-mono);
  }

  .engine-status {
    font-size: var(--font-size-xs);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-small);
    background: var(--theme-error);
    color: var(--theme-text-inverse);
    font-weight: 500;
  }

  .engine-status.loaded {
    background: var(--theme-success);
  }

  .content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  .no-project {
    text-align: center;
    padding: var(--spacing-8);
  }

  .message h3 {
    margin: 0 0 var(--spacing-3) 0;
    font-size: var(--font-size-xl);
    color: var(--theme-text-secondary);
  }

  .message p {
    margin: 0;
    color: var(--theme-text-secondary);
    font-size: var(--font-size-base);
  }

  .loading {
    text-align: center;
    padding: var(--spacing-8);
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--theme-border);
    border-top: 4px solid var(--theme-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-4) auto;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .loading p {
    margin: 0;
    color: var(--theme-text-secondary);
    font-size: var(--font-size-base);
  }

  .canvas-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000000;  /* RPG Maker 通常使用黑色背景 */
    border: 2px solid var(--theme-border);
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
    padding: var(--spacing-4);
    box-sizing: border-box;
  }

  .rpg-game-container {
    position: relative;
    width: 816px;  /* RPG Maker 标准宽度 */
    height: 624px; /* RPG Maker 标准高度 */
    max-width: calc(100% - 32px);
    max-height: calc(100% - 32px);
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 确保 Canvas 在 RPG 游戏容器中正确显示 */
  .rpg-game-container :global(#gameCanvas) {
    opacity: 1 !important;
    filter: none !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    transform: none !important;
    margin: 0 !important;
    width: 816px !important;
    height: 624px !important;
    display: block !important;
    border: none !important;
    outline: none !important;
    z-index: 1 !important;
    pointer-events: auto !important;
  }

  /* 确保视频元素也在 RPG 游戏容器中正确显示 */
  .rpg-game-container :global(#gameVideo) {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 816px !important;
    height: 624px !important;
    transform: none !important;
    object-fit: contain !important;
    z-index: 2 !important;
  }

  /* 确保错误显示器也在 RPG 游戏容器中，但保持原有样式 */
  .rpg-game-container :global(#errorPrinter) {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 10 !important;
    /* 不强制设置背景色、尺寸等，让 RPG Maker 自己控制 */
  }

  /* 其他 RPG Maker 元素的默认样式 */
  .rpg-game-container :global(*:not(#gameCanvas):not(#gameVideo):not(#errorPrinter)) {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
  }

  .canvas-placeholder {
    text-align: center;
    padding: var(--spacing-6);
    color: var(--theme-text-secondary);
  }

  .canvas-placeholder p {
    margin: 0 0 var(--spacing-3) 0;
    font-size: var(--font-size-base);
  }

  .canvas-placeholder button {
    padding: var(--spacing-2) var(--spacing-4);
    background: var(--theme-primary);
    color: var(--theme-text-inverse);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: var(--transition-base);
  }

  .canvas-placeholder button:hover {
    background: var(--theme-primary-dark);
  }
</style>