/**
 * 快捷键管理模块
 * 处理全局快捷键事件
 */

import { saveOperationRecords } from './filePro';
import { get } from 'svelte/store';
import { globalObjectState } from './stores/objectState';

/**
 * 快捷键配置接口
 */
interface ShortcutConfig {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  action: () => void;
  description: string;
}

/**
 * 快捷键管理器类
 */
class ShortcutKeyManager {
  private shortcuts: Map<string, ShortcutConfig> = new Map();
  private isInitialized = false;

  /**
   * 初始化快捷键管理器
   */
  init(): void {
    if (this.isInitialized) {
      console.warn('ShortcutKeyManager: 已经初始化过了');
      return;
    }

    console.log('ShortcutKeyManager: 开始初始化快捷键系统');

    // 注册默认快捷键
    this.registerDefaultShortcuts();

    // 添加全局键盘事件监听器
    document.addEventListener('keydown', this.handleKeyDown.bind(this));

    this.isInitialized = true;
    console.log('ShortcutKeyManager: 快捷键系统初始化完成');
  }

  /**
   * 注册默认快捷键
   */
  private registerDefaultShortcuts(): void {
    console.log('ShortcutKeyManager: 注册默认快捷键');

    // Ctrl+S: 收集操作记录
    this.register({
      key: 's',
      ctrl: true,
      action: this.handleGenerateCode.bind(this),
      description: '收集操作记录'
    });

    console.log('ShortcutKeyManager: 默认快捷键注册完成');
  }

  /**
   * 注册快捷键
   */
  register(config: ShortcutConfig): void {
    const key = this.getShortcutKey(config);
    this.shortcuts.set(key, config);
    console.log(`ShortcutKeyManager: 注册快捷键 ${key} - ${config.description}`);
  }

  /**
   * 注销快捷键
   */
  unregister(config: Omit<ShortcutConfig, 'action' | 'description'>): void {
    const key = this.getShortcutKey(config);
    if (this.shortcuts.delete(key)) {
      console.log(`ShortcutKeyManager: 注销快捷键 ${key}`);
    }
  }

  /**
   * 生成快捷键标识符
   */
  private getShortcutKey(config: Omit<ShortcutConfig, 'action' | 'description'>): string {
    const parts: string[] = [];

    if (config.ctrl) parts.push('ctrl');
    if (config.alt) parts.push('alt');
    if (config.shift) parts.push('shift');
    parts.push(config.key.toLowerCase());

    return parts.join('+');
  }

  /**
   * 处理键盘按下事件
   */
  private handleKeyDown(event: KeyboardEvent): void {
    // 检查是否在输入框中
    if (this.isInputElement(event.target as Element)) {
      return;
    }

    // 构建当前按键组合
    const currentKey = this.getCurrentKey(event);

    // 查找匹配的快捷键
    const shortcut = this.shortcuts.get(currentKey);
    if (shortcut) {
      console.log(`ShortcutKeyManager: 触发快捷键 ${currentKey} - ${shortcut.description}`);

      // 阻止默认行为
      event.preventDefault();
      event.stopPropagation();

      // 执行快捷键动作
      try {
        shortcut.action();
      } catch (error) {
        console.error(`ShortcutKeyManager: 执行快捷键 ${currentKey} 时出错:`, error);
      }
    }
  }

  /**
   * 获取当前按键组合
   */
  private getCurrentKey(event: KeyboardEvent): string {
    const parts: string[] = [];

    if (event.ctrlKey) parts.push('ctrl');
    if (event.altKey) parts.push('alt');
    if (event.shiftKey) parts.push('shift');
    parts.push(event.key.toLowerCase());

    return parts.join('+');
  }

  /**
   * 检查是否在输入元素中
   */
  private isInputElement(element: Element | null): boolean {
    if (!element) return false;

    const tagName = element.tagName.toLowerCase();
    const isInput = tagName === 'input' || tagName === 'textarea';
    const isContentEditable = element.getAttribute('contenteditable') === 'true';
    const isInInputContainer = element.closest('.MuiTextField-root') !== null ||
      element.closest('[contenteditable="true"]') !== null;

    return isInput || isContentEditable || isInInputContainer;
  }

  /**
   * 处理生成代码快捷键 (Ctrl+S)
   */
  private async handleGenerateCode(): Promise<void> {
    console.log('ShortcutKeyManager: 执行收集操作记录操作 (Ctrl+S)');

    try {
      // 1. 从 store 中获取 rootObject
      const currentState = get(globalObjectState);

      if (!currentState.rootObject) {
        console.warn('没有根对象，无法收集操作记录');
        return;
      }


      console.log('=== 操作记录收集完成 ===');

      // 3. 保存操作记录（使用 filePro 统一处理）
      await saveOperationRecords();

    } catch (error) {
      console.error('ShortcutKeyManager: 收集操作记录时出错:', error);
    }
  }

  /**
   * 获取所有已注册的快捷键
   */
  getRegisteredShortcuts(): Array<{ key: string; description: string }> {
    return Array.from(this.shortcuts.entries()).map(([key, config]) => ({
      key,
      description: config.description
    }));
  }

  /**
   * 销毁快捷键管理器
   */
  destroy(): void {
    if (!this.isInitialized) return;

    console.log('ShortcutKeyManager: 销毁快捷键系统');

    // 移除事件监听器
    document.removeEventListener('keydown', this.handleKeyDown.bind(this));

    // 清空快捷键
    this.shortcuts.clear();

    this.isInitialized = false;
    console.log('ShortcutKeyManager: 快捷键系统已销毁');
  }
}

/**
 * 全局快捷键管理器实例
 */
const shortcutKeyManager = new ShortcutKeyManager();

/**
 * 初始化快捷键系统
 */
export function initShortcutKeys(): void {
  shortcutKeyManager.init();
}

/**
 * 注册快捷键
 */
export function registerShortcut(config: ShortcutConfig): void {
  shortcutKeyManager.register(config);
}

/**
 * 注销快捷键
 */
export function unregisterShortcut(config: Omit<ShortcutConfig, 'action' | 'description'>): void {
  shortcutKeyManager.unregister(config);
}

/**
 * 获取所有已注册的快捷键
 */
export function getRegisteredShortcuts(): Array<{ key: string; description: string }> {
  return shortcutKeyManager.getRegisteredShortcuts();
}

/**
 * 销毁快捷键系统
 */
export function destroyShortcutKeys(): void {
  shortcutKeyManager.destroy();
}

/**
 * 默认导出
 */
export default {
  init: initShortcutKeys,
  register: registerShortcut,
  unregister: unregisterShortcut,
  getRegisteredShortcuts,
  destroy: destroyShortcutKeys
};

// 模块加载时的信息输出
console.log('ShortcutKey 模块已加载');
console.log('使用 initShortcutKeys() 来初始化快捷键系统');
console.log('默认快捷键:');
console.log('  Ctrl+S - 收集操作记录');