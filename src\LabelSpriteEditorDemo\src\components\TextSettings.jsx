import { useState, useEffect, useCallback, useRef } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Paper,
  Slider,
  IconButton,
  Tooltip
} from '@mui/material';
import FormatBoldIcon from '@mui/icons-material/FormatBold';
import FormatItalicIcon from '@mui/icons-material/FormatItalic';
import { SketchPicker } from 'react-color';
import useEditorStore from '../store/editorStore';

const TextSettings = () => {
  // 只订阅需要的方法，避免拖拽时的重新渲染
  const redraw = useEditorStore((state) => state.redraw);

  // 获取当前sprite
  const getCurrentSprite = () => {
    if (window.SpriteEditor && window.SpriteEditor.currentSprite) {
      return window.SpriteEditor.currentSprite;
    }
    return null;
  };

  // 状态
  const [fontFace, setFontFace] = useState('sans-serif');
  const [fontSize, setFontSize] = useState(16);
  const [fontBold, setFontBold] = useState(false);
  const [fontItalic, setFontItalic] = useState(false);
  const [textColor, setTextColor] = useState('#ffffff');
  const [outlineColor, setOutlineColor] = useState('rgba(0, 0, 0, 0.5)');
  const [outlineWidth, setOutlineWidth] = useState(3);
  const [showTextColorPicker, setShowTextColorPicker] = useState(false);
  const [showOutlineColorPicker, setShowOutlineColorPicker] = useState(false);
  // 字体选项
  const [fontOptions, setFontOptions] = useState([]);

  // 🎯 重试计数器和开始时间
  const retryCountRef = useRef(0);
  const startTimeRef = useRef(Date.now());

  // 🎯 从sprite.bitmap获取属性的函数（使用useCallback优化）
  const loadFontSettingsFromSprite = useCallback(() => {
    console.log("检查全局字体设置", {
      globalFonts: window.SpriteEditor?.globalFonts,
      setGlobalFonts: window.SpriteEditor?.setGlobalFonts
    });

    // 🎯 兼容两种字体设置方式
    let fontsToUse = null;

    // 优先使用 globalFonts（通过setExternalSpriteForEdit设置）
    if (window.SpriteEditor?.globalFonts && window.SpriteEditor.globalFonts.length > 0) {
      fontsToUse = window.SpriteEditor.globalFonts;
      console.log("发现globalFonts字体设置");
    }
    // 兼容 setGlobalFonts（直接属性赋值）
    else if (window.SpriteEditor?.setGlobalFonts && Array.isArray(window.SpriteEditor.setGlobalFonts) && window.SpriteEditor.setGlobalFonts.length > 0) {
      fontsToUse = window.SpriteEditor.setGlobalFonts;
      console.log("发现setGlobalFonts字体设置");
    }

    if (fontsToUse) {
      console.log("更新字体选项:", fontsToUse);
      setFontOptions(fontsToUse);
    }

    const sprite = getCurrentSprite();
    if (sprite && sprite.bitmap) {
      // 🎯 重置重试计数器（成功获取到sprite）
      retryCountRef.current = 0;
      startTimeRef.current = Date.now();

      // 🎯 严格检查：必须有fontFace才能设置
      if (!sprite.bitmap.fontFace) {
        console.error('❌ sprite.bitmap.fontFace 未设置，无法加载字体设置');
        console.error('当前bitmap状态:', {
          fontFace: sprite.bitmap.fontFace,
          fontSize: sprite.bitmap.fontSize,
          textColor: sprite.bitmap.textColor,
          hasElements: !!sprite.bitmap.elements
        });
        throw new Error('sprite.bitmap.fontFace 未设置，请确保bitmap已正确初始化');
      }

      // 检查字体是否在可用选项中
      if (fontsToUse && !fontsToUse.includes(sprite.bitmap.fontFace)) {
        console.warn('⚠️ bitmap字体不在可用选项中:', sprite.bitmap.fontFace, '可用字体:', fontsToUse);
      }

      console.log('✅ 从bitmap加载字体设置:', sprite.bitmap.fontFace);

      // 🎯 处理字体设置：如果是字体列表，选择第一个可用的字体
      let targetFontFace = sprite.bitmap.fontFace;

      // 检查是否是字体列表（包含逗号）
      if (targetFontFace && targetFontFace.includes(',')) {
        console.log('检测到字体列表:', targetFontFace);

        // 分割字体列表并清理空格
        const fontList = targetFontFace.split(',').map(font => font.trim());
        console.log('分割后的字体列表:', fontList);

        // 在可用字体选项中查找第一个匹配的字体
        let foundFont = null;
        if (fontsToUse && fontsToUse.length > 0) {
          for (const font of fontList) {
            if (fontsToUse.includes(font)) {
              foundFont = font;
              console.log('在字体选项中找到匹配字体:', font);
              break;
            }
          }
        }

        // 如果找到匹配的字体，使用它；否则使用列表中的第一个
        targetFontFace = foundFont || fontList[0];
        console.log('最终选择的字体:', targetFontFace);
      }

      // 检查选择的字体是否在可用选项中
      if (fontsToUse && !fontsToUse.includes(targetFontFace)) {
        console.warn('⚠️ 选择的字体不在可用选项中:', targetFontFace, '可用字体:', fontsToUse);
        // 如果不在选项中，使用第一个可用字体
        if (fontsToUse.length > 0) {
          targetFontFace = fontsToUse[0];
          console.log('使用第一个可用字体:', targetFontFace);
        }
      }

      // 🎯 严格使用处理后的字体值
      setFontFace(targetFontFace);
      setFontSize(sprite.bitmap.fontSize);
      setFontBold(sprite.bitmap.fontBold);
      setFontItalic(sprite.bitmap.fontItalic);
      setTextColor(sprite.bitmap.textColor);
      setOutlineColor(sprite.bitmap.outlineColor);
      setOutlineWidth(sprite.bitmap.outlineWidth);

      console.log('TextSettings已从sprite.bitmap加载字体设置:', {
        originalFontFace: sprite.bitmap.fontFace,
        selectedFontFace: targetFontFace,
        fontSize: sprite.bitmap.fontSize,
        fontBold: sprite.bitmap.fontBold,
        fontItalic: sprite.bitmap.fontItalic,
        textColor: sprite.bitmap.textColor,
        outlineColor: sprite.bitmap.outlineColor,
        outlineWidth: sprite.bitmap.outlineWidth
      });
    } else {
      // 🎯 多次重试逻辑：10秒内没有获取到才报错
      retryCountRef.current++;
      const elapsedTime = Date.now() - startTimeRef.current;

      console.warn(`⚠️ 第${retryCountRef.current}次尝试获取sprite失败，已耗时${Math.round(elapsedTime / 1000)}秒`);

      if (elapsedTime >= 10000) { // 10秒
        console.error('❌ 10秒内无法获取sprite或sprite.bitmap，停止重试');
        console.error('重试统计:', {
          totalRetries: retryCountRef.current,
          elapsedTime: Math.round(elapsedTime / 1000) + '秒',
          spriteExists: !!getCurrentSprite(),
          bitmapExists: !!(getCurrentSprite()?.bitmap)
        });
        throw new Error(`10秒内无法获取sprite或sprite.bitmap（共重试${retryCountRef.current}次），请确保sprite已正确初始化`);
      } else {
        console.log(`🔄 将继续重试，剩余时间：${Math.round((10000 - elapsedTime) / 1000)}秒`);
      }
    }
  }, []);

  // 初始化时从sprite.bitmap获取属性
  useEffect(() => {
    loadFontSettingsFromSprite();
  }, [loadFontSettingsFromSprite]);

  // 🎯 优化的sprite监听逻辑
  useEffect(() => {
    let interval = null;
    let isInitialized = false;

    const handleSpriteChange = () => {
      console.log('检测到sprite变化，重新加载字体设置');
      loadFontSettingsFromSprite();
    };

    // 🎯 监听全局字体更新事件
    const handleGlobalFontsUpdate = (event) => {
      console.log('检测到全局字体更新:', event.detail.fonts);
      setFontOptions(event.detail.fonts);

      // 字体选项更新后，如果定时器还在运行且已有bitmap属性，可以停止定时器
      if (interval && !isInitialized) {
        const sprite = getCurrentSprite();
        if (sprite && sprite.bitmap && sprite.bitmap.fontFace) {
          console.log('全局字体已更新且bitmap属性已设置，停止定时器');
          isInitialized = true;
          clearInterval(interval);
          interval = null;
        }
      }
    };

    // 监听自定义事件，当sprite更新时重新加载设置
    document.addEventListener('spriteUpdated', handleSpriteChange);
    document.addEventListener('globalFontsUpdated', handleGlobalFontsUpdate);

    // 🎯 优化的定期检查逻辑
    interval = setInterval(() => {
      const sprite = getCurrentSprite();
      if (sprite && sprite.bitmap) {
        // 检查字体属性是否已设置完成
        const hasFontSettings = sprite.bitmap.fontFace &&
          sprite.bitmap.fontSize &&
          sprite.bitmap.textColor;

        // 🎯 当字体选项不为空且bitmap属性设置完成后，清除定时器
        if (fontOptions.length > 0 && hasFontSettings && !isInitialized) {
          console.log('字体选项已加载且bitmap属性已设置完成，停止定时器');
          console.log('当前字体设置:', {
            fontFace: sprite.bitmap.fontFace,
            fontSize: sprite.bitmap.fontSize,
            fontBold: sprite.bitmap.fontBold,
            fontItalic: sprite.bitmap.fontItalic,
            textColor: sprite.bitmap.textColor,
            outlineColor: sprite.bitmap.outlineColor,
            outlineWidth: sprite.bitmap.outlineWidth
          });

          isInitialized = true;
          clearInterval(interval);
          interval = null;

          return;
        }

        // 🎯 检查是否有新的字体设置需要加载（兼容两种方式）
        if (fontOptions.length === 0) {
          let fontsToLoad = null;

          // 优先检查 globalFonts
          if (window.SpriteEditor?.globalFonts && window.SpriteEditor.globalFonts.length > 0) {
            fontsToLoad = window.SpriteEditor.globalFonts;
          }
          // 兼容 setGlobalFonts
          else if (window.SpriteEditor?.setGlobalFonts && Array.isArray(window.SpriteEditor.setGlobalFonts) && window.SpriteEditor.setGlobalFonts.length > 0) {
            fontsToLoad = window.SpriteEditor.setGlobalFonts;
          }

          if (fontsToLoad) {
            console.log('发现全局字体设置，更新字体选项:', fontsToLoad);
            setFontOptions(fontsToLoad);

            // 🎯 严格检查：只有当bitmap有fontFace时才设置
            if (sprite.bitmap.fontFace) {
              console.log('设置bitmap中的字体:', sprite.bitmap.fontFace);
              setFontFace(sprite.bitmap.fontFace);
            } else {
              console.warn('⚠️ bitmap.fontFace 未设置，无法设置字体');
            }
          }
        }

        // 🎯 严格检查是否需要从bitmap加载字体属性
        if (sprite.bitmap.fontFace && sprite.bitmap.fontFace !== fontFace) {
          console.log('检测到bitmap字体属性变化，重新加载:', sprite.bitmap.fontFace);
          loadFontSettingsFromSprite();
        } else if (!sprite.bitmap.fontFace) {
          console.warn('⚠️ sprite.bitmap.fontFace 未设置，跳过字体加载');
        }
      }
    }, 1000);

    return () => {
      document.removeEventListener('spriteUpdated', handleSpriteChange);
      document.removeEventListener('globalFontsUpdated', handleGlobalFontsUpdate);
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [fontFace, fontOptions, loadFontSettingsFromSprite]);

  // 更新bitmap属性
  const updateSettings = (property, value) => {
    const sprite = getCurrentSprite();
    if (sprite && sprite.bitmap) {
      // 直接设置bitmap的属性
      sprite.bitmap[property] = value;

      // 如果有现有的文本元素，更新它们
      if (sprite.bitmap.elements) {
        sprite.bitmap.elements.forEach(element => {
          if (element.type === 'text') {
            // 只更新未明确设置的属性
            if (property === 'fontFace' && !element.fontFace) {
              element.fontFace = value;
            } else if (property === 'fontSize' && !element.fontSize) {
              element.fontSize = value;
            } else if (property === 'fontBold' && element.fontBold === undefined) {
              element.fontBold = value;
            } else if (property === 'fontItalic' && element.fontItalic === undefined) {
              element.fontItalic = value;
            } else if (property === 'textColor' && !element.textColor) {
              element.textColor = value;
            } else if (property === 'outlineColor' && !element.outlineColor) {
              element.outlineColor = value;
            } else if (property === 'outlineWidth' && element.outlineWidth === undefined) {
              element.outlineWidth = value;
            }
          }
        });

        // 重新绘制
        redraw();
      }
    }
  };

  // 处理字体变更
  const handleFontFaceChange = (e) => {
    const value = e.target.value;
    setFontFace(value);
    updateSettings('fontFace', value);
  };

  // 处理字体大小变更
  const handleFontSizeChange = (_, value) => {
    setFontSize(value);
    updateSettings('fontSize', value);
  };

  // 处理粗体变更
  const handleFontBoldChange = () => {
    const newValue = !fontBold;
    setFontBold(newValue);
    updateSettings('fontBold', newValue);
  };

  // 处理斜体变更
  const handleFontItalicChange = () => {
    const newValue = !fontItalic;
    setFontItalic(newValue);
    updateSettings('fontItalic', newValue);
  };

  // 处理文本颜色变更
  const handleTextColorChange = (color) => {
    setTextColor(color.hex);
    updateSettings('textColor', color.hex);
  };

  // 处理轮廓颜色变更
  const handleOutlineColorChange = (color) => {
    setOutlineColor(color.hex);
    updateSettings('outlineColor', color.hex);
  };

  // 处理轮廓宽度变更
  const handleOutlineWidthChange = (_, value) => {
    setOutlineWidth(value);
    updateSettings('outlineWidth', value);
  };

  return (
    <Paper sx={{ p: 1, mb: 1 }}>
      <Box sx={{ display: 'flex', flexDirection: 'row', gap: 1, justifyContent: 'space-between', alignItems: 'center' }}>
        {/* 字体 */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <FormControl fullWidth size="small">
            <InputLabel id="font-face-label" sx={{ fontSize: '0.75rem' }}>字体</InputLabel>
            <Select
              labelId="font-face-label"
              value={fontFace}
              onChange={handleFontFaceChange}
              label="字体"
              size="small"
              sx={{ fontSize: '0.75rem' }}
            >
              {fontOptions.map(font => (
                <MenuItem key={font} value={font} sx={{ fontSize: '0.75rem' }}>
                  {font}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        {/* 字体大小 */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Typography variant="caption" sx={{ display: 'block', textAlign: 'center', fontSize: '0.75rem' }}>
            字体大小: {fontSize}
          </Typography>
          <Slider
            value={fontSize}
            onChange={handleFontSizeChange}
            min={8}
            max={72}
            step={1}
            size="small"
          />
        </Box>

        {/* 粗体/斜体图标 */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Tooltip title="粗体">
            <IconButton
              onClick={handleFontBoldChange}
              color={fontBold ? "primary" : "default"}
              size="small"
              sx={{
                border: fontBold ? '1px solid #1976d2' : '1px solid #ddd',
                mr: 0.5,
                backgroundColor: fontBold ? 'rgba(25, 118, 210, 0.1)' : 'transparent',
                padding: '4px'
              }}
            >
              <FormatBoldIcon fontSize="small" />
            </IconButton>
          </Tooltip>

          <Tooltip title="斜体">
            <IconButton
              onClick={handleFontItalicChange}
              color={fontItalic ? "primary" : "default"}
              size="small"
              sx={{
                border: fontItalic ? '1px solid #1976d2' : '1px solid #ddd',
                backgroundColor: fontItalic ? 'rgba(25, 118, 210, 0.1)' : 'transparent',
                padding: '4px'
              }}
            >
              <FormatItalicIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>

        {/* 文本颜色 */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Typography variant="caption" sx={{ display: 'block', textAlign: 'center', fontSize: '0.75rem' }}>
            文本颜色
          </Typography>
          <Box
            sx={{
              height: '24px',
              backgroundColor: textColor,
              border: '1px solid #ddd',
              cursor: 'pointer',
              borderRadius: '2px'
            }}
            onClick={() => setShowTextColorPicker(!showTextColorPicker)}
          />
          {showTextColorPicker && (
            <Box sx={{ position: 'absolute', zIndex: 2 }}>
              <Box
                sx={{
                  position: 'fixed',
                  top: 0,
                  right: 0,
                  bottom: 0,
                  left: 0,
                }}
                onClick={() => setShowTextColorPicker(false)}
              />
              <SketchPicker
                color={textColor}
                onChange={handleTextColorChange}
              />
            </Box>
          )}
        </Box>

        {/* 轮廓颜色 */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Typography variant="caption" sx={{ display: 'block', textAlign: 'center', fontSize: '0.75rem' }}>
            轮廓颜色
          </Typography>
          <Box
            sx={{
              height: '24px',
              backgroundColor: outlineColor,
              border: '1px solid #ddd',
              cursor: 'pointer',
              borderRadius: '2px'
            }}
            onClick={() => setShowOutlineColorPicker(!showOutlineColorPicker)}
          />
          {showOutlineColorPicker && (
            <Box sx={{ position: 'absolute', zIndex: 2 }}>
              <Box
                sx={{
                  position: 'fixed',
                  top: 0,
                  right: 0,
                  bottom: 0,
                  left: 0,
                }}
                onClick={() => setShowOutlineColorPicker(false)}
              />
              <SketchPicker
                color={outlineColor}
                onChange={handleOutlineColorChange}
              />
            </Box>
          )}
        </Box>

        {/* 轮廓宽度 */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Typography variant="caption" sx={{ display: 'block', textAlign: 'center', fontSize: '0.75rem' }}>
            轮廓宽度: {outlineWidth}
          </Typography>
          <Slider
            value={outlineWidth}
            onChange={handleOutlineWidthChange}
            min={0}
            max={10}
            step={0.5}
            size="small"
          />
        </Box>
      </Box>
    </Paper>
  );
};

export default TextSettings;
