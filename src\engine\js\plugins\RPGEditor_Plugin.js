// Scene_Map 编辑器插件
// 地图ID: 1
// 生成时间: 2025-06-12T07:04:23.308Z
// 对象数量: 1

(() => {
  'use strict';

  // === 重写 Scene_Map.prototype.createDisplayObjects ===
  // 地图ID: 1
  Scene_Map.prototype.createDisplayObjects = function () {
    // 创建精灵集
    this.createSpriteset();

    // 创建窗口层
    this.createWindowLayer();

    // === 编辑器创建的对象 ===

    // 添加到地图层的对象
    // 创建 Spriteset_Map
    const _spriteset = new Spriteset_Map();
    this._spriteset._tilemap.addChild(_spriteset);
    // 创建 Sprite
    const _baseSprite = new Sprite();
    this._spriteset._tilemap.addChild(_baseSprite);
    // 创建 ScreenSprite
    const screensprite_1749711863310_me3xbk = new ScreenSprite();
    this._spriteset._tilemap.addChild(screensprite_1749711863310_me3xbk);
    // 创建 Graphics
    const _graphics = new PIXI.Graphics();
    this._spriteset._tilemap.addChild(_graphics);


    // 创建 TilingSprite
    const tilingsprite_1749711863311_aktxhf = new PIXI.TilingSprite();
    this._spriteset._tilemap.addChild(tilingsprite_1749711863311_aktxhf);

    // 创建 Tilemap
    const tilemap_1749711863311_mut4ey = new Tilemap();
    // 设置基础属性
    tilemap_1749711863311_mut4ey.width = 816;
    tilemap_1749711863311_mut4ey.height = 624;
    this._spriteset._tilemap.addChild(tilemap_1749711863311_mut4ey);
    // 创建 
    const _lowerLayer = new ();
    // 设置基础属性
    _lowerLayer.x = -48;
    _lowerLayer.y = -48;
    this._spriteset._tilemap.addChild(_lowerLayer);
    // 创建 
    const _1749711863312_dt5ytw = new ();
    this._spriteset._tilemap.addChild(_1749711863312_dt5ytw);

    // 创建 
    const _1749711863313_lvv749 = new ();
    this._spriteset._tilemap.addChild(_1749711863313_lvv749);


    // 创建 Sprite_Character
    const sprite_character_1749711863313_r5xgwt = new Sprite_Character();
    // 设置基础属性
    sprite_character_1749711863313_r5xgwt.anchor.x = 0.5;
    sprite_character_1749711863313_r5xgwt.anchor.y = 1;
    sprite_character_1749711863313_r5xgwt.x = 24;
    sprite_character_1749711863313_r5xgwt.y = 42;
    sprite_character_1749711863313_r5xgwt.visible = false;
    sprite_character_1749711863313_r5xgwt.bitmap = ImageManager.loadBitmap('img/characters/', 'Vehicle');
    this._spriteset._tilemap.addChild(sprite_character_1749711863313_r5xgwt);

    // 创建 Sprite_Character
    const sprite_character_1749711863315_fc6usn = new Sprite_Character();
    // 设置基础属性
    sprite_character_1749711863315_fc6usn.anchor.x = 0.5;
    sprite_character_1749711863315_fc6usn.anchor.y = 1;
    sprite_character_1749711863315_fc6usn.x = 24;
    sprite_character_1749711863315_fc6usn.y = 42;
    sprite_character_1749711863315_fc6usn.visible = false;
    sprite_character_1749711863315_fc6usn.bitmap = ImageManager.loadBitmap('img/characters/', 'Vehicle');
    this._spriteset._tilemap.addChild(sprite_character_1749711863315_fc6usn);

    // 创建 Sprite_Character
    const sprite_character_1749711863316_8sfen9 = new Sprite_Character();
    // 设置基础属性
    sprite_character_1749711863316_8sfen9.anchor.x = 0.5;
    sprite_character_1749711863316_8sfen9.anchor.y = 1;
    sprite_character_1749711863316_8sfen9.x = 24;
    sprite_character_1749711863316_8sfen9.y = 42;
    sprite_character_1749711863316_8sfen9.visible = false;
    sprite_character_1749711863316_8sfen9.bitmap = ImageManager.loadBitmap('img/characters/', 'Vehicle');
    this._spriteset._tilemap.addChild(sprite_character_1749711863316_8sfen9);

    // 创建 Sprite_Character
    const sprite_character_1749711863317_evr161 = new Sprite_Character();
    // 设置基础属性
    sprite_character_1749711863317_evr161.anchor.x = 0.5;
    sprite_character_1749711863317_evr161.anchor.y = 1;
    sprite_character_1749711863317_evr161.x = 72;
    sprite_character_1749711863317_evr161.y = 90;
    sprite_character_1749711863317_evr161.bitmap = ImageManager.loadBitmap('img/characters/', 'Damage2');
    this._spriteset._tilemap.addChild(sprite_character_1749711863317_evr161);

    // 创建 Sprite_Character
    const sprite_character_1749711863318_cynt52 = new Sprite_Character();
    // 设置基础属性
    sprite_character_1749711863318_cynt52.anchor.x = 0.5;
    sprite_character_1749711863318_cynt52.anchor.y = 1;
    sprite_character_1749711863318_cynt52.x = 408;
    sprite_character_1749711863318_cynt52.y = 330;
    sprite_character_1749711863318_cynt52.bitmap = ImageManager.loadBitmap('img/characters/', 'Actor1');
    this._spriteset._tilemap.addChild(sprite_character_1749711863318_cynt52);

    // 创建 Sprite_Character
    const sprite_character_1749711863319_e93she = new Sprite_Character();
    // 设置基础属性
    sprite_character_1749711863319_e93she.anchor.x = 0.5;
    sprite_character_1749711863319_e93she.anchor.y = 1;
    sprite_character_1749711863319_e93she.x = 408;
    sprite_character_1749711863319_e93she.y = 330;
    sprite_character_1749711863319_e93she.bitmap = ImageManager.loadBitmap('img/characters/', 'Actor1');
    this._spriteset._tilemap.addChild(sprite_character_1749711863319_e93she);

    // 创建 Sprite_Character
    const sprite_character_1749711863320_91bntv = new Sprite_Character();
    // 设置基础属性
    sprite_character_1749711863320_91bntv.anchor.x = 0.5;
    sprite_character_1749711863320_91bntv.anchor.y = 1;
    sprite_character_1749711863320_91bntv.x = 408;
    sprite_character_1749711863320_91bntv.y = 330;
    sprite_character_1749711863320_91bntv.bitmap = ImageManager.loadBitmap('img/characters/', 'Actor1');
    this._spriteset._tilemap.addChild(sprite_character_1749711863320_91bntv);

    // 创建 Sprite_Character
    const sprite_character_1749711863320_wthdyt = new Sprite_Character();
    // 设置基础属性
    sprite_character_1749711863320_wthdyt.anchor.x = 0.5;
    sprite_character_1749711863320_wthdyt.anchor.y = 1;
    sprite_character_1749711863320_wthdyt.x = 408;
    sprite_character_1749711863320_wthdyt.y = 330;
    sprite_character_1749711863320_wthdyt.bitmap = ImageManager.loadBitmap('img/characters/', 'Actor1');
    this._spriteset._tilemap.addChild(sprite_character_1749711863320_wthdyt);

    // 创建 
    const _upperLayer = new ();
    // 设置基础属性
    _upperLayer.x = -48;
    _upperLayer.y = -48;
    this._spriteset._tilemap.addChild(_upperLayer);
    // 创建 
    const _1749711863322_f5nsk6 = new ();
    this._spriteset._tilemap.addChild(_1749711863322_f5nsk6);

    // 创建 
    const _1749711863322_3tr02m = new ();
    this._spriteset._tilemap.addChild(_1749711863322_3tr02m);


    // 创建 Sprite
    const sprite_1749711863323_e4gcmy = new Sprite();
    // 设置基础属性
    sprite_1749711863323_e4gcmy.anchor.x = 0.5;
    sprite_1749711863323_e4gcmy.anchor.y = 1;
    sprite_1749711863323_e4gcmy.width = 48;
    sprite_1749711863323_e4gcmy.height = 48;
    sprite_1749711863323_e4gcmy.x = 24;
    sprite_1749711863323_e4gcmy.y = 42;
    sprite_1749711863323_e4gcmy.alpha = 0;
    sprite_1749711863323_e4gcmy.bitmap = ImageManager.loadBitmap('img/system/', 'Shadow1');
    this._spriteset._tilemap.addChild(sprite_1749711863323_e4gcmy);

    // 创建 Sprite_Destination
    const sprite_destination_1749711863324_ryts94 = new Sprite_Destination();
    // 设置基础属性
    sprite_destination_1749711863324_ryts94.anchor.x = 0.5;
    sprite_destination_1749711863324_ryts94.anchor.y = 0.5;
    sprite_destination_1749711863324_ryts94.visible = false;
    this._spriteset._tilemap.addChild(sprite_destination_1749711863324_ryts94);



    // 创建 Weather
    const _weather = new Weather();
    this._spriteset._tilemap.addChild(_weather);
    // 创建 ScreenSprite
    const _dimmerSprite = new ScreenSprite();
    // 设置基础属性
    _dimmerSprite.alpha = 0;
    this._spriteset._tilemap.addChild(_dimmerSprite);
    // 创建 Graphics
    const _graphics = new PIXI.Graphics();
    this._spriteset._tilemap.addChild(_graphics);



    // 创建 Sprite
    const _pictureContainer = new Sprite();
    // 设置基础属性
    _pictureContainer.width = 816;
    _pictureContainer.height = 624;
    this._spriteset._tilemap.addChild(_pictureContainer);
    // 创建 Sprite_Picture
    const sprite_picture_1749711863327_w15rgh = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863327_w15rgh.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863327_w15rgh);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863327_ncuvlw = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863327_ncuvlw.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863327_ncuvlw);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863327_nyfxn6 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863327_nyfxn6.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863327_nyfxn6);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863328_v05zw8 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863328_v05zw8.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863328_v05zw8);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863328_0q2o7b = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863328_0q2o7b.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863328_0q2o7b);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863329_6ks0p5 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863329_6ks0p5.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863329_6ks0p5);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863329_zrfk8r = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863329_zrfk8r.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863329_zrfk8r);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863330_d8np20 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863330_d8np20.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863330_d8np20);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863330_r37v6h = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863330_r37v6h.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863330_r37v6h);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863330_pb8tfe = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863330_pb8tfe.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863330_pb8tfe);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863331_ffhf6v = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863331_ffhf6v.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863331_ffhf6v);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863331_yumejs = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863331_yumejs.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863331_yumejs);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863332_45a4pp = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863332_45a4pp.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863332_45a4pp);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863332_gofz7h = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863332_gofz7h.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863332_gofz7h);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863333_usm187 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863333_usm187.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863333_usm187);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863333_cuf6xf = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863333_cuf6xf.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863333_cuf6xf);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863333_kch93k = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863333_kch93k.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863333_kch93k);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863334_6u8sz8 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863334_6u8sz8.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863334_6u8sz8);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863334_ktfqpb = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863334_ktfqpb.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863334_ktfqpb);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863335_ijif5g = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863335_ijif5g.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863335_ijif5g);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863335_8pais4 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863335_8pais4.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863335_8pais4);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863336_d04qgh = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863336_d04qgh.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863336_d04qgh);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863336_vx93ea = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863336_vx93ea.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863336_vx93ea);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863337_gkixim = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863337_gkixim.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863337_gkixim);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863337_2a5gse = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863337_2a5gse.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863337_2a5gse);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863337_v2pel6 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863337_v2pel6.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863337_v2pel6);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863338_mzm3mt = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863338_mzm3mt.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863338_mzm3mt);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863338_7onnus = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863338_7onnus.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863338_7onnus);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863339_llpall = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863339_llpall.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863339_llpall);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863339_ise0bw = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863339_ise0bw.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863339_ise0bw);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863340_9v59sq = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863340_9v59sq.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863340_9v59sq);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863340_hs00us = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863340_hs00us.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863340_hs00us);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863341_lu75c3 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863341_lu75c3.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863341_lu75c3);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863341_d9a909 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863341_d9a909.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863341_d9a909);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863342_z6amtu = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863342_z6amtu.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863342_z6amtu);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863342_hg9wc4 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863342_hg9wc4.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863342_hg9wc4);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863343_wnn75p = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863343_wnn75p.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863343_wnn75p);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863343_ehwg2g = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863343_ehwg2g.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863343_ehwg2g);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863344_5mnhjx = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863344_5mnhjx.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863344_5mnhjx);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863344_ez4bvk = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863344_ez4bvk.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863344_ez4bvk);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863344_mxd9n2 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863344_mxd9n2.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863344_mxd9n2);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863345_2hgyjv = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863345_2hgyjv.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863345_2hgyjv);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863345_x5d78w = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863345_x5d78w.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863345_x5d78w);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863346_yyzahm = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863346_yyzahm.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863346_yyzahm);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863346_thp82i = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863346_thp82i.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863346_thp82i);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863347_hzr8pn = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863347_hzr8pn.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863347_hzr8pn);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863347_o562w5 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863347_o562w5.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863347_o562w5);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863348_4ltssa = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863348_4ltssa.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863348_4ltssa);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863348_pn9s0q = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863348_pn9s0q.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863348_pn9s0q);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863349_w6gvut = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863349_w6gvut.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863349_w6gvut);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863349_e9hltp = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863349_e9hltp.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863349_e9hltp);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863349_m0q2db = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863349_m0q2db.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863349_m0q2db);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863350_u8vlha = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863350_u8vlha.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863350_u8vlha);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863350_qhqeaj = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863350_qhqeaj.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863350_qhqeaj);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863351_74pvx9 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863351_74pvx9.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863351_74pvx9);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863351_1hxebx = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863351_1hxebx.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863351_1hxebx);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863351_29mbxr = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863351_29mbxr.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863351_29mbxr);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863352_jsxd80 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863352_jsxd80.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863352_jsxd80);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863352_5wmdxq = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863352_5wmdxq.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863352_5wmdxq);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863353_bt2j4o = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863353_bt2j4o.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863353_bt2j4o);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863353_eq3shg = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863353_eq3shg.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863353_eq3shg);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863354_2tbh4y = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863354_2tbh4y.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863354_2tbh4y);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863354_jgu20t = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863354_jgu20t.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863354_jgu20t);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863354_8gq5jy = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863354_8gq5jy.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863354_8gq5jy);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863355_cxplxg = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863355_cxplxg.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863355_cxplxg);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863355_dlp8ey = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863355_dlp8ey.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863355_dlp8ey);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863356_37yhc1 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863356_37yhc1.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863356_37yhc1);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863356_b14n4z = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863356_b14n4z.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863356_b14n4z);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863357_6et6o9 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863357_6et6o9.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863357_6et6o9);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863357_l0pz43 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863357_l0pz43.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863357_l0pz43);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863358_nz6q5q = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863358_nz6q5q.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863358_nz6q5q);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863358_voz9dj = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863358_voz9dj.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863358_voz9dj);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863358_tsp8ps = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863358_tsp8ps.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863358_tsp8ps);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863359_5sqvya = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863359_5sqvya.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863359_5sqvya);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863359_ifkc8w = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863359_ifkc8w.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863359_ifkc8w);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863360_m7tzzd = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863360_m7tzzd.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863360_m7tzzd);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863360_qve4la = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863360_qve4la.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863360_qve4la);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863360_cl0bo7 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863360_cl0bo7.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863360_cl0bo7);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863361_cmpier = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863361_cmpier.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863361_cmpier);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863361_n0cmr1 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863361_n0cmr1.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863361_n0cmr1);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863362_h9x1kp = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863362_h9x1kp.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863362_h9x1kp);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863362_x6y3n3 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863362_x6y3n3.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863362_x6y3n3);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863363_l302n5 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863363_l302n5.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863363_l302n5);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863363_ie0coa = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863363_ie0coa.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863363_ie0coa);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863363_s84fgx = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863363_s84fgx.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863363_s84fgx);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863364_9xu7h1 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863364_9xu7h1.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863364_9xu7h1);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863364_tidez3 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863364_tidez3.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863364_tidez3);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863364_s1g0ea = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863364_s1g0ea.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863364_s1g0ea);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863365_exs36o = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863365_exs36o.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863365_exs36o);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863365_zdpj31 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863365_zdpj31.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863365_zdpj31);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863366_i4bpxp = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863366_i4bpxp.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863366_i4bpxp);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863366_mg15i2 = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863366_mg15i2.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863366_mg15i2);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863366_9eivpc = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863366_9eivpc.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863366_9eivpc);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863367_vf507w = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863367_vf507w.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863367_vf507w);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863367_hgc28c = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863367_hgc28c.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863367_hgc28c);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863367_sr9qkk = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863367_sr9qkk.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863367_sr9qkk);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863368_0l39ki = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863368_0l39ki.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863368_0l39ki);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863368_zkuhah = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863368_zkuhah.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863368_zkuhah);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863369_bboo9a = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863369_bboo9a.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863369_bboo9a);

    // 创建 Sprite_Picture
    const sprite_picture_1749711863369_oyt18d = new Sprite_Picture();
    // 设置基础属性
    sprite_picture_1749711863369_oyt18d.visible = false;
    this._spriteset._tilemap.addChild(sprite_picture_1749711863369_oyt18d);


    // 创建 Sprite_Timer
    const _timerSprite = new Sprite_Timer();
    // 设置基础属性
    _timerSprite.x = 360;
    _timerSprite.visible = false;
    this._spriteset._tilemap.addChild(_timerSprite);



    // === WindowLayer UI对象 ===
    // === WindowLayer UI对象创建 ===
    // 确保WindowLayer存在
    if (!this._windowLayer) {
      this.createWindowLayer();
    }

    // 创建地图名称窗口（调用系统方法）
    this.createMapNameWindow();
    // 创建消息窗口（调用系统方法）
    this.createMessageWindow();
    // 创建滚动文本窗口（调用系统方法）
    this.createScrollTextWindow();
    // 创建金钱窗口（调用系统方法）
    this.createGoldWindow();
    // 创建姓名框窗口（调用系统方法）
    this.createNameBoxWindow();
    // 创建选择列表窗口（调用系统方法）
    this.createChoiceListWindow();
    // 创建数字输入窗口（调用系统方法）
    this.createNumberInputWindow();
    // 创建事件物品窗口（调用系统方法）
    this.createEventItemWindow();
    // 创建 Sprite_Button
    const sprite_button_1749711863374_j0paq9 = new Sprite_Button();
    // 设置基础属性
    sprite_button_1749711863374_j0paq9.x = 756;
    sprite_button_1749711863374_j0paq9.y = 2;
    sprite_button_1749711863374_j0paq9.alpha = 0.7529411764705882;
    this.addWindow(sprite_button_1749711863374_j0paq9);


    // 创建所有窗口
    this.createAllWindows();

    // 创建按钮
    this.createButtons();
  };


})();