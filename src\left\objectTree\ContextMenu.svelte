<script lang="ts">
  import type { ContextMenuData, ContextMenuItem, ObjectTreeNodeData } from './types';
  import { CreatableObjectType } from './types';

  // 导出组件以便其他文件可以导入
  export { ContextMenu as default };

  // Props
  interface Props {
    contextMenuData: ContextMenuData;
    onCreateObject?: (objectType: string, targetNode: ObjectTreeNodeData) => void;
    onDeleteObject?: (targetNode: ObjectTreeNodeData) => void;
    onClose?: () => void;
  }

  let {
    contextMenuData,
    onCreateObject,
    onDeleteObject,
    onClose
  }: Props = $props();

  // 菜单项配置
  let menuItems = $derived(() => {
    if (!contextMenuData.targetNode) return [];

    const items: ContextMenuItem[] = [];

    // 创建对象子菜单
    items.push({
      id: 'create-sprite',
      label: '创建 Sprite',
      icon: '🖼️',
      action: () => handleCreateObject(CreatableObjectType.SPRITE)
    });

    items.push({
      id: 'create-label',
      label: '创建 Label',
      icon: '📝',
      action: () => handleCreateObject(CreatableObjectType.LABEL)
    });

    items.push({
      id: 'create-container',
      label: '创建 Container',
      icon: '📦',
      action: () => handleCreateObject(CreatableObjectType.CONTAINER)
    });

    items.push({
      id: 'create-window',
      label: '创建 Window',
      icon: '🪟',
      action: () => handleCreateObject(CreatableObjectType.WINDOW)
    });

    items.push({
      id: 'create-button',
      label: '创建 Button',
      icon: '🔘',
      action: () => handleCreateObject(CreatableObjectType.BUTTON)
    });

    // 分隔线
    items.push({
      id: 'separator-1',
      label: '',
      separator: true,
      action: () => {}
    });

    // 删除对象（根节点不能删除）
    items.push({
      id: 'delete-object',
      label: '删除对象',
      icon: '🗑️',
      action: () => handleDeleteObject(),
      disabled: contextMenuData.targetNode?.isRoot || false
    });

    return items;
  });

  // 处理创建对象
  function handleCreateObject(objectType: string) {
    if (contextMenuData.targetNode) {
      onCreateObject?.(objectType, contextMenuData.targetNode);
    }
    closeMenu();
  }

  // 处理删除对象
  function handleDeleteObject() {
    if (contextMenuData.targetNode && !contextMenuData.targetNode.isRoot) {
      // 调用删除函数，不等待结果
      onDeleteObject?.(contextMenuData.targetNode);
    }
    closeMenu();
  }

  // 关闭菜单
  function closeMenu() {
    onClose?.();
  }

  // 处理点击外部区域
  function handleClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    const menu = document.querySelector('.context-menu');
    
    if (menu && !menu.contains(target)) {
      closeMenu();
    }
  }

  // 处理键盘事件
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      closeMenu();
    }
  }

  // 菜单位置样式
  let menuStyle = $derived(() => {
    if (!contextMenuData.visible) return 'display: none;';
    
    return `
      position: fixed;
      left: ${contextMenuData.x}px;
      top: ${contextMenuData.y}px;
      z-index: 1000;
    `;
  });
</script>

<!-- 全局事件监听 -->
<svelte:window
  onclick={contextMenuData.visible ? handleClickOutside : undefined}
  onkeydown={contextMenuData.visible ? handleKeydown : undefined}
/>

<!-- 右键菜单 -->
{#if contextMenuData.visible}
  <div 
    class="context-menu"
    style={menuStyle()}
    role="menu"
    aria-label="对象操作菜单"
  >
    <div class="menu-header">
      <span class="menu-title">对象操作</span>
      <span class="target-name">{contextMenuData.targetNode?.displayName || ''}</span>
    </div>

    <div class="menu-items">
      {#each menuItems() as item (item.id)}
        {#if item.separator}
          <div class="menu-separator"></div>
        {:else}
          <button
            class="menu-item"
            class:disabled={item.disabled}
            onclick={item.action}
            disabled={item.disabled}
            role="menuitem"
            aria-label={item.label}
          >
            {#if item.icon}
              <span class="menu-icon">{item.icon}</span>
            {/if}
            <span class="menu-label">{item.label}</span>
          </button>
        {/if}
      {/each}
    </div>
  </div>
{/if}

<style>
  .context-menu {
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border, #e0e0e0);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    overflow: hidden;
    font-family: var(--theme-font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
  }

  .menu-header {
    padding: 8px 12px;
    background: var(--theme-surface-variant, #f5f5f5);
    border-bottom: 1px solid var(--theme-border, #e0e0e0);
  }

  .menu-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--theme-text-secondary, #666);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .target-name {
    display: block;
    font-size: 13px;
    color: var(--theme-text, #333);
    margin-top: 2px;
    font-weight: 500;
  }

  .menu-items {
    padding: 4px 0;
  }

  .menu-item {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 13px;
    color: var(--theme-text, #333);
    transition: all 0.2s ease;
    gap: 8px;
  }

  .menu-item:hover:not(.disabled) {
    background: var(--theme-primary-light, rgba(33, 150, 243, 0.1));
    color: var(--theme-primary, #2196F3);
  }

  .menu-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    color: var(--theme-text-disabled, #999);
  }

  .menu-icon {
    font-size: 14px;
    width: 16px;
    text-align: center;
  }

  .menu-label {
    flex: 1;
    text-align: left;
  }

  .menu-separator {
    height: 1px;
    background: var(--theme-border, #e0e0e0);
    margin: 4px 0;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .context-menu {
      min-width: 180px;
    }

    .menu-item {
      padding: 10px 12px;
      font-size: 14px;
    }
  }
</style>
