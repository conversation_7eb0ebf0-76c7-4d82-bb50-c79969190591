/**
 * 操作管理类 - 管理多个场景的操作记录
 * 重新设计：从根节点开始遍历，分离场景对象和UI对象处理
 */

import { get } from 'svelte/store';
import { globalObjectState } from '../stores/objectState';
import type { SceneAnalysisResult, BaseDisplayProperties } from './type';
import { SpriteObjectSerializer } from './spriteSerializer';

/**
 * 操作管理类
 */
export class OperationManager {
  // 存储多个场景的分析结果
  private sceneRecords: Map<string, SceneAnalysisResult> = new Map();

  // 临时存储当前分析的数据
  private currentUIObjects: Map<string, BaseDisplayProperties> = new Map();

  /**
   * 分析并保存当前场景
   * @returns 分析结果
   */
  public analyzeAndSaveScene(): SceneAnalysisResult | null {
    console.log('开始分析当前场景...');

    // 1. 从全局store获取rootObject
    const currentState = get(globalObjectState);
    if (!currentState.rootObject) {
      console.warn('全局store中没有rootObject');
      return null;
    }

    const rootObject = currentState.rootObject;
    const sceneClassName = currentState.rootObjectType || rootObject.constructor.name;

    // 2. 清空临时存储
    this.currentUIObjects.clear();

    // 3. 确定场景ID
    const sceneId = this.generateSceneId(sceneClassName);

    // 4. 从根节点开始遍历所有子对象（统一处理为UI对象）
    this.traverseFromRoot(rootObject, 'scene');

    // 5. 构建结果
    const result: SceneAnalysisResult = {
      sceneClassName,
      uiObjects: new Map(this.currentUIObjects)
    };

    // 6. 保存到记录中
    this.sceneRecords.set(sceneId, result);

    console.log(`场景分析完成: ${sceneId}`, {
      sceneClassName,
      uiObjectsCount: this.currentUIObjects.size
    });

    return result;
  }

  /**
   * 获取特定场景记录
   * @param sceneId 场景ID
   * @returns 场景分析结果
   */
  public getSceneRecord(sceneId: string): SceneAnalysisResult | null {
    return this.sceneRecords.get(sceneId) || null;
  }

  /**
   * 获取所有场景记录
   * @returns 所有场景记录的Map
   */
  public getAllSceneRecords(): Map<string, SceneAnalysisResult> {
    return new Map(this.sceneRecords);
  }

  /**
   * 移除特定场景记录
   * @param sceneId 场景ID
   * @returns 是否成功移除
   */
  public removeSceneRecord(sceneId: string): boolean {
    return this.sceneRecords.delete(sceneId);
  }

  /**
   * 清空所有记录
   */
  public clearAllRecords(): void {
    this.sceneRecords.clear();
  }

  /**
   * 从根节点开始遍历所有子对象（统一处理为UI对象）
   * @param rootObject 根对象
   * @param basePath 基础路径
   */
  private traverseFromRoot(rootObject: any, basePath: string): void {
    if (!rootObject || !rootObject.children) {
      return;
    }

    // 遍历根对象的所有子对象，统一处理为UI对象
    for (let i = 0; i < rootObject.children.length; i++) {
      const child = rootObject.children[i];
      if (!child || !child.constructor) continue;

      const childPath = this.generateNumericPath(basePath, i);
      // 统一处理所有对象为UI对象
      this.processUIObjects(child, childPath);
    }
  }



  /**
   * 处理UI对象（统一处理所有对象）
   * 特殊处理：如果是继承自Window_Base的窗口类型，直接跳过不做任何记录
   * @param obj 对象
   * @param path 对象路径
   */
  private processUIObjects(obj: any, path: string): void {
    // 检查是否为Window_Base类型，如果是则直接跳过，不做任何记录
    if (this.isWindowBaseType(obj)) {
      console.log(`检测到Window_Base类型对象: ${obj.constructor.name}，直接跳过不做记录`);
      return;
    }

    // 处理当前对象（统一处理，不再区分UI和场景对象）
    const properties = this.processObjectByType(obj, path);
    this.currentUIObjects.set(path, properties);

    // 递归遍历子对象
    if (obj.children && obj.children.length > 0) {
      for (let i = 0; i < obj.children.length; i++) {
        const child = obj.children[i];
        if (!child || !child.constructor) continue;

        const childPath = this.generateNumericPath(path, i);
        // 递归处理子对象
        this.processUIObjects(child, childPath);
      }
    }
  }

  /**
   * 根据对象类型进行具体处理（通用方法）
   * @param obj 对象
   * @param _path 对象路径（暂未使用，保留用于扩展）
   * @returns 处理后的属性对象
   */
  private processObjectByType(obj: any, _path: string): BaseDisplayProperties {
    const className = obj.constructor.name;

    // 目前只支持 Sprite 类型
    if (className === 'Sprite') {
      const spriteSerializer = new SpriteObjectSerializer();
      return spriteSerializer.serialize(obj);
    }

    // 遇到其他未定义的类型，报错并提示
    console.error(`❌ 遇到未定义的对象类型: ${className}`);
    console.error(`💡 需要创建 ${className}ObjectSerializer 类来处理此类型`);
    console.error(`📍 对象路径: ${_path}`);
    console.error(`🔧 请参考 SpriteObjectSerializer 的实现方式`);

    throw new Error(`未支持的对象类型: ${className}，请添加相应的序列化器`);
  }



  /**
   * 检查对象是否为Window_Base类型
   * @param obj 对象
   * @returns 是否为Window_Base类型
   */
  private isWindowBaseType(obj: any): boolean {
    if (!obj || !obj.constructor) {
      return false;
    }

    const className = obj.constructor.name;

    // 检查是否为Window_Base或其子类
    // 常见的窗口类型包括：Window_Base, Window_Command, Window_Selectable等
    return className.startsWith('Window_') ||
      className === 'Window' ||
      (obj instanceof (window as any).Window_Base) ||className === 'WindowLayer';
  }







  /**
   * 获取地图ID
   */
  private getMapId(sceneClassName: string): string {
    if (sceneClassName === 'Scene_Map') {
      try {
        const gameMap = (window as any).$gameMap;
        if (gameMap && gameMap.mapId) {
          return gameMap.mapId().toString();
        }
      } catch (error) {
        console.error('获取地图ID时出错:', error);
      }
      return '1';
    }
    return 'default';
  }

  /**
   * 生成场景ID
   */
  private generateSceneId(sceneClassName: string): string {
    if (sceneClassName === 'Scene_Map') {
      const mapId = this.getMapId(sceneClassName);
      return `${sceneClassName}_${mapId}`;
    }
    // 对于非Scene_Map场景，使用固定的ID避免重复记录
    return `${sceneClassName}_default`;
  }

  /**
   * 生成数字格式的路径（如 0.0.1）
   * @param basePath 基础路径
   * @param index 当前索引
   * @returns 数字格式的路径
   */
  private generateNumericPath(basePath: string, index: number): string {
    if (basePath === 'scene') {
      // 根级别，直接返回索引
      return index.toString();
    }

    // 在现有路径后添加新的索引
    return `${basePath}.${index}`;
  }
}
