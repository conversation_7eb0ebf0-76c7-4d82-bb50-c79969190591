/*:
 * @plugindesc 编辑器预览功能插件
 * <AUTHOR> AI
 */

function cloneDisplayObject(source) {
  if (!source) return null;

  let clone;

  // 根据源对象类型创建相应的克隆对象
  if (source instanceof Sprite) {
    // 如果是精灵对象，复制其纹理
    clone = new Sprite();
    if (source.bitmap) {
      clone.bitmap = source.bitmap;
    }
    // 复制精灵特有属性
    clone.anchor.copyFrom(source.anchor);
    clone.blendMode = source.blendMode;
    clone.opacity = source.opacity;
    if (source._frame) {
      clone._frame = source._frame.clone();
    }
    if (source._tint !== undefined) {
      clone._tint = source._tint;
    }
  } else if (source instanceof Window_Base) {
    // 如果是窗口对象
    clone = new Window_Base(new Rectangle(0, 0, source.width, source.height));
    clone._padding = source._padding;
    clone._margin = source._margin;
    clone._colorTone = source._colorTone
      ? source._colorTone.slice()
      : [0, 0, 0, 0];
    clone.opacity = source.opacity;
    clone.backOpacity = source.backOpacity;
    clone.contentsOpacity = source.contentsOpacity;
    if (source.contents) {
      clone.contents = source.contents;
    }
  } else if (source instanceof PIXI.Container) {
    // 默认创建容器
    clone = new PIXI.Container();
  }

  if (clone) {
    // 复制基本变换属性
    clone.x = source.x;
    clone.y = source.y;
    clone.scale.x = source.scale.x;
    clone.scale.y = source.scale.y;
    clone.rotation = source.rotation;
    clone.alpha = source.alpha;
    clone.visible = source.visible;
    clone.zIndex = source.zIndex;

    // 递归复制子元素
    if (source.children && source.children.length > 0) {
      source.children.forEach((child) => {
        const childClone = cloneDisplayObject(child);
        if (childClone) clone.addChild(childClone);
      });
    }
  }

  return clone;
}

window.cloneDisplayObject = cloneDisplayObject;
