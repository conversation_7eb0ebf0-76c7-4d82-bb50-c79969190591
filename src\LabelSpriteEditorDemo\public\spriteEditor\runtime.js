// 只需要的基础库
const scriptUrls = [
  "src/spriteEditor/js/libs/pixi.js",
  "src/spriteEditor/js/rmmz_core.js",
  "src/spriteEditor/js/rmmz_sprites.js",
  "src/spriteEditor/js/rmmz_managers.js",
  "src/spriteEditor/RPGEditor_BitmapTracker.js",
];

class SpriteRuntime {
  constructor() {
    this.loadCount = 0;
    this.error = null;
  }

  run() {
    this.loadMainScripts();
  }

  loadMainScripts() {
    for (const url of scriptUrls) {
      const script = document.createElement("script");
      script.type = "text/javascript";
      script.src = url;
      script.async = false;
      script.defer = true;
      script.onload = this.onScriptLoad.bind(this);
      script.onerror = this.onScriptError.bind(this);
      script._url = url;
      document.body.appendChild(script);
    }
    this.numScripts = scriptUrls.length;
  }

  onScriptLoad() {
    if (++this.loadCount === this.numScripts) {
      this.initGraphics();
    }
  }

  onScriptError(e) {
    console.error("Failed to load", e.target._url);
  }

  initGraphics() {
    // 查找Canvas组件创建的canvas元素
    const canvas = document.getElementById('react-canvas');

    if (!canvas) {
      console.error('未找到id为react-canvas的canvas元素');
      return;
    }

    console.log('找到Canvas组件创建的canvas元素');

    // 初始化PIXI应用，使用已存在的canvas
    const app = new PIXI.Application({
      width: 816,
      height: 624,
      backgroundColor: 0xf6f6f6,
      view: canvas // 使用已存在的canvas
    });

    console.log("runtime.js初始化了PIXI应用");

    // 设置全局Graphics对象
    Graphics._app = app;
    Graphics._width = 816;
    Graphics._height = 624;

    // 不需要居中显示canvas，因为Canvas组件已经处理了

    test()
  }

  centerCanvas(canvas) {
    // 设置canvas居中显示的样式，不调整大小
    canvas.style.position = "absolute";
    canvas.style.margin = "auto";
    canvas.style.top = 0;
    canvas.style.left = 0;
    canvas.style.right = 0;
    canvas.style.bottom = 0;
  }
}



function test() {
  console.log("RPG Maker 引擎初始化完成");

  // 不再在这里创建和添加精灵，而是通过React组件来管理
  // 这个函数现在只是一个初始化完成的标志

  // 检查必要的对象是否都已定义
  if (typeof Graphics !== 'undefined' &&
    typeof Bitmap !== 'undefined' &&
    typeof Sprite !== 'undefined') {

    // 创建事件对象，只包含已定义的对象
    const detail = {
      Graphics: Graphics,
      Bitmap: Bitmap,
      Sprite: Sprite
    };

    // 如果ImageManager已定义，也包含它
    if (typeof ImageManager !== 'undefined') {
      detail.ImageManager = ImageManager;
    }

    // 立即触发事件，因为Canvas组件已经挂载并添加了事件监听器
    const event = new CustomEvent('rpgmakerInitialized', { detail });
    document.dispatchEvent(event);
    console.log('RPG Maker初始化完成事件已触发', detail);
  } else {
    console.warn("RPG Maker 引擎部分对象尚未定义，无法完成初始化");
  }
}


const runtime = new SpriteRuntime();
runtime.run();