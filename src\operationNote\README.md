# 操作记录保存系统

一个简洁高效的操作记录保存系统，用于 RPG Maker MZ 编辑器，能够记录所有编辑操作并生成对应的插件代码。

## 🎯 核心特性

### 1. 简洁的设计架构
- **单一类型**：`OperationNode` 包含所有必要信息
- **树形结构**：完全镜像显示对象列表
- **三个核心容器**：
  - `children` 数组：子对象（镜像显示列表）
  - `operations` Map：属性修改接口记录
  - `deletions` Map：删除引擎对象的操作
- **接口系统**：使用 `IPropertyModification` 接口统一处理属性修改

### 2. 智能删除机制
- **编辑器对象删除**：直接抵消创建记录，不生成代码
- **引擎对象删除**：记录删除操作，生成删除代码
- **删除优先执行**：确保删除操作在其他操作之前执行

### 3. 自动代码生成
- **递归遍历**：从根节点开始自动遍历所有子节点
- **路径自动计算**：根据树形结构自动生成访问路径
- **分类处理**：删除、创建、修改分别处理
- **模板代码**：使用 `tempCode.ts` 中的模板生成高质量代码
- **接口驱动**：属性修改通过接口的 `generateCode()` 方法生成代码

## 🏗️ 系统架构

```
OperationRecordSystem
├── rootNode: OperationNode
│   ├── children: Array<OperationNode | undefined>
│   ├── operations: Map<string, IPropertyModification>
│   ├── deletions: Map<string, DeletionOperation>
│   └── nodeInfo: { className?, createType?, isEditorCreated? }
├── config: { globalIdCounter, deletedIds }
└── 接口系统:
    ├── IPropertyModification (接口)
    └── BasePropertyModification (实现)
```

## 📝 使用方法

### 基本操作

```typescript
import { operationRecordSystem, BasePropertyModification } from './operationNote';

// 1. 记录对象创建（自动处理初始属性）
operationRecordSystem.recordObjectCreation([0, 1], 'Sprite', {
  x: 100,
  y: 200,
  visible: true,
  alpha: 0.8
});

// 2. 记录属性修改（使用接口系统）
operationRecordSystem.recordPropertyModification([0, 1], 'scaleX', 2.0);
operationRecordSystem.recordPropertyModification([0, 1], 'visible', false);

// 3. 记录对象删除（智能抵消）
const engineObject = scene._spriteset._characterSprites[0];
const isCancel = operationRecordSystem.recordObjectDeletion([0, 2], engineObject);
console.log(isCancel ? '编辑器对象已抵消' : '引擎对象删除已记录');

// 4. 生成插件代码（包含模板代码）
const pluginCode = operationRecordSystem.generatePlugin('MyEditorPlugin');

// 5. 获取统计信息
const stats = operationRecordSystem.getStatistics();

// 6. 直接使用属性修改接口
const alphaModification = new BasePropertyModification(0.5, 'alpha');
const code = alphaModification.generateCode('targetObject');
console.log('生成的代码:', code);
```

## 🔧 RPG Maker MZ 基础对象必须保存的属性

### 1. PIXI.Container (基础容器)
```typescript
// 继承自 PIXI.DisplayObject 的基础属性
const containerEssentialProps = {
  // 位置和变换
  x: number,
  y: number,
  scaleX: number,  // scale.x
  scaleY: number,  // scale.y
  skewX: number,   // skew.x
  skewY: number,   // skew.y
  rotation: number,

  // 显示属性
  alpha: number,
  visible: boolean,

  // 锚点和轴心
  anchorX: number, // anchor.x
  anchorY: number, // anchor.y
  pivotX: number,  // pivot.x
  pivotY: number,  // pivot.y

  // 混合模式
  blendMode: number,

  // 遮罩和滤镜
  mask: any,
  filters: any[]
};
```

### 2. Sprite (精灵对象)
```typescript
const spriteEssentialProps = {
  // 继承 Container 的所有属性
  ...containerEssentialProps,

  // Sprite 特有属性
  bitmap: string,           // 图片路径或 Bitmap 对象
  width: number,           // _frame.width
  height: number,          // _frame.height
  opacity: number,         // alpha * 255 (0-255)

  // 帧设置
  frameX: number,          // _frame.x
  frameY: number,          // _frame.y
  frameWidth: number,      // _frame.width
  frameHeight: number,     // _frame.height

  // 色彩效果
  hue: number,             // _hue (-360 to 360)
  blendColor: number[],    // _blendColor [r,g,b,a]
  colorTone: number[],     // _colorTone [r,g,b,gray]

  // 特殊状态
  hidden: boolean          // _hidden
};
```

### 3. Window (窗口对象)
```typescript
const windowEssentialProps = {
  // 继承 Container 的所有属性
  ...containerEssentialProps,

  // 窗口尺寸
  width: number,           // _width
  height: number,          // _height

  // 窗口样式
  windowskin: string,      // 窗口皮肤图片路径
  padding: number,         // _padding
  margin: number,          // _margin

  // 透明度设置
  opacity: number,         // 窗口整体透明度 (0-255)
  backOpacity: number,     // 背景透明度 (0-255)
  contentsOpacity: number, // 内容透明度 (0-255)
  openness: number,        // 开启度 (0-255)

  // 颜色调整
  colorTone: number[],     // _colorTone [r,g,b,0]

  // 窗口状态
  active: boolean,         // 激活状态
  frameVisible: boolean,   // 边框可见性
  cursorVisible: boolean,  // 光标可见性

  // 滚动箭头
  downArrowVisible: boolean,
  upArrowVisible: boolean,
  pause: boolean,          // 暂停标志

  // 光标位置
  cursorRectX: number,     // _cursorRect.x
  cursorRectY: number,     // _cursorRect.y
  cursorRectWidth: number, // _cursorRect.width
  cursorRectHeight: number,// _cursorRect.height

  // 滚动原点
  originX: number,         // origin.x
  originY: number          // origin.y
};
```

### 4. Scene_Base (场景对象)
```typescript
const sceneEssentialProps = {
  // 继承 Container 的所有属性
  ...containerEssentialProps,

  // 场景状态
  started: boolean,        // _started
  active: boolean,         // _active

  // 淡入淡出
  fadeSign: number,        // _fadeSign
  fadeDuration: number,    // _fadeDuration
  fadeWhite: number,       // _fadeWhite
  fadeOpacity: number      // _fadeOpacity
};
```

### 5. Tilemap (瓦片地图)
```typescript
const tilemapEssentialProps = {
  // 继承 Container 的所有属性
  ...containerEssentialProps,

  // 地图尺寸
  width: number,           // _width
  height: number,          // _height

  // 瓦片设置
  tileWidth: number,       // 瓦片宽度
  tileHeight: number,      // 瓦片高度

  // 地图数据
  mapWidth: number,        // _mapWidth
  mapHeight: number,       // _mapHeight
  mapData: number[],       // _mapData

  // 滚动设置
  originX: number,         // origin.x
  originY: number,         // origin.y

  // 循环设置
  horizontalWrap: boolean,
  verticalWrap: boolean,

  // 动画
  animationCount: number,

  // 瓦片集
  bitmaps: string[],       // 瓦片集图片路径
  flags: number[]          // 瓦片标志
};
```

### 6. 特殊对象属性

#### Window_Base 扩展属性
```typescript
const windowBaseEssentialProps = {
  ...windowEssentialProps,

  // 内容位图
  contents: string,        // 内容位图路径
  contentsBack: string,    // 背景内容位图路径

  // 开关状态
  opening: boolean,        // _opening
  closing: boolean         // _closing
};
```

#### 构造函数参数
```typescript
const constructorParams = {
  // Sprite 构造参数
  Sprite: {
    bitmap?: string        // 可选的初始位图
  },

  // Window 构造参数
  Window_Base: {
    rect: {               // Rectangle 对象
      x: number,
      y: number,
      width: number,
      height: number
    }
  },

  // Scene 构造参数
  Scene_Base: {
    // 通常无参数
  }
};
```

### 数据持久化

```typescript
// 导出数据（保存到文件）
const saveData = operationRecordSystem.exportData();

// 导入数据（从文件加载）
operationRecordSystem.importData(saveData);

// 清空所有记录
operationRecordSystem.clear();
```

## 🔧 节点信息结构

```typescript
interface NodeInfo {
  className?: string;      // 对象类名
  createType?: string;     // 创建对象时候的类型
  isEditorCreated?: boolean; // 是否为编辑器创建
}
```

## 🗂️ 删除操作记录

```typescript
interface DeletionOperation {
  targetClass: string;     // 目标类名
  targetPath: number[];    // 目标路径
  code: string;           // 删除代码
  timestamp: number;      // 时间戳
}
```

## 📊 统计信息

```typescript
interface Statistics {
  totalNodes: number;      // 总节点数
  createdObjects: number;  // 创建对象数
  deletedObjects: number;  // 删除对象数
  modifiedObjects: number; // 修改对象数
}
```

## 🎮 生成的插件代码示例

```javascript
//=============================================================================
// MyEditorPlugin.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc MyEditorPlugin v1.0.0
 * <AUTHOR>
 * @help 编辑器生成的操作记录插件
 * 
 * 统计信息：
 * - 总节点数: 3
 * - 创建对象: 2
 * - 删除对象: 1
 * - 修改对象: 1
 */

(() => {
    'use strict';
    
    console.log('MyEditorPlugin 开始加载');
    
    // 等待场景加载完成后执行
    const originalStart = Scene_Map.prototype.start;
    Scene_Map.prototype.start = function() {
        originalStart.call(this);
        
        setTimeout(() => {
            executeEditorOperations();
        }, 100);
    };
    
    function executeEditorOperations() {
        console.log('执行编辑器操作');
        
        // === 删除操作 (路径: children.0) ===
        try {
            const sprite = SceneManager._scene.children[0];
            if (sprite) {
                sprite.visible = false;
                if (sprite.parent) sprite.parent.removeChild(sprite);
            }
            console.log('删除成功: Sprite_Character');
        } catch (error) {
            console.error('删除失败: Sprite_Character', error);
        }

        // === 操作记录 (路径: children.1) ===
        // 创建对象: Sprite
        const obj = new Sprite();
        obj.x = 100;
        obj.y = 200;
        obj.visible = true;
        // 添加到父容器
        const parent = SceneManager._scene;
        if (parent && parent.addChild) parent.addChild(obj);

        // 属性修改
        const target = SceneManager._scene.children[1];
        if (target) {
            target.alpha = 0.5;
        }
        
        console.log('编辑器操作执行完成');
    }
    
})();
```

## 🧪 测试示例

运行 `example.ts` 中的测试函数：

```typescript
import { 
  demonstrateOperationRecordSystem,
  testDeletionCancellation,
  testNestedStructure,
  testDataPersistence 
} from './example';

// 完整演示
demonstrateOperationRecordSystem();

// 删除抵消测试
testDeletionCancellation();

// 嵌套结构测试
testNestedStructure();

// 数据持久化测试
testDataPersistence();
```

## ✅ 优势特点

1. **结构简洁**：单一类型包含所有必要信息
2. **完全同步**：记录数组与显示列表完全一致
3. **智能抵消**：编辑器对象删除直接抵消创建记录
4. **删除优先**：删除操作优先执行，确保正确的执行顺序
5. **自动生成**：递归遍历生成完整插件代码
6. **易于维护**：清晰的接口和详细的日志
7. **数据持久化**：支持导出/导入，便于保存和加载

## 🔄 工作流程

1. **记录阶段**：用户在编辑器中进行操作时，系统自动记录
2. **处理阶段**：智能判断删除类型，编辑器对象直接抵消
3. **生成阶段**：遍历树形结构，生成对应的插件代码
4. **执行阶段**：生成的插件在游戏中按正确顺序执行操作

这个系统为 RPG Maker MZ 编辑器提供了一个完整、高效的操作记录和代码生成解决方案。
