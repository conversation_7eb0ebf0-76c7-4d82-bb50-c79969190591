<script lang="ts">
  /**
   * Slider 滑动条组件
   * 基于原生 input[type="range"] 的简化实现
   */
  
  // Props using Svelte 5 syntax
  let {
    value = $bindable(0),
    min = 0,
    max = 100,
    step = 1,
    disabled = false,
    size = 'md',
    variant = 'default',
    id = '',
    name = '',
    ariaLabel = '',
    onInput = () => {},
    onChange = () => {}
  }: {
    value?: number;
    min?: number;
    max?: number;
    step?: number;
    disabled?: boolean;
    size?: 'sm' | 'md' | 'lg';
    variant?: 'default' | 'success' | 'warning' | 'error';
    id?: string;
    name?: string;
    ariaLabel?: string;
    onInput?: (value: number, event: Event) => void;
    onChange?: (value: number, event: Event) => void;
  } = $props();
  
  // 内部状态
  let sliderElement: HTMLInputElement;
  
  // 处理输入事件
  function handleInput(event: Event) {
    const target = event.target as HTMLInputElement;
    const newValue = Number(target.value);
    console.log('Slider handleInput:', newValue);
    value = newValue;
    onInput(newValue, event);
  }

  function handleChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const newValue = Number(target.value);
    console.log('Slider handleChange:', newValue);
    value = newValue;
    onChange(newValue, event);
  }
  
  // 获取容器样式类
  function getContainerClass() {
    const baseClass = 'slider-container';
    const sizeClass = `slider-${size}`;
    const variantClass = `slider-${variant}`;
    const disabledClass = disabled ? 'slider-disabled' : '';
    
    return [baseClass, sizeClass, variantClass, disabledClass]
      .filter(Boolean)
      .join(' ');
  }
  
  // 公开方法
  export function focus() {
    sliderElement?.focus();
  }
  
  export function blur() {
    sliderElement?.blur();
  }
</script>

<div class={getContainerClass()}>
  <input
    bind:this={sliderElement}
    type="range"
    {id}
    {name}
    {min}
    {max}
    {step}
    {value}
    {disabled}
    aria-label={ariaLabel}
    class="slider-input"
    oninput={handleInput}
    onchange={handleChange}
  />
</div>

<style>
  .slider-container {
    position: relative;
    width: 100%;
    padding: 8px 0;
  }

  .slider-input {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #f1f5f9;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
  }

  /* WebKit 浏览器样式 */
  .slider-input::-webkit-slider-track {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #f1f5f9;
    border: none;
  }

  .slider-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: rgba(64, 105, 240, 0.9);
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .slider-input::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .slider-input::-webkit-slider-thumb:active {
    transform: scale(1.2);
  }

  /* Firefox 浏览器样式 */
  .slider-input::-moz-range-track {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #f1f5f9;
    border: none;
  }

  .slider-input::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: rgba(64, 105, 240, 0.9);
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .slider-input::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .slider-input::-moz-range-thumb:active {
    transform: scale(1.2);
  }

  /* 禁用状态 */
  .slider-disabled .slider-input {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .slider-disabled .slider-input::-webkit-slider-thumb {
    cursor: not-allowed;
  }

  .slider-disabled .slider-input::-moz-range-thumb {
    cursor: not-allowed;
  }

  /* 尺寸变体 */
  .slider-sm .slider-input {
    height: 4px;
  }

  .slider-sm .slider-input::-webkit-slider-track {
    height: 4px;
  }

  .slider-sm .slider-input::-moz-range-track {
    height: 4px;
  }

  .slider-sm .slider-input::-webkit-slider-thumb {
    width: 14px;
    height: 14px;
  }

  .slider-sm .slider-input::-moz-range-thumb {
    width: 14px;
    height: 14px;
  }

  .slider-lg .slider-input {
    height: 8px;
  }

  .slider-lg .slider-input::-webkit-slider-track {
    height: 8px;
  }

  .slider-lg .slider-input::-moz-range-track {
    height: 8px;
  }

  .slider-lg .slider-input::-webkit-slider-thumb {
    width: 22px;
    height: 22px;
  }

  .slider-lg .slider-input::-moz-range-thumb {
    width: 22px;
    height: 22px;
  }

  /* 状态变体 */
  .slider-success .slider-input::-webkit-slider-thumb {
    background: #22c55e;
  }

  .slider-success .slider-input::-moz-range-thumb {
    background: #22c55e;
  }

  .slider-warning .slider-input::-webkit-slider-thumb {
    background: #f59e0b;
  }

  .slider-warning .slider-input::-moz-range-thumb {
    background: #f59e0b;
  }

  .slider-error .slider-input::-webkit-slider-thumb {
    background: #ef4444;
  }

  .slider-error .slider-input::-moz-range-thumb {
    background: #ef4444;
  }

  /* 焦点样式 */
  .slider-input:focus {
    outline: none;
  }

  .slider-input:focus::-webkit-slider-thumb {
    outline: 2px solid rgba(64, 105, 240, 0.3);
    outline-offset: 2px;
  }

  .slider-input:focus::-moz-range-thumb {
    outline: 2px solid rgba(64, 105, 240, 0.3);
    outline-offset: 2px;
  }
</style>
