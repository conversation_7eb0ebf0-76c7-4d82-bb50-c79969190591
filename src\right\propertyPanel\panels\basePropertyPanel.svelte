<script lang="ts">
  import { globalObjectState } from '../../../stores/objectState';
  import LabelInput from '../../../components/LabelInput.svelte';
  import Slider from '../../../components/Slider.svelte';
  import Label from '../../../components/Label.svelte';
  import Checkbox from '../../../components/Checkbox.svelte';
  import AccordionPanel from '../../../components/AccordionPanel.svelte';
  import { updateObjectProperty } from '../../propertyLogic/basePropertyLogic';

  // 监听选中的对象数组，默认显示第一个元素
  let selectedObjectArray = $derived($globalObjectState.selectedObject);
  let selectedObject = $derived(selectedObjectArray?.[0] || null);
  let selectedObjectType = $derived($globalObjectState.selectedObjectType?.[0] || null);

  // 基础属性的响应式状态
  let x = $state(0);
  let y = $state(0);
  let width = $state(0);
  let height = $state(0);
  let anchorX = $state(0);
  let anchorY = $state(0);
  let scaleX = $state(1);
  let scaleY = $state(1);
  let skewX = $state(0);
  let skewY = $state(0);
  let rotation = $state(0);
  let alpha = $state(1);
  let visible = $state(true);

  // 字符串状态用于 LabelInput 组件绑定
  let xString = $state('0');
  let yString = $state('0');
  let widthString = $state('0');
  let heightString = $state('0');
  let anchorXString = $state('0');
  let anchorYString = $state('0');
  let scaleXString = $state('1');
  let scaleYString = $state('1');
  let skewXString = $state('0');
  let skewYString = $state('0');
  let rotationString = $state('0');

  // 整个面板的展开状态
  let isExpanded = $state(true);



  // 监听对象变化，更新属性值
  $effect(() => {
    if (selectedObject) {
      console.log('=== 更新属性面板 ===');
      console.log('选中对象:', selectedObject);
      console.log('对象类型:', selectedObjectType);

      // 更新位置属性 - 检查属性是否存在
      x = (selectedObject.x !== undefined) ? selectedObject.x : 0;
      y = (selectedObject.y !== undefined) ? selectedObject.y : 0;

      // 更新尺寸属性 - 检查属性是否存在
      width = (selectedObject.width !== undefined) ? selectedObject.width : 0;
      height = (selectedObject.height !== undefined) ? selectedObject.height : 0;

      // 同步字符串状态 - 只在对象切换时更新，避免触发循环更新
      xString = x.toString();
      yString = y.toString();
      widthString = width.toString();
      heightString = height.toString();

      // 更新锚点属性
      if (selectedObject.anchor) {
        anchorX = selectedObject.anchor.x || 0;
        anchorY = selectedObject.anchor.y || 0;
      } else {
        anchorX = 0;
        anchorY = 0;
      }

      // 更新缩放属性
      if (selectedObject.scale) {
        scaleX = selectedObject.scale.x || 1;
        scaleY = selectedObject.scale.y || 1;
      } else {
        scaleX = 1;
        scaleY = 1;
      }

      // 更新倾斜属性
      if (selectedObject.skew) {
        skewX = selectedObject.skew.x || 0;
        skewY = selectedObject.skew.y || 0;
      } else {
        skewX = 0;
        skewY = 0;
      }

      // 更新旋转属性
      rotation = selectedObject.rotation || 0;

      // 更新透明度属性
      alpha = selectedObject.alpha !== undefined ? selectedObject.alpha : 1;

      // 更新可见性属性
      visible = selectedObject.visible !== undefined ? selectedObject.visible : true;

      // 同步其他字符串状态
      anchorXString = anchorX.toString();
      anchorYString = anchorY.toString();
      scaleXString = scaleX.toString();
      scaleYString = scaleY.toString();
      skewXString = skewX.toString();
      skewYString = skewY.toString();
      rotationString = rotation.toString();

      console.log('属性值已更新:', {
        position: { x, y },
        size: { width, height },
        anchor: { x: anchorX, y: anchorY },
        scale: { x: scaleX, y: scaleY },
        skew: { x: skewX, y: skewY },
        alpha,
        visible
      });
    }
  });

  // 监听字符串变化，同步到数字值和对象
  $effect(() => {
    const newX = Number(xString);
    if (!isNaN(newX) && newX !== x) {
      x = newX;
      updateProperty('x', newX);
    }
  });

  $effect(() => {
    const newY = Number(yString);
    if (!isNaN(newY) && newY !== y) {
      y = newY;
      updateProperty('y', newY);
    }
  });

  $effect(() => {
    const newWidth = Number(widthString);
    if (!isNaN(newWidth) && newWidth !== width) {
      width = newWidth;
      updateProperty('width', newWidth);
    }
  });

  $effect(() => {
    const newHeight = Number(heightString);
    if (!isNaN(newHeight) && newHeight !== height) {
      height = newHeight;
      updateProperty('height', newHeight);
    }
  });

  $effect(() => {
    const newAnchorX = Number(anchorXString);
    if (!isNaN(newAnchorX) && newAnchorX !== anchorX) {
      anchorX = newAnchorX;
      updateProperty('anchorX', newAnchorX);
    }
  });

  $effect(() => {
    const newAnchorY = Number(anchorYString);
    if (!isNaN(newAnchorY) && newAnchorY !== anchorY) {
      anchorY = newAnchorY;
      updateProperty('anchorY', newAnchorY);
    }
  });

  $effect(() => {
    const newScaleX = Number(scaleXString);
    if (!isNaN(newScaleX) && newScaleX !== scaleX) {
      scaleX = newScaleX;
      updateProperty('scaleX', newScaleX);
    }
  });

  $effect(() => {
    const newScaleY = Number(scaleYString);
    if (!isNaN(newScaleY) && newScaleY !== scaleY) {
      scaleY = newScaleY;
      updateProperty('scaleY', newScaleY);
    }
  });

  $effect(() => {
    const newSkewX = Number(skewXString);
    if (!isNaN(newSkewX) && newSkewX !== skewX) {
      skewX = newSkewX;
      updateProperty('skewX', newSkewX);
    }
  });

  $effect(() => {
    const newSkewY = Number(skewYString);
    if (!isNaN(newSkewY) && newSkewY !== skewY) {
      skewY = newSkewY;
      updateProperty('skewY', newSkewY);
    }
  });

  $effect(() => {
    const newRotation = Number(rotationString);
    if (!isNaN(newRotation) && newRotation !== rotation) {
      rotation = newRotation;
      updateProperty('rotation', newRotation);
    }
  });

  // 属性更新函数 - 使用 basePropertyLogic 支持多对象
  function updateProperty(property: string, value: number) {
    if (!selectedObjectArray || selectedObjectArray.length === 0) {
      console.warn('没有选中的对象');
      return;
    }

    console.log(`=== 通过 basePropertyLogic 更新多对象属性 ${property} ===`);
    console.log('选中对象数组:', selectedObjectArray);
    console.log('新值:', value);

    // 使用新的多对象更新逻辑
    updateObjectProperty(selectedObjectArray, property, value);
  }

  // 布尔属性更新函数
  function updateBooleanProperty(property: string, value: boolean) {
    if (!selectedObjectArray || selectedObjectArray.length === 0) {
      console.warn('没有选中的对象');
      return;
    }

    console.log(`=== 通过 basePropertyLogic 更新多对象布尔属性 ${property} ===`);
    console.log('选中对象数组:', selectedObjectArray);
    console.log('新值:', value);

    // 使用新的多对象更新逻辑
    updateObjectProperty(selectedObjectArray, property, value);
  }
</script>

{#if selectedObject}
  <AccordionPanel
    title="基础属性"
    icon="⚙️"
    badge={selectedObjectType || '未知类型'}
    badgeVariant="info"
    bind:expanded={isExpanded}
  >
        <!-- 位置属性 -->
        <div class="property-section">

          <div class="property-row">
               <h4>📍 位置：</h4>
            <div class="property-item-horizontal">
              <label for="pos-x">X:</label>
              <LabelInput
                bind:value={xString}
                type="number"
                step={1}
              />
            </div>
            <div class="property-item-horizontal">
              <label for="pos-y">Y:</label>
              <LabelInput
                bind:value={yString}
                type="number"
                step={1}
              />
            </div>
          </div>
        </div>

        <!-- 尺寸属性 -->
        <div class="property-section">

          <div class="property-row">
              <h4>📏 尺寸：</h4>
            <div class="property-item-horizontal">
              <label for="size-width">W:</label>
              <LabelInput
                bind:value={widthString}
                type="number"
                step={1}
              />
            </div>
            <div class="property-item-horizontal">
              <label for="size-height">H:</label>
              <LabelInput
                bind:value={heightString}
                type="number"
                step={1}
              />
            </div>
          </div>
        </div>

        <!-- 锚点属性 -->
        <div class="property-section">

          <div class="property-row">
            <h4>⚓ 锚点：</h4>
            <div class="property-item-horizontal">
              <label for="anchor-x">X:</label>
              <LabelInput
                bind:value={anchorXString}
                type="number"
                step={0.01}
                min={0}
                max={1}
                precision={2}
              />
            </div>
            <div class="property-item-horizontal">
              <label for="anchor-y">Y:</label>
              <LabelInput
                bind:value={anchorYString}
                type="number"
                step={0.01}
                min={0}
                max={1}
                precision={2}
              />
            </div>
          </div>
        </div>

        <!-- 缩放属性 -->
        <div class="property-section">

          <div class="property-row">
            <h4>🔍 缩放：</h4>
            <div class="property-item-horizontal">
              <label for="scale-x">X:</label>
              <LabelInput
                bind:value={scaleXString}
                type="number"
                step={0.1}
                min={0.1}
                max={3}
                precision={1}
              />
            </div>
            <div class="property-item-horizontal">
              <label for="scale-y">Y:</label>
              <LabelInput
                bind:value={scaleYString}
                type="number"
                step={0.1}
                min={0.1}
                max={3}
                precision={1}
              />
            </div>
          </div>
        </div>

        <!-- 倾斜属性 -->
        <div class="property-section">

          <div class="property-row">
             <h4>🔄 倾斜：</h4>
            <div class="property-item-horizontal">
              <label for="skew-x">X:</label>
              <LabelInput
                bind:value={skewXString}
                type="number"
                step={0.1}
                precision={1}
              />
            </div>
            <div class="property-item-horizontal">
              <label for="skew-y">Y:</label>
              <LabelInput
                bind:value={skewYString}
                type="number"
                step={0.1}
                precision={1}
              />
            </div>
          </div>
        </div>

        <!-- 旋转属性 -->
        <div class="property-section">

          <div class="property-row">
              <h4>🔄 旋转：</h4>
            <div class="property-item-horizontal">
              <label for="rotation">角度:</label>
              <LabelInput
                bind:value={rotationString}
                type="number"
                step={0.1}
                precision={1}
              />
            </div>
          </div>
        </div>

        <!-- 透明度属性 -->
        <div class="property-section">

          <div class="property-row">
            <div class="property-item-horizontal property-item-full-width">
              <label for="alpha">透明度:</label>
              <Slider
                bind:value={alpha}
                min={0}
                max={1}
                step={0.01}
                onInput={(value: number) => updateProperty('alpha', value)}
                onChange={(value: number) => updateProperty('alpha', value)}
              />
              <Label text={alpha.toFixed(2)} size="md" variant="secondary" />
            </div>
          </div>
        </div>

        <!-- 可见性属性 -->
        <div class="property-section">
          <div class="property-row">
            <div class="property-item-horizontal">
              <label for="visible">👁️ 可见性：</label>
              <Checkbox
                checked={visible}
                onChange={(checked: boolean) => {
                  visible = checked;
                  updateBooleanProperty('visible', checked);
                }}
              />
              <Label text={visible ? '显示' : '隐藏'} size="md" variant="secondary" />
            </div>
          </div>
        </div>
  </AccordionPanel>
{:else}
  <div class="no-selection">
    <p>请选择一个对象来查看其属性</p>
  </div>
{/if}

<style>

  .property-section {
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 4px;
    padding: 8px;
    border: 1px solid var(--theme-border-light, #e9ecef);
  }

  .property-section h4 {
    margin: 0 0 6px 0;
    font-size: 11px;
    font-weight: 600;
    color: var(--theme-text, #1a202c);
  }

  .property-row {
    display: flex;
    gap: 8px;
    align-items: flex-start;
  }

  .property-item-horizontal {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
  }

  .property-item-full-width {
    flex: 1;
  }

  .property-item-horizontal label {
    font-size: 10px;
    font-weight: 500;
    color: var(--theme-text-secondary, #718096);
    white-space: nowrap;
    min-width: 20px;
    text-align: right;
  }





  .no-selection {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 16px;
    text-align: center;
    color: var(--theme-text-secondary, #718096);
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 6px;
    border: 2px dashed var(--theme-border, #e2e8f0);
  }

  .no-selection p {
    margin: 0;
    font-size: 12px;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .property-row {
      flex-direction: column;
      gap: 4px;
    }
  }
</style>