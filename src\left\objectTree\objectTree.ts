/**
 * 对象树逻辑处理
 */

import type { ObjectTreeNodeData, ObjectTreeConfig } from './types';
import {
  isRPGMakerMZType,
  getObjectTypeName,
  getObjectDisplayName,
  generateNodeId,
  getObjectChildren,
  hasChildren,
  determineNodeType,
  isWindowType,
  type NodeTypeInfo
} from './typeUtils';

/**
 * 默认对象树配置
 */
export const DEFAULT_OBJECT_TREE_CONFIG: ObjectTreeConfig = {
  showProperties: true,
  showMethods: false,
  maxDepth: 3,
  autoExpandRoot: true
};

/**
 * 创建对象树节点
 * @param obj 对象实例
 * @param typeName 类型名称
 * @param isRoot 是否为根节点
 * @param depth 深度
 * @param config 配置
 * @param parentType 父节点类型（用于类型继承）
 * @returns 对象树节点
 */
export function createObjectTreeNode(
  obj: any,
  typeName: string,
  isRoot: boolean = false,
  depth: number = 0,
  config: ObjectTreeConfig = DEFAULT_OBJECT_TREE_CONFIG,
  parentType?: string
): ObjectTreeNodeData {
  // 确定节点类型信息
  const typeInfo = determineNodeType(
    obj,
    parentType,
    isRoot // 是否是主类型节点
  );

  const node: ObjectTreeNodeData = {
    currentObject: obj,
    isRoot: isRoot,
    objectType: isRoot ? typeName : undefined,
    isRPGMakerType: isRPGMakerMZType(typeName),
    children: [],
    expanded: isRoot ? config.autoExpandRoot : false,
    id: generateNodeId(typeName, obj),
    displayName: getObjectDisplayName(obj, typeName),
    depth: depth,
    typeInfo: typeInfo
  };

  // 如果深度未超过限制且不是窗口类型，构建子节点
  if (depth < config.maxDepth && hasChildren(obj) && !typeInfo.isWindowType) {
    buildChildNodes(node, obj, depth + 1, config, typeInfo.classType || typeName);
  }

  return node;
}

/**
 * 创建对象树节点（使用已计算的类型信息）
 * @param obj 对象实例
 * @param typeName 类型名称
 * @param isRoot 是否为根节点
 * @param depth 深度
 * @param config 配置
 * @param typeInfo 已计算的类型信息
 * @returns 对象树节点
 */
function createObjectTreeNodeWithTypeInfo(
  obj: any,
  typeName: string,
  isRoot: boolean,
  depth: number,
  config: ObjectTreeConfig,
  typeInfo: NodeTypeInfo
): ObjectTreeNodeData {
  const node: ObjectTreeNodeData = {
    currentObject: obj,
    isRoot: isRoot,
    objectType: isRoot ? typeName : undefined,
    isRPGMakerType: isRPGMakerMZType(typeName),
    children: [],
    expanded: isRoot ? config.autoExpandRoot : false,
    id: generateNodeId(typeName, obj),
    displayName: getObjectDisplayName(obj, typeName),
    depth: depth,
    typeInfo: typeInfo
  };

  // 如果深度未超过限制且不是窗口类型，构建子节点
  if (depth < config.maxDepth && hasChildren(obj) && !typeInfo.isWindowType) {
    buildChildNodes(node, obj, depth + 1, config, typeInfo.classType || typeName);
  }

  return node;
}

/**
 * 构建子节点
 * @param parentNode 父节点
 * @param parentObj 父对象
 * @param depth 深度
 * @param config 配置
 * @param parentType 父节点类型
 */
function buildChildNodes(
  parentNode: ObjectTreeNodeData,
  parentObj: any,
  depth: number,
  config: ObjectTreeConfig,
  parentType?: string
) {
  const children = getObjectChildren(parentObj);

  // 添加调试信息
  const parentTypeName = getObjectTypeName(parentObj);
  console.log(`[ObjectTree] 构建子节点 - 父对象: ${parentTypeName}, 子对象数量: ${children.length}`);

  for (let i = 0; i < children.length; i++) {
    const child = children[i];
    if (!child) continue;

    const childTypeName = getObjectTypeName(child);

    const effectiveParentType = parentNode.typeInfo?.isTypeNode ?
      parentNode.typeInfo.classType : parentType;

    // 确定子节点类型信息
    const childTypeInfo = determineNodeType(
      child,
      effectiveParentType,
      false // 不是主类型节点
    );

    // 创建子节点，传递计算好的类型信息
    // 注意：窗口类型的节点会被创建，但在createObjectTreeNodeWithTypeInfo中不会为它们创建子节点
    const childNode = createObjectTreeNodeWithTypeInfo(
      child,
      childTypeName,
      false,
      depth,
      config,
      childTypeInfo
    );

    parentNode.children.push(childNode);
  }
}

// 展开/收起相关函数已移至 objectTreeActions.ts 中统一管理
// 这些函数与新的状态管理系统不兼容，已被替代

// 查找和统计相关函数已移至 objectTreeStore.ts 和 objectTreeActions.ts 中
// 新的扁平化存储结构提供了更高效的查找和统计方法

// 过滤功能已在 ObjectTreePanel.svelte 中实现，使用派生状态提供更好的性能
