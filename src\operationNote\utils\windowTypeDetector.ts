/**
 * 窗口类型检测工具
 * 统一检测对象是否为窗口类型（继承自 Window_Base）
 */

/**
 * 检测窗口类型并返回窗口名称
 * @param input 要检测的对象或类名字符串
 * @returns 如果是窗口类型返回窗口名称，否则返回 null
 */
export function detectWindowType(input: any | string): string | null {
  try {
    let className: string;
    let targetClass: any;

    // 处理输入参数
    if (typeof input === 'string') {
      className = input;
      targetClass = (window as any)[className];
    } else if (input && input.constructor) {
      className = input.constructor.name;
      targetClass = input.constructor;
    } else {
      console.warn('无效的输入参数:', input);
      return null;
    }

    // 如果能找到类，检查是否继承自 Window_Base
    if (targetClass) {
      const isWindow = isInheritedFromWindowBase(targetClass);
      return isWindow ? className : null;
    }

    // 如果找不到类，使用字符串匹配作为后备方案
    const isWindow = isWindowTypeByName(className);
    return isWindow ? className : null;

  } catch (error) {
    console.error('检测窗口类型时出错:', error);

    // 出错时使用字符串匹配作为后备方案
    const className = typeof input === 'string' ? input : input?.constructor?.name;
    if (className && isWindowTypeByName(className)) {
      return className;
    }

    return null;
  }
}

/**
 * 检查类是否继承自 Window_Base
 * @param targetClass 要检查的类
 * @returns 是否继承自 Window_Base
 */
function isInheritedFromWindowBase(targetClass: any): boolean {
  if (!targetClass) {
    return false;
  }

  // 获取 Window_Base 类
  const WindowBase = (window as any).Window_Base;
  if (!WindowBase) {
    console.warn('无法找到 Window_Base 类，使用字符串匹配');
    return isWindowTypeByName(targetClass.name);
  }

  // 检查原型链
  let currentClass = targetClass;
  const maxDepth = 10; // 防止无限循环
  let depth = 0;

  while (currentClass && depth < maxDepth) {
    console.log(`检查类继承链 [${depth}]:`, currentClass.name);

    // 检查是否是 Window_Base
    if (currentClass === WindowBase) {
      console.log(`✅ ${targetClass.name} 继承自 Window_Base`);
      return true;
    }

    // 检查是否直接继承自 Window_Base
    if (currentClass.prototype && currentClass.prototype instanceof WindowBase) {
      console.log(`✅ ${targetClass.name} 的原型继承自 Window_Base`);
      return true;
    }

    // 向上查找父类
    currentClass = Object.getPrototypeOf(currentClass);
    depth++;
  }

  console.log(`❌ ${targetClass.name} 不继承自 Window_Base`);
  return false;
}

/**
 * 基于类名的窗口类型检测（后备方案）
 * @param className 类名
 * @returns 是否为窗口类型
 */
function isWindowTypeByName(className: string): boolean {
  // 已知的窗口类型列表
  const knownWindowTypes = [
    'Window_Base', 'Window_Scrollable', 'Window_Selectable', 'Window_Command',
    'Window_HorzCommand', 'Window_TitleCommand', 'Window_MenuCommand',
    'Window_ItemCategory', 'Window_ItemList', 'Window_SkillType',
    'Window_SkillStatus', 'Window_SkillList', 'Window_EquipStatus',
    'Window_EquipCommand', 'Window_EquipSlot', 'Window_EquipItem',
    'Window_Status', 'Window_Options', 'Window_SavefileList',
    'Window_ShopCommand', 'Window_ShopBuy', 'Window_ShopSell',
    'Window_ShopNumber', 'Window_ShopStatus', 'Window_NameInput',
    'Window_ChoiceList', 'Window_NumberInput', 'Window_EventItem',
    'Window_Message', 'Window_ScrollText', 'Window_MapName',
    'Window_BattleLog', 'Window_PartyCommand', 'Window_ActorCommand',
    'Window_BattleStatus', 'Window_BattleActor', 'Window_BattleEnemy',
    'Window_BattleSkill', 'Window_BattleItem', 'Window_Help',
    'Window_Gold', 'Window_StatusBase', 'Window_StatusParams',
    'Window_StatusEquip', 'Window_DebugRange', 'Window_DebugEdit',
    'Window_GameEnd'
  ];

  const isKnownWindow = knownWindowTypes.includes(className);

  if (isKnownWindow) {
    console.log(`✅ ${className} 是已知的窗口类型`);
  } else {
    console.log(`❌ ${className} 不是已知的窗口类型`);
  }

  return isKnownWindow;
}

/**
 * 兼容性方法：检测是否为窗口类型（返回布尔值）
 * @param input 要检测的对象或类名字符串
 * @returns 是否为窗口类型
 */
export function isWindowType(input: any | string): boolean {
  return detectWindowType(input) !== null;
}
