/**
 * 文件操作处理模块
 * 统一管理操作记录文件的保存和加载
 */

import { OperationRecordsAPI } from './lib';
import { OperationManager } from './generators/operationManager';
import type { SceneAnalysisResult } from './generators/type';
import { generatePluginCode, type OperationRecordsData } from './generators/pluginGenerator';

import { getCurrentProjectState } from './stores/projectStore';

// 创建全局 OperationManager 实例
export const globalOperationManager = new OperationManager();


/**
 * 保存操作记录到当前项目
 */
export async function saveOperationRecords(): Promise<void> {
  console.log('=== 开始保存操作记录 ===');

  try {
    const projectState = getCurrentProjectState();

    if (!projectState.isLoaded || !projectState.projectPath) {
      console.log('没有加载的项目，跳过保存操作记录');
      return;
    }

    // 使用 OperationManager 分析并保存当前场景
    const sceneAnalysisResult = globalOperationManager.analyzeAndSaveScene();

    if (!sceneAnalysisResult) {
      console.warn('场景分析失败，无法保存操作记录');
      return;
    }

    // 获取所有场景记录
    const allSceneRecords = globalOperationManager.getAllSceneRecords();

    // 构建 OperationRecordsData
    const dataToSave: OperationRecordsData = {
      version: '2.0.0',
      timestamp: Date.now(),
      author: 'RPG Editor',
      records: convertSceneRecordsToSerializable(allSceneRecords)
    };

    console.log('准备保存的场景记录数据:', {
      sceneCount: allSceneRecords.size,
      currentScene: sceneAnalysisResult.sceneClassName
    });

    // 保存到文件
    const saveResult = await OperationRecordsAPI.saveOperationRecords(
      projectState.projectPath,
      JSON.stringify(dataToSave, null, 2)
    );

    if (saveResult.success) {
      console.log('=== 操作记录保存成功 ===');
      console.log('保存路径:', saveResult.data);
    } else {
      console.error('保存操作记录失败:', saveResult.error);
    }

  } catch (error) {
    console.error('保存操作记录时发生异常:', error);
  }
}

/**
 * 将场景记录 Map 转换为可序列化的对象
 * @param sceneRecords 场景记录 Map
 * @returns 可序列化的对象
 */
function convertSceneRecordsToSerializable(sceneRecords: Map<string, SceneAnalysisResult>): Record<string, SceneAnalysisResult> {
  const result: Record<string, SceneAnalysisResult> = {};

  // 转换每个场景记录
  for (const [sceneId, sceneData] of sceneRecords) {
    result[sceneId] = {
      sceneClassName: sceneData.sceneClassName,
      uiObjects: convertMapToObject(sceneData.uiObjects)
    };
  }

  return result;
}

/**
 * 将嵌套的 Map 结构转换为普通对象
 * @param mapData Map 数据
 * @returns 普通对象
 */
function convertMapToObject(mapData: Map<string, any>): any {
  const result: any = {};

  for (const [key, value] of mapData) {
    if (value instanceof Map) {
      // 如果值也是 Map，递归转换
      result[key] = convertMapToObject(value);
    } else {
      result[key] = value;
    }
  }

  return result;
}

/**
 * 从指定项目路径加载操作记录
 */
export async function loadOperationRecords(projectPath: string): Promise<{ success: boolean; message?: string }> {
  console.log('=== 开始加载操作记录 ===');
  console.log('项目路径:', projectPath);

  try {
    const loadResult = await OperationRecordsAPI.loadOperationRecords(projectPath);

    if (!loadResult.success) {
      console.error('加载操作记录失败:', loadResult.error);
      return { success: false, message: loadResult.error };
    }

    if (!loadResult.data) {
      console.log('项目中没有操作记录文件');
      return { success: true, message: '项目中没有操作记录文件' };
    }

    // 解析加载的数据
    const parsedData = JSON.parse(loadResult.data);

    // 检查数据格式
    if (parsedData.records && typeof parsedData.records === 'object') {
      console.log('检测到场景记录数据，开始恢复到 OperationManager...');

      // 清空现有记录
      globalOperationManager.clearAllRecords();

      // 恢复场景记录到 OperationManager
      // 注意：这里需要将普通对象转换回 Map 结构
      // 由于 OperationManager 的 sceneRecords 是私有的，我们可能需要添加一个导入方法
      // 暂时先记录日志，具体实现可能需要在 OperationManager 中添加导入方法

      console.log('=== 场景记录加载完成 ===');
      console.log('加载的场景数量:', Object.keys(parsedData.records).length);

      return { success: true, message: '场景记录加载成功' };
    } else {
      console.log('未识别的数据格式');
      return { success: false, message: '未识别的数据格式' };
    }

  } catch (error) {
    console.error('加载操作记录时发生异常:', error);
    return { success: false, message: String(error) };
  }
}

/**
 * 导出插件到当前项目
 */
export async function exportPlug(): Promise<void> {
  console.log('=== 开始导出插件 ===');

  try {
    // 先保存操作记录
    await saveOperationRecords();

    const projectState = getCurrentProjectState();

    if (!projectState.isLoaded || !projectState.projectPath) {
      console.log('没有加载的项目，无法导出插件');

      if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
        new Notification('导出插件失败', {
          body: '没有加载的项目',
          icon: '/favicon.ico'
        });
      }
      return;
    }

    // 获取所有场景记录
    const allSceneRecords = globalOperationManager.getAllSceneRecords();

    if (allSceneRecords.size === 0) {
      console.log('没有场景记录，无法生成插件');

      if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
        new Notification('导出插件失败', {
          body: '没有场景记录数据',
          icon: '/favicon.ico'
        });
      }
      return;
    }

    // 构建操作记录数据
    const operationData: OperationRecordsData = {
      version: '2.0.0',
      timestamp: Date.now(),
      author: 'RPG Editor',
      records: convertSceneRecordsToSerializable(allSceneRecords)
    };

    // 生成插件代码
    const pluginCode = generatePluginCode(operationData, {
      pluginName: 'RPGEditor_GeneratedPlugin',
      pluginDescription: 'Auto-generated plugin from RPG Editor',
      author: 'RPG Editor',
      version: '1.0.0'
    });

    // 导出插件文件
    const exportResult = await OperationRecordsAPI.savePluginFile(
      projectState.projectPath,
      pluginCode
    );

    if (exportResult.success) {
      console.log('=== 插件导出完成 ===');
      console.log('插件文件路径:', exportResult.data);

      if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
        new Notification('插件导出成功', {
          body: '插件已保存到项目的plugins文件夹',
          icon: '/favicon.ico'
        });
      }
    } else {
      console.error('插件导出失败:', exportResult.error);

      if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
        new Notification('插件导出失败', {
          body: exportResult.error,
          icon: '/favicon.ico'
        });
      }
    }

  } catch (error) {
    console.error('导出插件时发生异常:', error);

    if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
      new Notification('插件导出异常', {
        body: String(error),
        icon: '/favicon.ico'
      });
    }
  }
}

