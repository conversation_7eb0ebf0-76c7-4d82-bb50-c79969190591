/**
 * Scene_Map 专门处理器
 * 处理 Scene_Map 场景的分析、保存和代码生成
 */

import type { SceneAnalysisResult, SceneObjectInfo } from '../sceneAnalyzer';
import { type SpriteData, processMultipleSpriteData } from '../spritePro';
import { isWindowType } from '../utils/windowTypeDetector';

/**
 * Scene_Map 数据结构
 */
export interface SceneMapData {
  mapId: number;                    // 当前地图ID
  sceneClassName: string;           // 场景类名（应该是 Scene_Map）
  sceneObjects: SpriteData[];       // 场景中的对象
  windowLayerObjects: any[];        // WindowLayer 中的对象
  metadata: {
    timestamp: number;
    totalObjects: number;
    objectTypes: string[];
  };
}

/**
 * Scene_Map 处理器类
 */
export class SceneMapProcessor {

  /**
   * 从场景分析结果创建 Scene_Map 数据
   * @param analysisResult 场景分析结果
   * @returns Scene_Map 数据
   */
  static createSceneMapData(analysisResult: SceneAnalysisResult): SceneMapData {
    console.log('=== 创建 Scene_Map 数据 ===');

    // 获取当前地图ID
    const mapId = this.getCurrentMapId();
    console.log('当前地图ID:', mapId);

    // 转换对象数据
    const sceneObjects = this.convertToSpriteData(analysisResult.objects);

    // 提取 WindowLayer 对象
    const windowLayerObjects = this.extractWindowLayerObjects(analysisResult.objects);

    return {
      mapId,
      sceneClassName: analysisResult.sceneClassName,
      sceneObjects,
      windowLayerObjects,
      metadata: analysisResult.metadata
    };
  }

  /**
   * 获取当前地图ID
   */
  private static getCurrentMapId(): number {
    try {
      // 尝试从游戏数据获取当前地图ID
      const gameMap = (window as any).$gameMap;
      if (gameMap && gameMap.mapId) {
        return gameMap.mapId();
      }

      // 如果无法获取，返回默认值
      console.warn('无法获取当前地图ID，使用默认值 1');
      return 1;
    } catch (error) {
      console.error('获取地图ID时出错:', error);
      return 1;
    }
  }

  /**
   * 转换场景对象为 SpriteData 格式
   */
  private static convertToSpriteData(objects: SceneObjectInfo[]): SpriteData[] {
    const result: SpriteData[] = [];

    const convertObject = (obj: SceneObjectInfo): SpriteData => {
      return {
        className: obj.className,
        referenceName: obj.referenceName,
        properties: obj.properties,
        constructorParams: obj.constructorParams,
        children: obj.children ? obj.children.map(convertObject) : []
      };
    };

    for (const obj of objects) {
      // 跳过 WindowLayer 和 Spriteset_Map，它们会单独处理或已经存在
      if (obj.className !== 'WindowLayer' && obj.className !== 'Spriteset_Map') {
        result.push(convertObject(obj));
      }
    }

    return result;
  }

  /**
   * 提取 WindowLayer 对象
   */
  private static extractWindowLayerObjects(objects: SceneObjectInfo[]): any[] {
    console.log('=== 提取 WindowLayer 对象 ===');
    console.log('输入对象数量:', objects.length);
    console.log('输入对象类型:', objects.map(obj => obj.className));

    const windowLayerObjects: any[] = [];

    const findWindowLayers = (objList: SceneObjectInfo[], depth: number = 0) => {
      const indent = '  '.repeat(depth);
      console.log(`${indent}查找 WindowLayer，深度: ${depth}, 对象数量: ${objList.length}`);

      for (const obj of objList) {
        console.log(`${indent}检查对象: ${obj.className}`);

        if (obj.className === 'WindowLayer') {
          console.log(`${indent}发现 WindowLayer，子对象数量: ${obj.children ? obj.children.length : 0}`);
          windowLayerObjects.push(obj);
        }

        if (obj.children && obj.children.length > 0) {
          console.log(`${indent}递归检查 ${obj.className} 的 ${obj.children.length} 个子对象`);
          findWindowLayers(obj.children, depth + 1);
        }
      }
    };

    findWindowLayers(objects);

    console.log('提取到的 WindowLayer 对象数量:', windowLayerObjects.length);
    windowLayerObjects.forEach((obj, index) => {
      console.log(`WindowLayer[${index}]:`, {
        className: obj.className,
        childrenCount: obj.children ? obj.children.length : 0,
        children: obj.children ? obj.children.map((child: any) => child.className) : []
      });
    });

    return windowLayerObjects;
  }

  /**
   * 生成 Scene_Map 的 createDisplayObjects 重写代码
   * @param sceneMapData Scene_Map 数据
   * @returns 生成的代码
   */
  static generateCreateDisplayObjectsCode(sceneMapData: SceneMapData): string {
    let code = '';

    // 生成方法重写
    code += `// === 重写 Scene_Map.prototype.createDisplayObjects ===\n`;
    code += `// 地图ID: ${sceneMapData.mapId}\n`;
    code += `Scene_Map.prototype.createDisplayObjects = function() {\n`;
    code += `    // 创建精灵集\n`;
    code += `    this.createSpriteset();\n`;
    code += ` this.createWindowLayer();\n`;

    code += `    \n`;

    // 添加场景对象
    if (sceneMapData.sceneObjects.length > 0) {
      code += `    // === 编辑器创建的对象 ===\n`;
      code += `    \n`;

      // 为每个对象根据实际结构添加到对应的parent中
      for (const obj of sceneMapData.sceneObjects) {
        code += `    // 创建并添加 ${obj.className} 对象\n`;
        const objCode = this.generateObjectWithCorrectParent(obj, '    ');
        code += objCode;
        code += `    \n`;
      }
    }

    // 处理 WindowLayer 中的对象
    console.log('=== 处理 WindowLayer 对象 ===');
    console.log('WindowLayer 对象数量:', sceneMapData.windowLayerObjects.length);

    if (sceneMapData.windowLayerObjects.length > 0) {
      code += `    // === WindowLayer 中的对象 ===\n`;
      code += `    \n`;

      for (let i = 0; i < sceneMapData.windowLayerObjects.length; i++) {
        const windowLayerObj = sceneMapData.windowLayerObjects[i];
        console.log(`处理 WindowLayer[${i}]:`, {
          className: windowLayerObj.className,
          childrenCount: windowLayerObj.children ? windowLayerObj.children.length : 0
        });

        if (windowLayerObj.children && windowLayerObj.children.length > 0) {
          code += `    // WindowLayer[${i}] 子对象 (${windowLayerObj.children.length}个)\n`;

          // 处理 WindowLayer 中的每个子对象
          for (let j = 0; j < windowLayerObj.children.length; j++) {
            const child = windowLayerObj.children[j];
            console.log(`  处理子对象[${j}]:`, {
              className: child.className,
              referenceName: child.referenceName,
              properties: child.properties
            });

            // 检查是否是窗口类型
            const isWindow = isWindowType(child.className);
            console.log(`  ${child.className} 是否为窗口类型:`, isWindow);

            if (isWindow) {
              // 窗口类型对象通过 createAllWindows() 创建，这里不需要单独创建
              code += `    // ${child.className} 通过 createAllWindows() 创建\n`;
              console.log(`  ${child.className} 将通过 createAllWindows() 创建`);
            } else {
              // 非窗口类型对象需要手动创建并添加到 WindowLayer
              console.log(`  开始为 ${child.className} 生成创建代码`);
              const childCode = processMultipleSpriteData([child], '    ');
              console.log(`  生成的原始代码:`, childCode);

              const finalCode = childCode.replace(/this\.addChild/g, 'this._windowLayer.addChild');
              console.log(`  替换后的代码:`, finalCode);

              code += finalCode;
            }
          }
          code += `    \n`;
        } else {
          console.log(`WindowLayer[${i}] 没有子对象`);
        }
      }
    } else {
      console.log('没有 WindowLayer 对象需要处理');
    }

    // 检查是否存在窗口类型对象
    const hasWindowObjects = this.hasWindowObjects(sceneMapData);

    if (hasWindowObjects) {
      // 如果存在窗口类型，只调用系统方法创建所有窗口
      code += `    // 创建所有窗口（检测到窗口对象）\n`;
      code += `    this.createAllWindows();\n`;
      code += `    \n`;
    }
    // 如果没有窗口类型，不生成 createAllWindows() 调用

    code += `    // 创建按钮\n`;
    code += `    this.createButtons();\n`;
    code += `};\n\n`;

    // 添加窗口安全访问方法的重写
    code += this.generateWindowSafetyMethods();

    return code;
  }

  /**
   * 为单个对象生成代码并添加到正确的父容器
   * @param obj 对象数据
   * @param indent 缩进字符串
   * @returns 生成的代码
   */
  private static generateObjectWithCorrectParent(obj: SpriteData, indent: string): string {
    // 生成对象创建代码
    const objCode = processMultipleSpriteData([obj], indent);

    // 确定正确的父容器
    const parentContainer = this.determineCorrectParent(obj);

    // 替换 addChild 调用为正确的父容器
    let finalCode = objCode.replace(/this\.addChild/g, parentContainer);

    // 为场景对象添加地图滚动监听功能
    if (this.needsMapScrollListener(obj, parentContainer)) {
      // 从已生成的代码中提取变量名
      const varNameMatch = objCode.match(/const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=/);
      const varName = varNameMatch ? varNameMatch[1] : (obj.referenceName || `_${obj.className.toLowerCase()}_${Date.now()}`);

      finalCode += this.generateMapScrollListenerCode(obj, indent, varName);
    }

    return finalCode;
  }

  /**
   * 确定对象的正确父容器
   * @param obj 对象数据
   * @returns 父容器路径
   */
  private static determineCorrectParent(obj: SpriteData): string {
    console.log(`确定 ${obj.className} 的父容器...`);

    // 检查对象是否有存储的父容器信息
    const storedParent = (obj as any).parentContainer;
    if (storedParent) {
      console.log(`使用存储的父容器信息: ${storedParent}`);
      return storedParent;
    }

    // 检查对象的引用名称，这可以帮助确定它在场景中的位置
    const referenceName = obj.referenceName;
    if (referenceName) {
      console.log(`对象有引用名称: ${referenceName}`);

      // 根据引用名称推断父容器
      if (referenceName.includes('window') || referenceName.includes('Window')) {
        console.log(`根据引用名称判断为窗口对象，添加到 WindowLayer`);
        return 'this._windowLayer.addChild';
      }
    }

    // 根据对象类型确定默认父容器
    if (isWindowType(obj.className)) {
      console.log(`${obj.className} 是窗口类型，添加到 WindowLayer`);
      return 'this._windowLayer.addChild';
    }

    if (obj.className === 'TilingSprite' || obj.className === 'PIXI.TilingSprite') {
      console.log(`${obj.className} 是平铺精灵，添加到基础精灵层`);
      return 'this._spriteset._baseSprite.addChild';
    }

    // 检查对象属性中是否有层级信息
    const properties = obj.properties || {};

    // 如果对象有特殊的层级标记
    if (properties.layer === 'base') {
      console.log(`对象标记为 base 层`);
      return 'this._spriteset._baseSprite.addChild';
    } else if (properties.layer === 'tilemap') {
      console.log(`对象标记为 tilemap 层`);
      return 'this._spriteset._tilemap.addChild';
    } else if (properties.layer === 'top') {
      console.log(`对象标记为 top 层`);
      return 'this._spriteset.addChild';
    } else if (properties.layer === 'ui' || properties.layer === 'scene') {
      console.log(`对象标记为 UI 层`);
      return 'this.addChild';
    }

    // 根据对象的 z-index 或深度信息判断层级
    if (properties.z !== undefined || properties.zIndex !== undefined) {
      const z = properties.z || properties.zIndex;
      if (z < 0) {
        console.log(`对象 z-index < 0，添加到基础精灵层`);
        return 'this._spriteset._baseSprite.addChild';
      } else if (z > 100) {
        console.log(`对象 z-index > 100，添加到顶层`);
        return 'this._spriteset.addChild';
      }
    }

    // 检查对象是否是容器类型
    if (obj.className.includes('Container') || obj.className === 'PIXI.Container') {
      console.log(`${obj.className} 是容器类型，添加到场景根部`);
      return 'this.addChild';
    }

    // 默认添加到 tilemap（与角色同层）
    console.log(`${obj.className} 使用默认设置，添加到地图层`);
    return 'this._spriteset._tilemap.addChild';
  }

  /**
   * 判断对象是否需要地图滚动监听
   * @param obj 对象数据
   * @param parentContainer 父容器路径
   * @returns 是否需要地图滚动监听
   */
  private static needsMapScrollListener(obj: SpriteData, parentContainer: string): boolean {
    // 只有添加到场景层（spriteset相关层）的对象才需要地图滚动监听
    // UI对象（添加到scene根部或windowLayer）不需要跟随地图滚动
    const needsListener = parentContainer.includes('_spriteset');

    console.log(`${obj.className} 是否需要地图滚动监听: ${needsListener} (父容器: ${parentContainer})`);

    return needsListener;
  }

  /**
   * 生成地图滚动监听代码
   * @param obj 对象数据
   * @param indent 缩进字符串
   * @returns 地图滚动监听代码
   */
  private static generateMapScrollListenerCode(obj: SpriteData, indent: string): string {
    const properties = obj.properties || {};

    // 从 processMultipleSpriteData 生成的代码中提取变量名
    // 需要与对象创建代码中的变量名保持一致
    const tempCode = processMultipleSpriteData([obj], '');
    const varNameMatch = tempCode.match(/const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=/);
    const varName = varNameMatch ? varNameMatch[1] : (obj.referenceName || `_${obj.className.toLowerCase()}_${Date.now()}`);

    // 获取对象的地图坐标（如果有的话）
    const mapX = properties.mapX || properties.x || 0;
    const mapY = properties.mapY || properties.y || 0;

    let code = '';
    code += `${indent}// 为 ${obj.className} 添加地图滚动监听\n`;
    code += `${indent}if (${varName}) {\n`;
    code += `${indent}    // 保存地图坐标\n`;
    code += `${indent}    ${varName}.mapX = ${mapX};\n`;
    code += `${indent}    ${varName}.mapY = ${mapY};\n`;
    code += `${indent}    \n`;
    code += `${indent}    // 保存上一帧的屏幕坐标\n`;
    code += `${indent}    ${varName}._lastScreenX = ${varName}.x;\n`;
    code += `${indent}    ${varName}._lastScreenY = ${varName}.y;\n`;
    code += `${indent}    \n`;
    code += `${indent}    // 启用地图滚动跟踪\n`;
    code += `${indent}    ${varName}._mapScrollEnabled = true;\n`;
    code += `${indent}    \n`;
    code += `${indent}    // 保存原始的 update 方法\n`;
    code += `${indent}    const original_${varName.replace(/[^a-zA-Z0-9]/g, '_')}_Update = ${varName}.update;\n`;
    code += `${indent}    \n`;
    code += `${indent}    // 重写 update 方法\n`;
    code += `${indent}    ${varName}.update = function() {\n`;
    code += `${indent}        // 调用原始 update 方法\n`;
    code += `${indent}        if (original_${varName.replace(/[^a-zA-Z0-9]/g, '_')}_Update && typeof original_${varName.replace(/[^a-zA-Z0-9]/g, '_')}_Update === 'function') {\n`;
    code += `${indent}            original_${varName.replace(/[^a-zA-Z0-9]/g, '_')}_Update.call(this);\n`;
    code += `${indent}        }\n`;
    code += `${indent}        \n`;
    code += `${indent}        const gameMap = $gameMap;\n`;
    code += `${indent}        if (!gameMap || this.mapX === undefined || this.mapY === undefined || !this._mapScrollEnabled) {\n`;
    code += `${indent}            return;\n`;
    code += `${indent}        }\n`;
    code += `${indent}        \n`;
    code += `${indent}        // 检测坐标是否被外部修改\n`;
    code += `${indent}        const currentScrollX = gameMap.displayX() * gameMap.tileWidth();\n`;
    code += `${indent}        const currentScrollY = gameMap.displayY() * gameMap.tileHeight();\n`;
    code += `${indent}        const expectedScreenX = this.mapX - currentScrollX;\n`;
    code += `${indent}        const expectedScreenY = this.mapY - currentScrollY;\n`;
    code += `${indent}        \n`;
    code += `${indent}        // 检测坐标变化\n`;
    code += `${indent}        const xChanged = Math.abs(this.x - this._lastScreenX) > 0.1;\n`;
    code += `${indent}        const yChanged = Math.abs(this.y - this._lastScreenY) > 0.1;\n`;
    code += `${indent}        \n`;
    code += `${indent}        // 检测是否是地图滚动引起的变化\n`;
    code += `${indent}        const expectedXChange = Math.abs(expectedScreenX - this._lastScreenX) > 0.1;\n`;
    code += `${indent}        const expectedYChange = Math.abs(expectedScreenY - this._lastScreenY) > 0.1;\n`;
    code += `${indent}        \n`;
    code += `${indent}        // 如果坐标发生了变化，但不是由地图滚动引起的，说明被外部修改了\n`;
    code += `${indent}        if ((xChanged && !expectedXChange) || (yChanged && !expectedYChange)) {\n`;
    code += `${indent}            // 更新地图坐标以反映新的屏幕位置\n`;
    code += `${indent}            this.mapX = this.x + currentScrollX;\n`;
    code += `${indent}            this.mapY = this.y + currentScrollY;\n`;
    code += `${indent}        } else {\n`;
    code += `${indent}            // 正常情况下，根据地图滚动更新屏幕位置\n`;
    code += `${indent}            this.x = expectedScreenX;\n`;
    code += `${indent}            this.y = expectedScreenY;\n`;
    code += `${indent}        }\n`;
    code += `${indent}        \n`;
    code += `${indent}        // 更新上一帧坐标\n`;
    code += `${indent}        this._lastScreenX = this.x;\n`;
    code += `${indent}        this._lastScreenY = this.y;\n`;
    code += `${indent}    };\n`;
    code += `${indent}    \n`;
    code += `${indent}    // 初始位置设置\n`;
    code += `${indent}    const gameMap = $gameMap;\n`;
    code += `${indent}    if (gameMap) {\n`;
    code += `${indent}        const scrollX = gameMap.displayX() * gameMap.tileWidth();\n`;
    code += `${indent}        const scrollY = gameMap.displayY() * gameMap.tileHeight();\n`;
    code += `${indent}        ${varName}.x = ${varName}.mapX - scrollX;\n`;
    code += `${indent}        ${varName}.y = ${varName}.mapY - scrollY;\n`;
    code += `${indent}    }\n`;
    code += `${indent}    \n`;
    code += `${indent}    // 添加辅助方法：手动设置地图坐标\n`;
    code += `${indent}    ${varName}.setMapPosition = function(newMapX, newMapY) {\n`;
    code += `${indent}        this.mapX = newMapX;\n`;
    code += `${indent}        this.mapY = newMapY;\n`;
    code += `${indent}        \n`;
    code += `${indent}        // 立即更新屏幕位置\n`;
    code += `${indent}        const gameMap = $gameMap;\n`;
    code += `${indent}        if (gameMap) {\n`;
    code += `${indent}            const scrollX = gameMap.displayX() * gameMap.tileWidth();\n`;
    code += `${indent}            const scrollY = gameMap.displayY() * gameMap.tileHeight();\n`;
    code += `${indent}            this.x = this.mapX - scrollX;\n`;
    code += `${indent}            this.y = this.mapY - scrollY;\n`;
    code += `${indent}            this._lastScreenX = this.x;\n`;
    code += `${indent}            this._lastScreenY = this.y;\n`;
    code += `${indent}        }\n`;
    code += `${indent}    };\n`;
    code += `${indent}    \n`;
    code += `${indent}    // 添加辅助方法：设置屏幕坐标（会自动转换为地图坐标）\n`;
    code += `${indent}    ${varName}.setScreenPosition = function(newScreenX, newScreenY) {\n`;
    code += `${indent}        const gameMap = $gameMap;\n`;
    code += `${indent}        if (gameMap) {\n`;
    code += `${indent}            const scrollX = gameMap.displayX() * gameMap.tileWidth();\n`;
    code += `${indent}            const scrollY = gameMap.displayY() * gameMap.tileHeight();\n`;
    code += `${indent}            this.mapX = newScreenX + scrollX;\n`;
    code += `${indent}            this.mapY = newScreenY + scrollY;\n`;
    code += `${indent}        }\n`;
    code += `${indent}        \n`;
    code += `${indent}        this.x = newScreenX;\n`;
    code += `${indent}        this.y = newScreenY;\n`;
    code += `${indent}        this._lastScreenX = this.x;\n`;
    code += `${indent}        this._lastScreenY = this.y;\n`;
    code += `${indent}    };\n`;
    code += `${indent}    \n`;
    code += `${indent}    // 添加辅助方法：获取当前地图坐标\n`;
    code += `${indent}    ${varName}.getMapPosition = function() {\n`;
    code += `${indent}        return { x: this.mapX, y: this.mapY };\n`;
    code += `${indent}    };\n`;
    code += `${indent}}\n`;

    return code;
  }





  /**
   * 检查场景中是否存在窗口类型对象
   * @param sceneMapData Scene_Map 数据
   * @returns 是否存在窗口对象
   */
  private static hasWindowObjects(sceneMapData: SceneMapData): boolean {
    // 检查场景对象中是否有真正的窗口类型（使用继承检测）
    const hasSceneWindows = sceneMapData.sceneObjects.some(obj =>
      isWindowType(obj.className)
    );

    // 检查 WindowLayer 对象中是否有实际的窗口子对象
    const hasWindowLayerContent = this.hasWindowLayerContent(sceneMapData.windowLayerObjects);

    const result = hasSceneWindows || hasWindowLayerContent;

    console.log('检查窗口对象存在性:', {
      hasSceneWindows,
      hasWindowLayerContent,
      result,
      sceneObjectCount: sceneMapData.sceneObjects.length,
      windowLayerObjectCount: sceneMapData.windowLayerObjects.length
    });

    return result;
  }

  /**
   * 检查 WindowLayer 对象中是否有实际的窗口内容
   * @param windowLayerObjects WindowLayer 对象数组
   * @returns 是否有窗口内容
   */
  private static hasWindowLayerContent(windowLayerObjects: any[]): boolean {
    console.log('=== 检查 WindowLayer 内容 ===');
    console.log('WindowLayer 对象数量:', windowLayerObjects.length);

    if (windowLayerObjects.length === 0) {
      console.log('没有 WindowLayer 对象');
      return false;
    }

    // 打印 WindowLayer 对象的详细信息
    windowLayerObjects.forEach((obj, index) => {
      console.log(`WindowLayer[${index}]:`, {
        className: obj.className,
        childrenCount: obj.children ? obj.children.length : 0,
        children: obj.children ? obj.children.map((child: any) => child.className) : []
      });
    });

    // 递归检查 WindowLayer 及其子对象中是否有实际的窗口
    const checkForWindows = (objects: any[], depth: number = 0): boolean => {
      const indent = '  '.repeat(depth);
      console.log(`${indent}检查对象数组，深度: ${depth}, 对象数量: ${objects.length}`);

      for (const obj of objects) {
        console.log(`${indent}检查对象: ${obj.className}`);

        // 跳过 WindowLayer 本身，检查其子对象
        if (obj.className === 'WindowLayer') {
          console.log(`${indent}发现 WindowLayer，检查其子对象`);
          if (obj.children && obj.children.length > 0) {
            console.log(`${indent}WindowLayer 有 ${obj.children.length} 个子对象`);
            const hasWindows = checkForWindows(obj.children, depth + 1);
            if (hasWindows) {
              console.log(`${indent}在 WindowLayer 子对象中发现窗口`);
              return true;
            }
          } else {
            console.log(`${indent}WindowLayer 没有子对象`);
          }
        } else {
          // 检查是否是窗口类型（使用继承检测）
          const isWindow = isWindowType(obj.className);

          console.log(`${indent}对象 ${obj.className} 是否为窗口类型: ${isWindow}`);

          if (isWindow) {
            console.log(`${indent}发现窗口对象: ${obj.className}`);
            return true;
          }

          // 递归检查子对象
          if (obj.children && obj.children.length > 0) {
            console.log(`${indent}检查 ${obj.className} 的 ${obj.children.length} 个子对象`);
            if (checkForWindows(obj.children, depth + 1)) {
              return true;
            }
          }
        }
      }

      console.log(`${indent}在深度 ${depth} 没有发现窗口对象`);
      return false;
    };

    const result = checkForWindows(windowLayerObjects);
    console.log('WindowLayer 内容检查结果:', result);
    return result;
  }

  /**
   * 生成窗口安全访问方法的重写
   * 防止删除窗口后访问不存在的窗口对象时报错
   */
  private static generateWindowSafetyMethods(): string {
    let code = '';

    code += `// === 窗口安全访问方法重写 ===\n`;
    code += `// 防止删除窗口后访问不存在的窗口对象时报错\n\n`;

    // 重写 onTransferEnd 方法
    code += `// 重写 Scene_Map.prototype.onTransferEnd\n`;
    code += `Scene_Map.prototype.onTransferEnd = function() {\n`;
    code += `    // 安全访问 _mapNameWindow\n`;
    code += `    if (this._mapNameWindow && this._mapNameWindow.open) {\n`;
    code += `        this._mapNameWindow.open();\n`;
    code += `    }\n`;
    code += `    $gameMap.autoplay();\n`;
    code += `    if (this.shouldAutosave()) {\n`;
    code += `        this.requestAutosave();\n`;
    code += `    }\n`;
    code += `};\n\n`;

    // 重写 stop 方法
    code += `// 重写 Scene_Map.prototype.stop\n`;
    code += `Scene_Map.prototype.stop = function() {\n`;
    code += `    Scene_Message.prototype.stop.call(this);\n`;
    code += `    $gamePlayer.straighten();\n`;
    code += `    // 安全访问 _mapNameWindow\n`;
    code += `    if (this._mapNameWindow && this._mapNameWindow.close) {\n`;
    code += `        this._mapNameWindow.close();\n`;
    code += `    }\n`;
    code += `    if (this.needsSlowFadeOut()) {\n`;
    code += `        this.startFadeOut(this.slowFadeSpeed(), false);\n`;
    code += `    } else if (SceneManager.isNextScene(Scene_Map)) {\n`;
    code += `        this.fadeOutForTransfer();\n`;
    code += `    } else if (SceneManager.isNextScene(Scene_Battle)) {\n`;
    code += `        this.launchBattle();\n`;
    code += `    }\n`;
    code += `};\n\n`;

    // 重写 terminate 方法
    code += `// 重写 Scene_Map.prototype.terminate\n`;
    code += `Scene_Map.prototype.terminate = function() {\n`;
    code += `    Scene_Message.prototype.terminate.call(this);\n`;
    code += `    if (!SceneManager.isNextScene(Scene_Battle)) {\n`;
    code += `        this._spriteset.update();\n`;
    code += `        // 安全访问 _mapNameWindow\n`;
    code += `        if (this._mapNameWindow && this._mapNameWindow.hide) {\n`;
    code += `            this._mapNameWindow.hide();\n`;
    code += `        }\n`;
    code += `        this.hideMenuButton();\n`;
    code += `        SceneManager.snapForBackground();\n`;
    code += `    }\n`;
    code += `    $gameScreen.clearZoom();\n`;
    code += `};\n\n`;

    // 重写 updateMapNameWindow 方法
    code += `// 重写 Scene_Map.prototype.updateMapNameWindow\n`;
    code += `Scene_Map.prototype.updateMapNameWindow = function() {\n`;
    code += `    if ($gameMessage.isBusy()) {\n`;
    code += `        // 安全访问 _mapNameWindow\n`;
    code += `        if (this._mapNameWindow && this._mapNameWindow.close) {\n`;
    code += `            this._mapNameWindow.close();\n`;
    code += `        }\n`;
    code += `    }\n`;
    code += `};\n\n`;

    // 重写 callMenu 方法
    code += `// 重写 Scene_Map.prototype.callMenu\n`;
    code += `Scene_Map.prototype.callMenu = function() {\n`;
    code += `    SoundManager.playOk();\n`;
    code += `    SceneManager.push(Scene_Menu);\n`;
    code += `    Window_MenuCommand.initCommandPosition();\n`;
    code += `    $gameTemp.clearDestination();\n`;
    code += `    // 安全访问 _mapNameWindow\n`;
    code += `    if (this._mapNameWindow && this._mapNameWindow.hide) {\n`;
    code += `        this._mapNameWindow.hide();\n`;
    code += `    }\n`;
    code += `    this._waitCount = 2;\n`;
    code += `};\n\n`;

    // 重写 launchBattle 方法
    code += `// 重写 Scene_Map.prototype.launchBattle\n`;
    code += `Scene_Map.prototype.launchBattle = function() {\n`;
    code += `    BattleManager.saveBgmAndBgs();\n`;
    code += `    this.stopAudioOnBattleStart();\n`;
    code += `    SoundManager.playBattleStart();\n`;
    code += `    this.startEncounterEffect();\n`;
    code += `    // 安全访问 _mapNameWindow\n`;
    code += `    if (this._mapNameWindow && this._mapNameWindow.hide) {\n`;
    code += `        this._mapNameWindow.hide();\n`;
    code += `    }\n`;
    code += `};\n\n`;

    // 重写 Scene_Message 相关方法
    code += `// === Scene_Message 窗口安全访问方法重写 ===\n`;
    code += `// 防止删除消息窗口后访问不存在的窗口对象时报错\n\n`;

    // 重写 isMessageWindowClosing 方法
    code += `// 重写 Scene_Message.prototype.isMessageWindowClosing\n`;
    code += `Scene_Message.prototype.isMessageWindowClosing = function() {\n`;
    code += `    // 安全访问 _messageWindow\n`;
    code += `    return this._messageWindow && this._messageWindow.isClosing ? this._messageWindow.isClosing() : false;\n`;
    code += `};\n\n`;

    // 重写 associateWindows 方法
    code += `// 重写 Scene_Message.prototype.associateWindows\n`;
    code += `Scene_Message.prototype.associateWindows = function() {\n`;
    code += `    const messageWindow = this._messageWindow;\n`;
    code += `    if (!messageWindow) return; // 如果消息窗口不存在，直接返回\n`;
    code += `    \n`;
    code += `    // 安全设置各种窗口关联\n`;
    code += `    if (this._goldWindow) messageWindow.setGoldWindow(this._goldWindow);\n`;
    code += `    if (this._nameBoxWindow) messageWindow.setNameBoxWindow(this._nameBoxWindow);\n`;
    code += `    if (this._choiceListWindow) messageWindow.setChoiceListWindow(this._choiceListWindow);\n`;
    code += `    if (this._numberInputWindow) messageWindow.setNumberInputWindow(this._numberInputWindow);\n`;
    code += `    if (this._eventItemWindow) messageWindow.setEventItemWindow(this._eventItemWindow);\n`;
    code += `    \n`;
    code += `    // 安全设置反向关联\n`;
    code += `    if (this._nameBoxWindow && this._nameBoxWindow.setMessageWindow) {\n`;
    code += `        this._nameBoxWindow.setMessageWindow(messageWindow);\n`;
    code += `    }\n`;
    code += `    if (this._choiceListWindow && this._choiceListWindow.setMessageWindow) {\n`;
    code += `        this._choiceListWindow.setMessageWindow(messageWindow);\n`;
    code += `    }\n`;
    code += `    if (this._numberInputWindow && this._numberInputWindow.setMessageWindow) {\n`;
    code += `        this._numberInputWindow.setMessageWindow(messageWindow);\n`;
    code += `    }\n`;
    code += `    if (this._eventItemWindow && this._eventItemWindow.setMessageWindow) {\n`;
    code += `        this._eventItemWindow.setMessageWindow(messageWindow);\n`;
    code += `    }\n`;
    code += `};\n\n`;

    // 重写 cancelMessageWait 方法
    code += `// 重写 Scene_Message.prototype.cancelMessageWait\n`;
    code += `Scene_Message.prototype.cancelMessageWait = function() {\n`;
    code += `    // 安全访问 _messageWindow\n`;
    code += `    if (this._messageWindow && this._messageWindow.cancelWait) {\n`;
    code += `        this._messageWindow.cancelWait();\n`;
    code += `    }\n`;
    code += `};\n\n`;

    return code;
  }

  /**
   * 生成完整的 Scene_Map 插件代码
   * @param sceneMapData Scene_Map 数据
   * @returns 完整的插件代码
   */
  static generateCompletePlugin(sceneMapData: SceneMapData): string {
    let code = '';

    // 插件头部
    code += `// Scene_Map 编辑器插件\n`;
    code += `// 地图ID: ${sceneMapData.mapId}\n`;
    code += `// 生成时间: ${new Date().toISOString()}\n`;
    code += `// 对象数量: ${sceneMapData.sceneObjects.length}\n\n`;

    code += `(() => {\n`;
    code += `    'use strict';\n\n`;

    // 生成 createDisplayObjects 重写代码（已包含窗口安全访问方法）
    const createDisplayObjectsCode = this.generateCreateDisplayObjectsCode(sceneMapData);
    code += `    ${createDisplayObjectsCode.replace(/\n/g, '\n    ')}\n`;

    code += `})();\n`;

    return code;
  }

  /**
   * 验证 Scene_Map 数据
   */
  static validateSceneMapData(data: SceneMapData): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查基本字段
    if (!data.mapId || data.mapId <= 0) {
      errors.push('地图ID无效');
    }

    if (data.sceneClassName !== 'Scene_Map') {
      warnings.push(`场景类名不是 Scene_Map: ${data.sceneClassName}`);
    }

    if (!Array.isArray(data.sceneObjects)) {
      errors.push('sceneObjects 必须是数组');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
