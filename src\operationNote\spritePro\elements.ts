/**
 * Elements 数组处理模块
 * 负责处理 RPGEditor_BitmapTracker 插件的 elements 数组
 */

/**
 * Bitmap 文字属性列表
 */
const BITMAP_TEXT_PROPERTIES = [
  'fontBold', 'fontFace', 'fontItalic', 'fontSize',
  'outlineColor', 'outlineWidth', 'textColor',
  '_paintOpacity', '_smooth'
];

/**
 * 处理 elements 数组
 * @param elements elements 数组
 * @param properties 完整的属性对象（包含 bitmap 文字属性）
 * @param varName 变量名
 * @param indent 缩进字符串
 * @returns 生成的代码字符串
 */
export function processElements(
  elements: any[],
  properties: Record<string, any>,
  varName: string,
  indent: string
): string {
  console.log('=== 开始处理 elements 数组 ===');
  console.log(`Elements 数量: ${elements.length}`);

  let code = '';

  // 第一步：确保 bitmap 存在
  code += `${indent}// 确保 bitmap 存在（RPGEditor_BitmapTracker 插件）\n`;
  code += `${indent}if (!${varName}.bitmap) {\n`;
  code += `${indent}    ${varName}.bitmap = new Bitmap(Graphics.width, Graphics.height);\n`;
  code += `${indent}}\n`;

  // 第二步：设置 bitmap 的文字属性
  const bitmapPropsCode = processBitmapTextProperties(properties, varName, indent);
  if (bitmapPropsCode) {
    code += bitmapPropsCode;
  }

  // 第三步：检查是否有图片元素需要预加载
  const imageElements = elements.filter(el => el.type === 'image' && el.source);

  if (imageElements.length > 0) {
    code += processImageElements(elements, imageElements, varName, indent);
  } else {
    code += processTextOnlyElements(elements, varName, indent);
  }

  console.log('Elements 数组处理完成');
  return code;
}

/**
 * 处理 bitmap 文字属性
 */
function processBitmapTextProperties(
  properties: Record<string, any>,
  varName: string,
  indent: string
): string {
  let code = '';

  const hasTextProps = BITMAP_TEXT_PROPERTIES.some(prop =>
    properties.hasOwnProperty(prop) && properties[prop] !== undefined
  );

  if (!hasTextProps) {
    return code;
  }

  code += `${indent}// 设置 bitmap 文字属性\n`;

  for (const propName of BITMAP_TEXT_PROPERTIES) {
    if (properties.hasOwnProperty(propName) && properties[propName] !== undefined) {
      const value = properties[propName];
      const formattedValue = typeof value === 'string' ? `"${value}"` : value;
      code += `${indent}${varName}.bitmap.${propName} = ${formattedValue};\n`;
    }
  }

  return code;
}

/**
 * 处理包含图片元素的 elements 数组
 */
function processImageElements(
  elements: any[],
  imageElements: any[],
  varName: string,
  indent: string
): string {
  let code = '';

  code += `${indent}// 预加载图片元素\n`;
  code += `${indent}const imagePromises = [];\n`;

  // 为每个图片元素生成预加载代码
  imageElements.forEach((imgElement, index) => {
    code += `${indent}imagePromises.push(new Promise((resolve) => {\n`;

    const { folder, filename } = parseBitmapPath(imgElement.source);
    if (folder && filename) {
      code += `${indent}    const bitmap = ImageManager.loadBitmap('${folder}', '${filename}');\n`;
    } else {
      code += `${indent}    const bitmap = ImageManager.loadBitmapFromUrl('${imgElement.source}');\n`;
    }

    code += `${indent}    bitmap.addLoadListener(() => resolve(bitmap));\n`;
    code += `${indent}}));\n`;
  });

  // 等待所有图片加载完成后重建 elements 并重绘
  code += `${indent}// 等待所有图片加载完成后重建 elements 并重绘\n`;
  code += `${indent}Promise.all(imagePromises).then((loadedBitmaps) => {\n`;

  // 重建完整的 elements 数组
  code += `${indent}    // 重建 elements 数组\n`;
  const elementsJson = JSON.stringify(elements, null, 4);
  const indentedJson = elementsJson.replace(/\n/g, `\n${indent}    `);
  code += `${indent}    ${varName}.bitmap.elements = ${indentedJson};\n`;

  // 将加载的 bitmap 对象重新赋值给图片元素
  let imageIndex = 0;
  elements.forEach((element, elemIndex) => {
    if (element.type === 'image' && element.source) {
      code += `${indent}    ${varName}.bitmap.elements[${elemIndex}].source = loadedBitmaps[${imageIndex}];\n`;
      imageIndex++;
    }
  });

  code += `${indent}    // 调用重绘方法\n`;
  code += `${indent}    ${varName}.bitmap.redrawing();\n`;
  code += `${indent}});\n`;

  return code;
}

/**
 * 处理只包含文字元素的 elements 数组
 */
function processTextOnlyElements(
  elements: any[],
  varName: string,
  indent: string
): string {
  let code = '';

  // 重建 elements 数组
  code += `${indent}// 重建 elements 数组\n`;
  const elementsJson = JSON.stringify(elements, null, 4);
  const indentedJson = elementsJson.replace(/\n/g, `\n${indent}`);
  code += `${indent}${varName}.bitmap.elements = ${indentedJson};\n`;

  code += `${indent}// 调用重绘方法\n`;
  code += `${indent}${varName}.bitmap.redrawing();\n`;

  return code;
}

/**
 * 解析位图路径，分离文件夹和文件名
 */
function parseBitmapPath(bitmapPath: string): { folder: string; filename: string } {
  // 移除 .png 扩展名
  const pathWithoutExt = bitmapPath.replace(/\.png$/i, '');

  // 查找最后一个斜杠
  const lastSlashIndex = pathWithoutExt.lastIndexOf('/');

  if (lastSlashIndex === -1) {
    // 没有斜杠，整个路径就是文件名
    return { folder: '', filename: pathWithoutExt };
  }

  // 分离文件夹和文件名
  const folder = pathWithoutExt.substring(0, lastSlashIndex + 1); // 包含最后的斜杠
  const filename = pathWithoutExt.substring(lastSlashIndex + 1);

  return { folder, filename };
}
