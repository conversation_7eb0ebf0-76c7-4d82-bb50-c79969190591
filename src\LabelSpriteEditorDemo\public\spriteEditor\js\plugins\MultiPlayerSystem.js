//=============================================================================
// RPG Maker MZ - Multi Player System
//=============================================================================

/*:
 * @target MZ
 * @plugindesc 在本地创建和管理多个玩家角色
 * <AUTHOR> AI
 *
 * @param maxPlayers
 * @text 最大玩家数
 * @type number
 * @min 2
 * @max 4
 * @default 2
 * @desc 可以同时存在的最大玩家数量
 *
 * @param playerSpacing
 * @text 玩家间距
 * @type number
 * @min 1
 * @default 48
 * @desc 玩家之间的默认间距（像素）
 *
 * @command createPlayer
 * @text 创建新玩家
 * @desc 在当前地图上创建一个新的玩家角色
 *
 * @arg characterIndex
 * @type number
 * @min 0
 * @max 7
 * @default 0
 * @text 角色索引
 * @desc 角色图像的索引号(0-7)
 *
 * @arg characterName
 * @type string
 * @default Actor1
 * @text 角色图像
 * @desc 角色图像的文件名
 *
 * @help MultiPlayerSystem.js
 *
 * 这个插件允许在本地创建多个玩家角色。
 *
 * 插件命令：
 * 1. 创建新玩家 - 在当前地图上创建一个新的玩家角色
 */

(() => {
  const pluginName = "MultiPlayerSystem";
  const parameters = PluginManager.parameters(pluginName);
  const maxPlayers = Number(parameters.maxPlayers || 2);
  const playerSpacing = Number(parameters.playerSpacing || 48);

  // 存储所有玩家实例
  const _players = [];

  // 扩展Game_Player类来支持多玩家
  const _Game_Player_initialize = Game_Player.prototype.initialize;
  Game_Player.prototype.initialize = function () {
    _Game_Player_initialize.call(this);
  };

  // 创建新的玩家类
  class Game_SubPlayer extends Game_Player {
    constructor(playerId) {
      super();
      this.playerId = playerId;
      _players[playerId] = this;
      console.log(`创建了新玩家 ID: ${playerId}`, this);
    }

    name() {
      return this._characterName;
    }
  }

  // 注册插件命令
  PluginManager.registerCommand(pluginName, "createPlayer", (args) => {
    console.log("开始创建新玩家", args);

    if (_players.length >= maxPlayers) {
      console.log("已达到最大玩家数限制");
      return;
    }

    // 从1开始分配ID，0留给主玩家
    let playerId = 1;
    while (_players[playerId]) {
      playerId++;
    }
    if (playerId >= maxPlayers) {
      console.log("已达到最大玩家数限制");
      return;
    }
    const newPlayer = new Game_SubPlayer(playerId);

    // 设置新玩家的图像
    const characterName = String(args.characterName || "Actor1");
    const characterIndex = Number(args.characterIndex || 0);
    newPlayer.setImage(characterName, characterIndex);
    console.log("设置新玩家图像", characterName, characterIndex);

    // 设置新玩家的初始位置（在当前玩家附近）
    const currentPlayer = $gamePlayer;
    const spacing = playerSpacing;
    newPlayer.setPosition(currentPlayer.x + spacing / 48, currentPlayer.y);
    newPlayer.name = "6666666666666666";
    console.log("设置新玩家位置", newPlayer.x, newPlayer.y);

    // 将新玩家添加到地图并确保显示
    if (SceneManager._scene instanceof Scene_Map) {
      console.log("当前场景是地图场景，添加玩家精灵");
      addPlayerSprite(newPlayer);
    } else {
      console.log("当前不是地图场景，无法添加玩家精灵");
    }
  });

  // 添加玩家精灵到地图
  function addPlayerSprite(player) {
    const scene = SceneManager._scene;
    if (scene instanceof Scene_Map) {
      const spriteset = scene._spriteset;
      if (spriteset) {
        console.log("开始创建玩家精灵");
        const sprite = new Sprite_Character(player);
        // 设置精灵的初始位置
        sprite.x = player._realX * 48;
        sprite.y = player._realY * 48;
        // 添加到精灵列表和tilemap
        spriteset._characterSprites.push(sprite);
        spriteset._tilemap.addChild(sprite);
        // 更新精灵图像并设置位置
        sprite.update();
        sprite.updatePosition();
        sprite.updateCharacterFrame();
        console.log("玩家精灵创建完成，位置:", sprite.x, sprite.y);
      } else {
        console.log("无法获取spriteset");
      }
    }
  }

  // 扩展Scene_Map来处理多玩家
  const _Scene_Map_createSpriteset = Scene_Map.prototype.createSpriteset;
  Scene_Map.prototype.createSpriteset = function () {
    _Scene_Map_createSpriteset.call(this);
    console.log("场景初始化，检查现有玩家");

    // 自动创建一个测试玩家
    if (_players.length === 0) {
      console.log("自动创建测试玩家");
      PluginManager.callCommand(this, pluginName, "createPlayer", {
        characterName: "Actor1",
        characterIndex: 1,
      });
    }

    // 为所有已存在的子玩家创建精灵
    for (let i = 1; i < _players.length; i++) {
      if (_players[i]) {
        console.log(`为玩家 ${i} 创建精灵`);
        addPlayerSprite(_players[i]);
      }
    }
  };

  // 扩展Scene_Map的更新函数来处理所有玩家的更新
  const _Scene_Map_update = Scene_Map.prototype.update;
  Scene_Map.prototype.update = function () {
    _Scene_Map_update.call(this);
    // 更新所有玩家的精灵
    if (this._spriteset && this._spriteset._characterSprites) {
      this._spriteset._characterSprites.forEach((sprite) => {
        if (
          sprite &&
          sprite._character &&
          (sprite._character instanceof Game_Player ||
            sprite._character instanceof Game_SubPlayer) &&
          sprite.bitmap
        ) {
          sprite.updatePosition();
          sprite.updateCharacterFrame();
        }
      });
    }
  };
})();
