import React from 'react'
import { Box } from '@mui/material'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import './App.css'
import Canvas from './components/Canvas'
import ElementsList from './components/ElementsList'
import TextSettings from './components/TextSettings'


// 启用React-scan性能监控
// if (typeof window !== 'undefined') {
//   scan({
//     enabled: true,
//     log: true, // 控制台显示渲染日志
//   });
// }

// 创建主题
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#9c27b0',
    },
  },
});

// 使用React.memo包装组件，避免不必要的重新渲染
const App = React.memo(() => {
  console.log("App渲染");

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{
        width: '100%',
        height: '100vh',
        p: 1,
        bgcolor: '#f5f5f5',
        overflow: 'hidden'
      }}>
        <Box sx={{
          display: 'flex',
          height: '100%',
          border: '1px solid #ddd',
          borderRadius: 1,
          overflow: 'hidden',
          bgcolor: '#fff',
          boxShadow: 1
        }}>
          <Box sx={{
            flex: 3,
            borderRight: '1px solid #ddd',
            position: 'relative',
            bgcolor: '#f9f9f9',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <Box sx={{ borderBottom: '1px solid #ddd', bgcolor: '#f0f0f0' }}>
              <TextSettings />
            </Box>
            <Box sx={{ flex: 1, position: 'relative' }}>
              <Canvas />
            </Box>
          </Box>
          <Box sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            minWidth: 260,
            maxWidth: 300,
            bgcolor: '#fff'
          }}>
            <ElementsList />
          </Box>
        </Box>
      </Box>
    </ThemeProvider>
  );
});

export default App;
