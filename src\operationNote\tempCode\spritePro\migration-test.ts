/**
 * 迁移测试文件
 * 验证从旧 spriteTemplate API 迁移到新 SpritePro API 的效果
 */

import { processSpriteData, processMultipleSpriteData, type SpriteData } from './index';

/**
 * 测试数据：模拟从旧系统迁移过来的数据
 */
const testSprites: SpriteData[] = [
  // 背景精灵1
  {
    className: 'Sprite',
    referenceName: '_backSprite1',
    properties: {
      anchorX: 0.5,
      anchorY: 0.5,
      width: 816,
      height: 624,
      x: 408,
      y: 312,
      url: 'img/titles1/Ruins.png'
    }
  },
  
  // 背景精灵2
  {
    className: 'Sprite',
    referenceName: '_backSprite2',
    properties: {
      anchorX: 0.5,
      anchorY: 0.5,
      width: 1,
      height: 1,
      x: 408,
      y: 312
    },
    constructorParams: {
      bitmap: '$dataSystem.title2Name'
    }
  },
  
  // 游戏标题精灵（包含 elements 数组）
  {
    className: 'Sprite',
    referenceName: '_gameTitleSprite',
    properties: {
      width: 816,
      height: 624,
      fontBold: false,
      fontFace: "rmmz-mainfont, Microsoft Yahei, PingFang SC, sans-serif",
      fontItalic: false,
      fontSize: 72,
      outlineColor: "black",
      outlineWidth: 8,
      textColor: "#ffffff",
      _paintOpacity: 255,
      _smooth: true,
      elements: [
        {
          "type": "text",
          "text": "Project6",
          "x": 20,
          "y": 156,
          "maxWidth": 776,
          "lineHeight": 48,
          "align": "center",
          "bounds": {
            "x": 20,
            "y": 156,
            "width": 776,
            "height": 48
          }
        }
      ]
    }
  }
];

/**
 * 运行迁移测试
 */
export function runMigrationTest(): void {
  console.log('=== SpritePro 迁移测试开始 ===\n');
  
  // 测试单个 Sprite 处理
  console.log('1. 测试单个 Sprite 处理：');
  const singleCode = processSpriteData(testSprites[0]);
  console.log(singleCode);
  
  // 测试批量 Sprite 处理
  console.log('2. 测试批量 Sprite 处理：');
  const batchCode = processMultipleSpriteData(testSprites);
  console.log(batchCode);
  
  // 测试 WindowLayer
  console.log('3. 测试 WindowLayer 处理：');
  const windowLayerData: SpriteData = {
    className: 'WindowLayer',
    referenceName: '_windowLayer',
    properties: {
      x: 4,
      y: 4
    }
  };
  const windowLayerCode = processSpriteData(windowLayerData);
  console.log(windowLayerCode);
  
  console.log('=== SpritePro 迁移测试完成 ===');
}

/**
 * 对比新旧 API 生成的代码差异
 */
export function compareOldVsNew(): void {
  console.log('=== 新旧 API 代码对比 ===\n');
  
  console.log('新 SpritePro API 生成的代码：');
  const newCode = processMultipleSpriteData(testSprites, '    ');
  console.log(newCode);
  
  console.log('\n预期的代码结构：');
  console.log(`
    // 创建 Sprite
    const _backSprite1 = new Sprite();
    // 设置基础属性
    _backSprite1.anchor.x = 0.5;
    _backSprite1.anchor.y = 0.5;
    _backSprite1.width = 816;
    _backSprite1.height = 624;
    _backSprite1.x = 408;
    _backSprite1.y = 312;
    _backSprite1.bitmap = ImageManager.loadBitmap('img/titles1/', 'Ruins');
    this.addChild(_backSprite1);
  `);
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  runMigrationTest();
  compareOldVsNew();
}
