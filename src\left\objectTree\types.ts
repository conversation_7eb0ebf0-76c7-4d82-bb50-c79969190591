/**
 * 对象树相关类型定义
 */

import type { NodeTypeInfo } from './typeUtils';

/**
 * 对象树节点数据
 */
export interface ObjectTreeNodeData {
  /** 当前对象实例 */
  currentObject: any;

  /** 是否为根节点 */
  isRoot: boolean;

  /** 对象类型（根节点保存） */
  objectType?: string;

  /** 是否为 RPG Maker MZ 原生类型 */
  isRPGMakerType: boolean;

  /** 子对象数组 */
  children: ObjectTreeNodeData[];

  /** 展开状态 */
  expanded: boolean;

  /** 节点唯一标识 */
  id: string;

  /** 节点显示名称 */
  displayName: string;

  /** 节点层级深度 */
  depth: number;

  /** 节点类型信息 */
  typeInfo?: NodeTypeInfo;
}

/**
 * 全局对象状态
 */
export interface GlobalObjectState {
  /** 根对象 */
  rootObject: any | null;

  /** 根对象的类型 */
  rootObjectType: string | null;

  /** 对象树数据 */
  objectTreeData: ObjectTreeNodeData[];

  /** 选中的节点ID */
  selectedNodeId: string | null;

  /** 当前选中的对象数组（支持多选） */
  selectedObject: any[] | null;

  /** 当前选中对象的类型数组 */
  selectedObjectType: string[] | null;

  /** 最后更新时间戳，用于强制触发更新 */
  lastUpdateTime?: number;
}

/**
 * 对象树配置
 */
export interface ObjectTreeConfig {
  /** 是否显示属性 */
  showProperties: boolean;

  /** 是否显示方法 */
  showMethods: boolean;

  /** 最大展开深度 */
  maxDepth: number;

  /** 是否自动展开根节点 */
  autoExpandRoot: boolean;
}

/**
 * 节点操作类型
 */
export enum NodeActionType {
  SELECT = 'select',
  EXPAND = 'expand',
  COLLAPSE = 'collapse',
  REFRESH = 'refresh',
  CONTEXT_MENU = 'context_menu',
  CREATE_OBJECT = 'create_object',
  DELETE_OBJECT = 'delete_object'
}

/**
 * 节点操作事件
 */
export interface NodeActionEvent {
  type: NodeActionType;
  nodeId: string;
  nodeData: ObjectTreeNodeData;
  contextMenuData?: ContextMenuData;
  createObjectType?: string;
}

/**
 * 右键菜单数据
 */
export interface ContextMenuData {
  x: number;
  y: number;
  visible: boolean;
  targetNode: ObjectTreeNodeData | null;
}

/**
 * 可创建的对象类型
 */
export enum CreatableObjectType {
  SPRITE = 'Sprite',
  LABEL = 'Label',
  CONTAINER = 'Container',
  WINDOW = 'Window',
  BUTTON = 'Button'
}

/**
 * 右键菜单项
 */
export interface ContextMenuItem {
  id: string;
  label: string;
  icon?: string;
  action: () => void;
  disabled?: boolean;
  separator?: boolean;
}

/**
 * 对象创建参数
 */
export interface ObjectCreationParams {
  name?: string;
  x?: number;
  y?: number;
  visible?: boolean;
  text?: string; // 用于 Label 和 Button
}
