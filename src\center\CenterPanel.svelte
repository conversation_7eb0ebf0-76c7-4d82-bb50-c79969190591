<script lang="ts">
  /**
   * 中央面板组件
   * 使用 Tab 组件管理 Preview 和 ClassTree 页面
   */

  import Tab from '../components/Tab.svelte';
  import type { TabItem } from '../components/Tab.svelte';
  import Preview from './preview.svelte';
  import ClassTreePanel from './classTree/classTreePanel.svelte';

  // Tab 配置
  const tabs: TabItem[] = [
    { id: 'preview', label: '游戏预览', icon: '🎮' },
    { id: 'classTree', label: '类继承图', icon: '🌳' }
  ];

  let activeTab = $state('preview');

  function handleTabChange(tabId: string) {
    console.log('切换到标签页:', tabId);
    activeTab = tabId;
  }
</script>

<div class="center-panel">
  <div class="content">
    <Tab
      {tabs}
      {activeTab}
      ontabchange={handleTabChange}
      variant="underline"
      fullWidth={true}
    >
      {#snippet children(currentTab: string)}
        <div class="tab-content-container">
          <!-- 始终渲染所有组件，通过 CSS 控制显示/隐藏 -->
          <div class="tab-panel" class:active={currentTab === 'preview'}>
            <Preview />
          </div>
          <div class="tab-panel" class:active={currentTab === 'classTree'}>
            <ClassTreePanel />
          </div>
        </div>
      {/snippet}
    </Tab>
  </div>
</div>

<style>
  .center-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-background);
    overflow: hidden;
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .tab-content-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .tab-panel {
    flex: 1;
    display: none;
    flex-direction: column;
    overflow: hidden;
  }

  .tab-panel.active {
    display: flex !important;
  }

  /* 确保子组件能够占满空间 */
  .tab-panel :global(.preview-container),
  .tab-panel :global(.class-tree-panel) {
    height: 100%;
    flex: 1;
  }

  /* 确保 Tab 组件占满空间 */
  .content :global(.tab-content) {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 !important; /* 移除 Tab 组件的默认 padding */
  }

  .content :global(.tab-container) {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
</style>