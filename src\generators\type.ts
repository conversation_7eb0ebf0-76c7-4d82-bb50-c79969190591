
export interface OperationRecordsData {
  version: string;
  timestamp: number;
  author: string;
  records: Record<string, SceneAnalysisResult>;
}


export interface SceneAnalysisResult {
  // 场景基本信息
  sceneClassName: string;
  //  路径-> 对象
  uiObjects: Map<string, BaseDisplayProperties>;     // WindowLayer 数据（独立结构）
}

/**
 * 基础显示对象属性 (PIXI.DisplayObject)
 */
export interface BaseDisplayProperties {
  className: string;               // 对象类名
  name: string;
  // 位置和变换
  x: number;
  y: number;
  scaleX: number;    // scale.x
  scaleY: number;    // scale.y
  skewX: number;     // skew.x
  skewY: number;     // skew.y
  rotation: number;
  width: number;                  // 宽度
  height: number;                 // 高度
  // 显示属性
  alpha: number;
  visible: boolean;

  // 锚点和轴心
  anchorX: number;   // anchor.x
  anchorY: number;   // anchor.y
  pivotX: number;    // pivot.x
  pivotY: number;    // pivot.y
}


export interface spriteProperties extends BaseDisplayProperties {
  // 混合模式
  blendMode: number;
  // 遮罩和滤镜 (复杂对象，需要特殊处理)
  mask?: any;
  filters?: any[];
  // 新增：层级信息
  zIndex?: number;                 // Z轴层级
  bitmap: bitmapProperties;
}



export interface bitmapProperties {
  fontBold: boolean,
  fontFace: string,
  fontItalic: boolean,
  fontSize: number,
  outlineColor: string,
  outlineWidth: number,
  textColor: string,
  _paintOpacity: number,
  _smooth: boolean,
  elements?: any[],
  url?: string,
  regions?: {
    id?: string,
    label?: string,
    sx: number,
    sy: number,
    sw: number,
    sh: number
  }[];
}

// 2. 添加类型联合，便于类型检查
export type ObjectProperties = BaseDisplayProperties | spriteProperties;