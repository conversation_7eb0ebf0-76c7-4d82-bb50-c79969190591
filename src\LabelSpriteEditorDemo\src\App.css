/* 全局样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f4f4f4;
}

#root {
  width: 100%;
  height: 100vh;
}

/* 应用容器 */
.app-container {
  width: 100%;
  height: 100vh;
  padding: 20px;
  overflow: hidden;
}

/* 编辑器布局 */
.editor-layout {
  display: flex;
  height: 100%;
  border: 1px solid #ddd;
  border-radius: 5px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* 画布容器 */
.canvas-container {
  flex: 3;
  border-right: 1px solid #ddd;
  overflow: hidden;
  position: relative;
  background-color: #f9f9f9;
}

/* 侧边栏 */
.sidebar {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 260px;
  max-width: 320px;
  background-color: #fff;
}

/* 按钮容器 */
.buttons-container {
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #ddd;
  background-color: #f5f5f5;
}

/* 按钮样式 */
.add-button {
  padding: 8px 16px;
  margin-right: 10px;
  border: none;
  border-radius: 4px;
  background-color: #4CAF50;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-button:hover {
  background-color: #45a049;
}

.add-button:active {
  background-color: #3e8e41;
}

/* 元素列表容器 */
.elements-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

/* 元素项样式 */
.element-item {
  padding: 6px;
  margin-bottom: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
  transition: all 0.2s;
  font-size: 0.9em;
}

/* 元素内容样式 */
.element-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* 元素属性样式 */
.element-properties {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.element-item:hover {
  background-color: #f0f0f0;
  border-color: #ccc;
}

.element-item.selected {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

/* 文本元素样式 */
.text-element {
  color: #1890ff;
}

/* 图片元素样式 */
.image-element {
  color: #722ed1;
}

/* 元素操作按钮 */
.element-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
}

.element-actions button {
  padding: 3px 8px;
  margin-left: 5px;
  border: none;
  border-radius: 3px;
  background-color: #f0f0f0;
  cursor: pointer;
}

.element-actions button:hover {
  background-color: #e0e0e0;
}

.edit-button {
  color: #1890ff;
}

.delete-button {
  color: #f5222d;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  width: 400px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.form-group {
  margin-bottom: 15px;
}

.form-row {
  display: flex;
  gap: 10px;
}

.form-row .form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.submit-button,
.cancel-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.submit-button {
  background-color: #4CAF50;
  color: white;
  margin-right: 10px;
}

.submit-button:hover {
  background-color: #45a049;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

/* 空列表提示 */
.empty-list {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.empty-message {
  color: #999;
  text-align: center;
  padding: 20px;
}

/* 列表标题 */
.list-title {
  margin-top: 0;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

/* 编辑表单 */
.edit-form {
  margin-bottom: 8px;
}

.edit-form input {
  width: 100%;
  padding: 4px;
  margin-bottom: 4px;
  border: 1px solid #ddd;
  border-radius: 3px;
}

/* NumberInput组件样式 */
.number-input {
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.number-input label {
  font-size: 0.85em;
  color: #555;
  flex: 1;
}

.number-input input,
.number-value {
  width: 50px;
  padding: 2px 4px;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 0.9em;
  text-align: right;
  background-color: #f5f5f5;
}

.number-value {
  cursor: ew-resize;
  user-select: none;
  color: #0066cc;
}

.number-value:hover {
  background-color: #e0e0e0;
}

/* 属性编辑区域 */
.edit-properties {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
  margin-bottom: 8px;
}

.edit-text {
  margin-bottom: 8px;
}

.edit-text label {
  display: block;
  margin-bottom: 3px;
  font-weight: bold;
  font-size: 0.9em;
}

.edit-actions {
  display: flex;
  justify-content: flex-end;
}

.edit-actions button {
  padding: 3px 8px;
  margin-left: 5px;
  border: none;
  border-radius: 3px;
  background-color: #f0f0f0;
  cursor: pointer;
}

.edit-actions button:first-child {
  background-color: #4CAF50;
  color: white;
}

/* 元素预览和详情 */
.element-preview {
  font-weight: bold;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-input-container {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.element-preview .text-input {
  flex: 1;
  padding: 3px 6px;
  border: 1px solid #ddd;
  border-radius: 3px 0 0 3px;
  font-size: 0.95em;
  border-right: none;
}

.confirm-button {
  padding: 3px 8px;
  background-color: #4CAF50;
  color: white;
  border: 1px solid #4CAF50;
  border-radius: 0 3px 3px 0;
  cursor: pointer;
  font-size: 0.85em;
  white-space: nowrap;
}

.confirm-button:hover {
  background-color: #45a049;
}

.element-details {
  font-size: 0.85em;
  color: #666;
  line-height: 1.4;
}

/* 复选框样式 */
input[type="checkbox"] {
  margin-right: 5px;
}

/* 帮助文本样式 */
.form-help-text {
  font-size: 0.8em;
  color: #666;
  margin-top: 5px;
  margin-bottom: 10px;
  font-style: italic;
}
