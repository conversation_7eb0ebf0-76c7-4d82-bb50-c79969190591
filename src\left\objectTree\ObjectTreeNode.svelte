<script lang="ts">
  import type { ObjectTreeNodeData, NodeActionEvent, NodeActionType, ContextMenuData } from './types';
  import { globalObjectState } from '../../stores/objectState';
  import ObjectTreeNode from './ObjectTreeNode.svelte';
  import ContextMenu from './ContextMenu.svelte';
  import { handleContextMenu, closeContextMenu } from './contextMenuLogic';
  import { handleObjectCreation } from './objectCreationLogic';
  import { handleObjectDeletion } from './objectDeletionLogic';

  // Props
  interface Props {
    node: ObjectTreeNodeData;
    isSelected?: boolean;
    onNodeAction?: (event: NodeActionEvent) => void;
    contextMenuData?: ContextMenuData;
    onContextMenuUpdate?: (data: ContextMenuData) => void;
  }

  let {
    node,
    isSelected = false,
    onNodeAction,
    contextMenuData = { x: 0, y: 0, visible: false, targetNode: null },
    onContextMenuUpdate
  }: Props = $props();

  // 使用全局状态管理展开状态
  let isExpanded = $derived(node.expanded);

  // 计算缩进
  let indentStyle = $derived(`margin-left: ${node.depth * 20}px`);

  // 节点图标 - 使用类型信息
  let nodeIcon = $derived(() => {
    // 如果是窗口类型，显示锁图标
    if (node.typeInfo?.isWindowType) {
      return '🔒';
    }
    // 优先使用类型信息中的图标
    if (node.typeInfo?.ico) {
      return node.typeInfo.ico;
    } else if (node.typeInfo?.parentClassInfo?.ico) {
      return node.typeInfo.parentClassInfo.ico;
    } else if (node.children.length > 0) {
      return isExpanded ? '📂' : '📁';
    } else if (node.isRPGMakerType) {
      return '🎮';
    } else {
      return '📄';
    }
  });

  // 展开/收起图标 - 使用全局状态
  let expandIcon = $derived(() => {
    if (node.children.length === 0) return '';
    return isExpanded ? '▼' : '▶';
  });

  // 处理节点点击
  function handleNodeClick() {
    const event: NodeActionEvent = {
      type: 'select' as NodeActionType,
      nodeId: node.id,
      nodeData: node
    };
    onNodeAction?.(event);
    console.log('节点被选中:', node.displayName,node.currentObject);
  }

  // 处理展开/收起点击 - 使用全局状态管理
  function handleExpandClick(event: MouseEvent) {
    event.stopPropagation(); // 阻止事件冒泡

    console.log('展开/收起按钮被点击:', {
      nodeId: node.id,
      displayName: node.displayName,
      currentExpanded: isExpanded,
      childrenCount: node.children.length
    });

    if (node.children.length > 0) {
      // 通过事件通知父组件切换状态，不直接修改本地状态
      const actionEvent: NodeActionEvent = {
        type: isExpanded ? 'collapse' as NodeActionType : 'expand' as NodeActionType,
        nodeId: node.id,
        nodeData: node
      };
      onNodeAction?.(actionEvent);

      console.log('节点展开状态切换事件已发送:', {
        displayName: node.displayName,
        action: actionEvent.type
      });
    } else {
      console.log('节点没有子节点，无法展开/收起');
    }
  }

  // 获取节点类型信息
  let nodeTypeInfo = $derived(() => {
    if (node.isRoot && node.objectType) {
      return `根节点 - ${node.objectType}`;
    } else if (node.typeInfo?.isTypeNode && node.typeInfo.classType) {
      return `类型节点 - ${node.typeInfo.classType}`;
    } else if (node.typeInfo?.inheritedType) {
      return `继承类型 - ${node.typeInfo.inheritedType}`;
    } else if (node.isRPGMakerType) {
      return 'RPG Maker MZ 类型';
    } else {
      return '自定义类型';
    }
  });

  // 获取模型对象信息
  let modelObjectInfo = $derived(() => {
    if (node.typeInfo?.hasModelObject && node.typeInfo.modelClassName) {
      return `模型: ${node.typeInfo.modelClassName}`;
    }
    return null;
  });



  // 处理右键菜单
  function handleRightClick(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    // 如果是窗口类型，不显示右键菜单
    if (node.typeInfo?.isWindowType) {
      console.log('窗口类型对象不可编辑:', node.displayName);
      return;
    }

    console.log('右键点击节点:', node.displayName);

    // 使用右键菜单逻辑处理
    handleContextMenu(event, node);

    // 更新上下文菜单数据
    const newContextMenuData: ContextMenuData = {
      x: event.clientX,
      y: event.clientY,
      visible: true,
      targetNode: node
    };

    onContextMenuUpdate?.(newContextMenuData);
  }

  // 处理创建对象
  function handleCreateObject(objectType: string, targetNode: ObjectTreeNodeData) {
    console.log('创建对象:', objectType, '目标节点:', targetNode.displayName);
    handleObjectCreation(objectType, targetNode);
  }

  // 处理删除对象
  async function handleDeleteObject(targetNode: ObjectTreeNodeData) {
    console.log('删除对象:', targetNode.displayName);
    await handleObjectDeletion(targetNode);
  }

  // 处理窗口对象删除
  async function handleDeleteWindowObject(event: MouseEvent) {
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault();

    console.log('删除窗口对象:', node.displayName);

    try {
      // 从父对象中移除当前对象
      if (node.currentObject && node.currentObject.parent) {
        const parent = node.currentObject.parent;
        if (parent.removeChild && typeof parent.removeChild === 'function') {
          parent.removeChild(node.currentObject);
          console.log('已从父对象中移除窗口对象:', node.displayName);
        }
      }

      // 调用通用删除逻辑
      await handleObjectDeletion(node);

      console.log('窗口对象删除成功:', node.displayName);
    } catch (error) {
      console.error('删除窗口对象失败:', error);
    }
  }

  // 关闭右键菜单
  function handleCloseContextMenu() {
    const newContextMenuData: ContextMenuData = {
      x: 0,
      y: 0,
      visible: false,
      targetNode: null
    };

    onContextMenuUpdate?.(newContextMenuData);
    closeContextMenu();
  }
</script>

<div
  class="tree-node"
  class:selected={isSelected}
  class:rpg-maker-type={node.isRPGMakerType}
  class:custom-type={!node.isRPGMakerType}
  class:root-node={node.isRoot}
  class:window-type={node.typeInfo?.isWindowType}
  style={indentStyle}
  onclick={handleNodeClick}
  oncontextmenu={handleRightClick}
  onkeydown={(e) => e.key === 'Enter' && handleNodeClick()}
  role="button"
  tabindex="0"
  title={nodeTypeInfo()}
>
  <!-- 展开/收起按钮 -->
  {#if node.children.length > 0 && !node.typeInfo?.isWindowType}
    <button
      class="expand-button"
      onclick={handleExpandClick}
      aria-label={isExpanded ? '收起' : '展开'}
    >
      {expandIcon()}
    </button>
  {:else}
    <span class="expand-spacer"></span>
  {/if}

  <!-- 节点图标 -->
  <span class="node-icon">{nodeIcon()}</span>

  <!-- 节点标签 -->
  <span class="node-label" title="调试: displayName={node.displayName}, type={typeof node.displayName}">
    {node.displayName || '未知对象'}
  </span>

  <!-- 窗口类型的操作按钮 -->
  {#if node.typeInfo?.isWindowType}
    <div class="window-actions">
      <button
        class="window-action-button delete-button"
        onclick={handleDeleteWindowObject}
        title="删除窗口对象"
        aria-label="删除窗口对象"
      >
        🗑️
      </button>
    </div>
  {/if}

  <!-- 类型标识 -->
  {#if node.typeInfo?.isTypeNode && node.typeInfo.classType}
    <span class="type-badge type-node">{node.typeInfo.classType}</span>
  {:else if node.typeInfo?.inheritedType}
    <span class="type-badge inherited-type">{node.typeInfo.inheritedType}</span>
  {/if}

  <!-- 模型对象标识 -->
  {#if node.typeInfo?.hasModelObject && node.typeInfo.modelClassName}
    <span class="model-badge" title="关联模型对象: {node.typeInfo.modelClassName}">
      🔗 {node.typeInfo.modelClassName}
    </span>
  {/if}



  <!-- 子节点数量指示器 -->
  {#if node.children.length > 0}
    <span class="children-count">({node.children.length})</span>
  {/if}

  <!-- 根节点类型标识 -->
  {#if node.isRoot && node.objectType}
    <span class="root-type-badge">{node.objectType}</span>
  {/if}
</div>

<!-- 递归渲染子节点 - 使用全局状态 -->
{#if isExpanded && node.children.length > 0}
  <div class="children-container">
    {#each node.children as childNode (childNode.id)}
      <ObjectTreeNode
        node={childNode}
        isSelected={$globalObjectState.selectedNodeId === childNode.id}
        {onNodeAction}
        {contextMenuData}
        {onContextMenuUpdate}
      />
    {/each}
  </div>
{/if}

<!-- 右键菜单组件 -->
{#if contextMenuData.visible && contextMenuData.targetNode?.id === node.id}
  <ContextMenu
    {contextMenuData}
    onCreateObject={handleCreateObject}
    onDeleteObject={handleDeleteObject}
    onClose={handleCloseContextMenu}
  />
{/if}

<style>
  .tree-node {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    cursor: pointer;
    user-select: none;
    border-radius: 4px;
    transition: all 0.2s ease;
    min-height: 28px;
    gap: 6px;
  }

  .tree-node:hover {
    background-color: var(--theme-surface-hover, rgba(0, 0, 0, 0.05));
  }

  .tree-node.selected {
    background-color: var(--theme-primary-light, rgba(33, 150, 243, 0.2));
    border: 1px solid var(--theme-primary, #2196F3);
  }

  /* RPG Maker MZ 类型样式 */
  .tree-node.rpg-maker-type {
    background-color: rgba(33, 150, 243, 0.1);
    border-left: 3px solid #2196F3;
  }

  .tree-node.rpg-maker-type .node-label {
    color: #1976D2;
    font-weight: 600;
  }

  /* 自定义类型样式 */
  .tree-node.custom-type {
    background-color: rgba(76, 175, 80, 0.1);
    border-left: 3px solid #4CAF50;
  }

  .tree-node.custom-type .node-label {
    color: #388E3C;
    font-weight: 500;
  }

  /* 根节点特殊样式 */
  .tree-node.root-node {
    font-weight: 700;
    border-radius: 6px;
    margin-bottom: 2px;
  }

  .tree-node.root-node.rpg-maker-type {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.15), rgba(33, 150, 243, 0.05));
    border: 2px solid #2196F3;
    border-left: 4px solid #2196F3;
  }

  .tree-node.root-node.custom-type {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.15), rgba(76, 175, 80, 0.05));
    border: 2px solid #4CAF50;
    border-left: 4px solid #4CAF50;
  }

  /* 窗口类型样式 - 锁定状态 */
  .tree-node.window-type {
    background-color: rgba(158, 158, 158, 0.1);
    border-left: 3px solid #9E9E9E;
    opacity: 0.7;
    cursor: not-allowed;
  }

  .tree-node.window-type .node-label {
    color: #757575;
    font-style: italic;
  }

  .tree-node.window-type:hover {
    background-color: rgba(158, 158, 158, 0.15);
  }

  /* 窗口操作按钮样式 */
  .window-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: auto;
  }

  .window-action-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 2px 4px;
    font-size: 12px;
    border-radius: 3px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
  }

  .window-action-button:hover {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
  }

  .window-action-button.delete-button {
    color: #f44336;
  }

  .window-action-button.delete-button:hover {
    background-color: rgba(244, 67, 54, 0.1);
    color: #d32f2f;
  }

  /* 确保窗口类型节点有足够空间容纳操作按钮 */
  .tree-node.window-type {
    padding-right: 12px;
  }

  .expand-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 2px;
    font-size: 12px;
    color: var(--theme-text-secondary, #666);
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    transition: all 0.2s ease;
  }

  .expand-button:hover {
    background-color: var(--theme-surface-hover, rgba(0, 0, 0, 0.1));
    color: var(--theme-text, #333);
  }

  .expand-spacer {
    width: 16px;
    height: 16px;
  }

  .node-icon {
    font-size: 14px;
    width: 18px;
    text-align: center;
  }

  .node-label {
    flex: 1;
    font-size: 13px;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .children-count {
    font-size: 11px;
    color: var(--theme-text-secondary, #666);
    background-color: var(--theme-surface, rgba(0, 0, 0, 0.05));
    padding: 1px 4px;
    border-radius: 8px;
    min-width: 16px;
    text-align: center;
  }

  .root-type-badge {
    font-size: 10px;
    color: var(--theme-text-secondary, #666);
    background-color: var(--theme-surface, rgba(0, 0, 0, 0.1));
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  /* 类型标识样式 */
  .type-badge {
    font-size: 9px;
    padding: 1px 4px;
    border-radius: 6px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    margin-left: 4px;
  }

  .type-badge.type-node {
    background-color: rgba(33, 150, 243, 0.2);
    color: #1976D2;
    border: 1px solid rgba(33, 150, 243, 0.4);
  }

  .type-badge.inherited-type {
    background-color: rgba(156, 39, 176, 0.2);
    color: #7B1FA2;
    border: 1px solid rgba(156, 39, 176, 0.4);
  }

  /* 模型对象标识样式 */
  .model-badge {
    font-size: 9px;
    background-color: rgba(255, 152, 0, 0.2);
    color: #F57C00;
    padding: 1px 4px;
    border-radius: 6px;
    font-weight: 600;
    border: 1px solid rgba(255, 152, 0, 0.4);
    margin-left: 4px;
    display: flex;
    align-items: center;
    gap: 2px;
  }



  .children-container {
    margin-left: 0;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .tree-node {
      padding: 6px 4px;
      min-height: 32px;
    }

    .node-label {
      font-size: 12px;
    }

    .children-count,
    .root-type-badge,
    .type-badge,
    .model-badge {
      font-size: 7px;
    }
  }
</style>
