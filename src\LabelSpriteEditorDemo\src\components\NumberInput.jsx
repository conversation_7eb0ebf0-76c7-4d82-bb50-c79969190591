import { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Typography, TextField } from '@mui/material';
import { styled } from '@mui/material/styles';

// 静态变量，用于跟踪拖拽状态
const DragState = {
  isDragging: false,
  startValue: 0,
  startX: 0,
  currentOnChange: null,
  step: 1,
  min: undefined,
  max: undefined
};

// 使用 styled API 创建自定义样式的 Box 组件
const DraggableValueBox = styled(Box)(({ theme }) => ({
  cursor: 'ew-resize',
  userSelect: 'none',
  padding: '2px 6px',
  margin: 0,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center', // 文字居中
  height: '20px',
  color: theme.palette.primary.main,
  position: 'relative',
  backgroundColor: 'transparent', // 透明背景
  borderRadius: 0, // 无圆角
  border: 'none', // 无边框
  minWidth: '30px', // 最小宽度
  '&:hover': {
    backgroundColor: theme.palette.grey[100], // 悬停时轻微背景色
  },
}));

const NumberInput = ({ label, value, onChange, min, max, step = 1 }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState(Math.floor(value).toString() || '0');
  const inputRef = useRef(null);
  const elementRef = useRef(null);

  // 当值变化时更新输入值，只显示整数部分
  useEffect(() => {
    if (!isEditing) {
      setInputValue(Math.floor(value).toString() || '0');
    }
  }, [value, isEditing]);

  // 当进入编辑模式时聚焦输入框并设置初始宽度
  useEffect(() => {
    if (isEditing && inputRef.current) {
      // 聚焦输入框
      inputRef.current.focus();

      // 设置初始宽度
      const textWidth = getTextWidth(inputValue) + 10;
      const width = Math.max(20, textWidth);
      inputRef.current.style.width = `${width}px`;
    }
  }, [isEditing, inputValue]);

  // 双击进入编辑模式
  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  // 鼠标按下开始拖动
  const handlePointerDown = (e) => {
    // 如果正在编辑或者已经有其他组件在拖拽中，则不处理
    if (isEditing || DragState.isDragging) return;

    // 存储拖拽状态
    DragState.isDragging = true;
    DragState.startValue = value || 0;
    DragState.startX = e.clientX;
    DragState.currentOnChange = onChange;
    DragState.step = step;
    DragState.min = min;
    DragState.max = max;

    // 设置指针捕获，确保即使鼠标移出元素也能接收事件
    if (e.currentTarget && 'setPointerCapture' in e.currentTarget) {
      try {
        e.currentTarget.setPointerCapture(e.pointerId);
        elementRef.current = e.currentTarget;
      } catch (err) {
        console.error("Error setting pointer capture:", err);
      }
    }

    // 定义鼠标移动处理函数
    const handleMouseMove = (moveEvent) => {
      moveEvent.preventDefault();
      if (!DragState.isDragging) return;

      // 计算拖动距离
      const diff = moveEvent.clientX - DragState.startX;
      const sensitivity = 1.0;

      // 计算新值
      let newValue = DragState.startValue + diff * sensitivity * DragState.step;

      // 应用最小/最大限制
      if (DragState.min !== undefined) newValue = Math.max(DragState.min, newValue);
      if (DragState.max !== undefined) newValue = Math.min(DragState.max, newValue);

      // 四舍五入到整数
      newValue = Math.round(newValue);

      // 调用onChange回调
      if (DragState.currentOnChange) {
        DragState.currentOnChange(newValue);
      }
    };

    // 定义鼠标释放处理函数
    const handleMouseUp = (upEvent) => {
      // 释放指针捕获
      if (elementRef.current && 'releasePointerCapture' in elementRef.current) {
        try {
          const pointerId = upEvent.pointerId;
          if (pointerId) {
            elementRef.current.releasePointerCapture(pointerId);
          }
        } catch (err) {
          console.error("Error releasing pointer capture:", err);
        }
      }

      // 重置拖拽状态
      DragState.isDragging = false;
      DragState.currentOnChange = null;

      // 移除事件监听器
      window.removeEventListener("pointermove", handleMouseMove);
      window.removeEventListener("pointerup", handleMouseUp);
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);

      // 恢复鼠标样式
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    // 添加事件监听
    window.addEventListener("pointermove", handleMouseMove);
    window.addEventListener("pointerup", handleMouseUp);
    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("mouseup", handleMouseUp);

    // 设置鼠标样式
    document.body.style.cursor = 'ew-resize';
    document.body.style.userSelect = 'none';

    // 防止文本选择
    e.preventDefault();
  };

  // 计算文本宽度的辅助函数
  const getTextWidth = (text, font = '0.8rem Arial') => {
    // 创建一个临时的 canvas 元素
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    context.font = font;
    // 测量文本宽度
    const metrics = context.measureText(text);
    return metrics.width;
  };

  // 输入框变化处理
  const handleChange = (e) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // 如果输入框引用存在，动态调整宽度
    if (inputRef.current) {
      // 计算文本宽度，加上一些内边距
      const textWidth = getTextWidth(newValue) + 10;
      // 设置最小宽度
      const width = Math.max(20, textWidth);
      // 直接设置输入元素的宽度
      inputRef.current.style.width = `${width}px`;
    }
  };

  // 输入框失焦处理
  const handleBlur = () => {
    setIsEditing(false);

    // 解析输入值，确保是整数
    let newValue = parseInt(inputValue, 10);
    if (!isNaN(newValue)) {
      // 应用最小/最大限制
      let validValue = newValue;
      if (min !== undefined) validValue = Math.max(min, validValue);
      if (max !== undefined) validValue = Math.min(max, validValue);

      // 调用onChange回调
      onChange(validValue);
    } else {
      // 如果输入无效，恢复原值
      setInputValue(Math.floor(value).toString() || '0');
    }
  };

  // 键盘事件处理
  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleBlur();
    } else if (e.key === "Escape") {
      setInputValue(value?.toString() || '0');
      setIsEditing(false);
    }
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
      <Typography variant="body2" sx={{ mr: 0.5, flex: 1, color: 'text.secondary', fontSize: '0.8rem' }}>
        {label}:
      </Typography>
      {isEditing ? (
        <TextField
          inputRef={inputRef}
          value={inputValue}
          onChange={handleChange}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          type="text" // 改为text类型，避免显示增减按钮
          size="small"
          variant="standard" // 使用标准变体，无边框
          // 直接设置输入属性
          slotProps={{
            input: {
              min,
              max,
              step,
              style: {
                textAlign: 'center', // 文字居中
                padding: '0px',
                fontSize: '0.8rem'
              }
            }
          }}
          sx={{
            minWidth: '20px', // 更小的最小宽度
            width: 'auto', // 宽度自适应
            maxWidth: '50px', // 限制最大宽度
            '& input': {
              textAlign: 'center', // 文字居中
              padding: '0px',
              fontSize: '0.8rem',
              width: 'auto', // 输入框宽度自适应
              boxSizing: 'content-box' // 使用内容盒模型计算宽度
            },
            '& .MuiInputBase-root': {
              height: '20px',
              width: 'auto', // 根组件宽度自适应
              display: 'inline-flex' // 使用内联弹性布局
            },
            '& .MuiInput-underline:before': {
              borderBottom: 'none' // 移除下划线
            },
            '& .MuiInput-underline:hover:before': {
              borderBottom: '1px solid rgba(0, 0, 0, 0.42)' // 悬停时显示下划线
            },
            '& .MuiInput-underline:after': {
              borderBottom: '1px solid #1976d2' // 聚焦时的下划线颜色
            }
          }}
        />
      ) : (
        <DraggableValueBox
          onDoubleClick={handleDoubleClick}
          onPointerDown={handlePointerDown}
          ref={elementRef}
        >
          <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
            {Math.floor(value)} {/* 只显示整数部分 */}
          </Typography>
        </DraggableValueBox>
      )}
    </Box>
  );
};

NumberInput.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.number.isRequired,
  onChange: PropTypes.func.isRequired,
  min: PropTypes.number,
  max: PropTypes.number,
  step: PropTypes.number
};

export default NumberInput;
