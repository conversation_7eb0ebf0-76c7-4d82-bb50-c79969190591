# Sprite 处理器 (SpritePro)

## 概述

SpritePro 是一个分层设计的 Sprite 处理器，用于根据保存的数据生成 Sprite 对象的代码模板。采用模块化设计，每个功能独立成模块，便于扩展和维护。

## 架构设计

### 分层处理流程

```
输入数据 (SpriteData)
    ↓
1. 对象创建层 (creation.ts)
    ↓
2. 基础属性层 (basicProperties.ts)
    ↓
3. 特殊内容层 (elements.ts 或 url.ts)
    ↓
4. 添加到父容器
    ↓
5. 子对象递归处理
    ↓
输出代码模板
```

### 模块职责

#### 1. `index.ts` - 主入口
- 对外暴露唯一的处理方法 `processSpriteData()`
- 协调各个模块的处理流程
- 处理子对象的递归

#### 2. `creation.ts` - 对象创建模块
- 生成对象创建代码
- 处理特殊对象类型（WindowLayer、Window_*）
- 生成唯一变量名

#### 3. `basicProperties.ts` - 基础属性模块
- 处理基础属性：位置、尺寸、锚点、缩放、倾斜、透明度、可见性
- 属性优先级排序（避免设置冲突）
- 默认值过滤和数值验证

#### 4. `elements.ts` - Elements 数组模块
- 处理 RPGEditor_BitmapTracker 插件的 elements 数组
- 处理 bitmap 文字属性
- 图片预加载和异步处理

#### 5. `url.ts` - URL 处理模块
- 处理 bitmap URL 或图片路径
- 特殊精灵的默认 bitmap 处理
- 路径解析和格式化

## 基础属性列表

```typescript
const BASIC_PROPERTIES = [
  'name',
  'x', 'y', 'width', 'height',           // 位置和尺寸 (数值)
  'anchorX', 'anchorY',                  // 锚点 (数值)
  'scaleX', 'scaleY',                    // 缩放 (数值)
  'skewX', 'skewY',                      // 倾斜 (数值)
  'alpha',                               // 透明度 (数值)
  'visible'                              // 可见性 (布尔)
];
```

## 互斥规则

- **elements 数组** 和 **url** 只能同时存在一个
- 如果存在 `elements` 数组，优先处理 elements
- 如果不存在 `elements` 但存在 `url` 或 `constructorParams.bitmap`，处理 url

## 使用方法

### 基本用法

```typescript
import { processSpriteData, type SpriteData } from './spritePro';

const spriteData: SpriteData = {
  className: 'Sprite',
  referenceName: '_mySprite',
  properties: {
    x: 100,
    y: 200,
    anchorX: 0.5,
    anchorY: 0.5,
    url: 'img/pictures/Actor1.png'
  }
};

const code = processSpriteData(spriteData);
console.log(code);
```

### 生成的代码示例

```javascript
// 创建 Sprite
const _mySprite = new Sprite();
// 设置基础属性
_mySprite.anchor.x = 0.5;
_mySprite.anchor.y = 0.5;
_mySprite.x = 100;
_mySprite.y = 200;
_mySprite.bitmap = ImageManager.loadBitmap('img/pictures/', 'Actor1');
this.addChild(_mySprite);
```

### Elements 数组示例

```typescript
const elementsSprite: SpriteData = {
  className: 'Sprite',
  referenceName: '_textSprite',
  properties: {
    fontSize: 24,
    textColor: '#ffffff',
    elements: [
      {
        type: 'text',
        text: 'Hello World',
        x: 10,
        y: 10,
        align: 'center'
      }
    ]
  }
};
```

## 扩展指南

### 添加新的基础属性

1. 在 `basicProperties.ts` 中的 `BASIC_PROPERTIES` 数组添加属性名
2. 在 `DEFAULT_VALUES` 中添加默认值
3. 在 `generatePropertyAssignment` 函数中添加处理逻辑

### 添加新的特殊内容处理

1. 在 `spritePro` 文件夹下创建新的模块文件
2. 在 `index.ts` 中导入并调用新模块
3. 确保与现有模块的互斥关系

### 添加新的对象类型

1. 在 `creation.ts` 中的 `generateSpriteCreation` 函数添加特殊处理
2. 在 `getActualClassName` 函数中添加类名映射（如果需要）

## 优势

1. **模块化设计**：每个功能独立，便于维护和扩展
2. **分层处理**：按逻辑层次处理，避免代码混乱
3. **类型安全**：完整的 TypeScript 类型定义
4. **互斥处理**：elements 和 url 的互斥逻辑清晰
5. **属性优先级**：基础属性按正确顺序设置，避免冲突
6. **扩展友好**：新增属性或功能只需修改对应模块
