/**
 * 响应式 Bitmap 类
 * 继承自 RPG Maker MZ 的 Bitmap 类，并添加 Svelte 5 的响应式特性
 */

// 导入 Svelte 5 的 $state
// 注意：$state 是 Svelte 5 的编译时特性，在运行时不需要导入
// import { $state } from 'svelte';

// 声明 RPG Maker MZ 的 Bitmap 类（全局可用）


declare global {
    interface Bitmap {
        fontBold: boolean,
        fontFace: string,
        fontItalic: boolean,
        fontSize: number,
        outlineColor: string,
        outlineWidth: number,
        textColor: string,
        _paintOpacity: number,
        _smooth: boolean,
        elements?: Element[],
        url: string,
        regions?: {
            id?: string,
            label?: string,
            sx: number,
            sy: number,
            sw: number,
            sh: number
        }[];
        redrawing?(): void;
    }
}

// 定义元素类型
export interface TextElement {
    type: 'text';
    id: string;  // 🔧 改为必需的
    text: string;
    x: number;
    y: number;
    maxWidth?: number;
    lineHeight?: number;
    align?: 'left' | 'center' | 'right';
}

export interface ImageElement {
    type: 'image';
    id: string;  // 🔧 改为必需的
    source: any; // Bitmap 对象
    sx: number;  // 源图像裁剪 x
    sy: number;  // 源图像裁剪 y
    sw: number;  // 源图像裁剪宽度
    sh: number;  // 源图像裁剪高度
    dx: number;  // 目标绘制 x
    dy: number;  // 目标绘制 y
    dw: number;  // 目标绘制宽度
    dh: number;  // 目标绘制高度
    regions?: RegionData[];
}

export type Element = TextElement | ImageElement;

// 定义区域数据类型
export interface RegionData {
    id?: string;
    label?: string;
    sx: number;
    sy: number;
    sw: number;
    sh: number;
    gridIndex?: number;
}


/**
 * 响应式 Bitmap 模型类
 * 纯数据模型，所有字段使用 $state 实现响应式，单向同步到 ReactiveBitmap
 */
export class BitmapModel {
    // 🔧 基本尺寸属性（响应式）
    width = $state(800);
    height = $state(600);

    // 🔧 字体属性（响应式）
    fontBold = $state(false);
    fontFace = $state('GameFont');
    fontItalic = $state(false);
    fontSize = $state(16);

    // 🔧 颜色属性（响应式）
    textColor = $state('#ffffff');
    outlineColor = $state('rgba(0, 0, 0, 0.5)');
    outlineWidth = $state(0);

    // 🔧 显示属性（响应式）
    _paintOpacity = $state(255);
    _smooth = $state(false);

    // 🔧 内容属性（响应式）
    elements = $state<Element[]>([]);
    regions = $state<RegionData[]>([]);



    // 🔧 保存的原始 bitmap 实例（保持引用不断开）
    private _originalBitmap: any = null;

    // 🔧 更新回调函数
    private _onUpdate: (() => void) | null = null;

    constructor(input?: any) {
        if (!input) {
            // 无参数：创建默认模型
            this.initializeDefaults();
        } else if (this.isBitmapObject(input)) {
            // 参数是 bitmap 对象：从 bitmap 初始化模型并保存引用
            this.initializeFromBitmap(input);
        } else if (input instanceof BitmapModel) {
            // 参数是 BitmapModel：复制模型数据并创建新的 bitmap
            this.initializeFromModel(input);
        } else {
            // 其他情况：当作普通数据对象处理
            this.initializeFromData(input);
        }
    }

    /**
     * 判断是否为 bitmap 对象
     */
    private isBitmapObject(obj: any): boolean {
        return obj && (
            // RPG Maker MZ Bitmap 对象特征
            (typeof obj.drawText === 'function' && typeof obj.blt === 'function') ||
            // 或者有 _canvas 属性
            obj._canvas ||
            // 或者有 redrawing 方法
            typeof obj.redrawing === 'function'
        );
    }

    /**
     * 初始化默认值
     */
    private initializeDefaults(): void {
        // 使用默认值，已经在属性声明时设置
        console.log('🔧 BitmapModel: 创建默认模型');
    }

    /**
     * 从 bitmap 对象初始化模型
     */
    private initializeFromBitmap(bitmap: any): void {
        // 保存原始 bitmap 引用（重要：不能断开）
        this._originalBitmap = bitmap;

        // 从 bitmap 同步属性到模型
        this.width = bitmap.width || 800;
        this.height = bitmap.height || 600;
        this.fontBold = bitmap.fontBold ?? false;
        this.fontFace = bitmap.fontFace ?? 'GameFont';
        this.fontItalic = bitmap.fontItalic ?? false;
        this.fontSize = bitmap.fontSize ?? 16;
        this.textColor = bitmap.textColor ?? '#ffffff';
        this.outlineColor = bitmap.outlineColor ?? 'rgba(0, 0, 0, 0.5)';
        this.outlineWidth = bitmap.outlineWidth ?? 0;
        this._paintOpacity = bitmap._paintOpacity ?? 255;
        this._smooth = bitmap._smooth ?? false;

        // 同步内容属性
        this.elements = bitmap.elements ? this.processElementsFromBitmap(bitmap.elements) : [];
        this.regions = bitmap.regions ? [...bitmap.regions] : [];

        console.log('🔧 BitmapModel: 从 bitmap 初始化模型完成', {
            width: this.width,
            height: this.height,
            elementsCount: this.elements.length,
            originalBitmap: this._originalBitmap
        });
    }

    /**
     * 从另一个 BitmapModel 初始化
     */
    private initializeFromModel(model: BitmapModel): void {
        // 复制所有属性
        this.width = model.width;
        this.height = model.height;
        this.fontBold = model.fontBold;
        this.fontFace = model.fontFace;
        this.fontItalic = model.fontItalic;
        this.fontSize = model.fontSize;
        this.textColor = model.textColor;
        this.outlineColor = model.outlineColor;
        this.outlineWidth = model.outlineWidth;
        this._paintOpacity = model._paintOpacity;
        this._smooth = model._smooth;
        this.elements = [...model.elements];
        this.regions = [...model.regions];

        // 创建新的 bitmap 实例
        this.createNewBitmap();

        console.log('🔧 BitmapModel: 从另一个模型初始化完成', {
            width: this.width,
            height: this.height,
            elementsCount: this.elements.length,
            newBitmap: this._originalBitmap
        });
    }

    /**
     * 创建新的 bitmap 实例
     */
    private createNewBitmap(): void {
        try {
            if (typeof window !== 'undefined' && window.Bitmap) {
                this._originalBitmap = new window.Bitmap(this.width, this.height);

                // 同步属性到新创建的 bitmap
                this.syncToOriginalBitmap();

                // 🔧 设置响应式同步
                this.setupOneWaySync();

                console.log('🔧 BitmapModel: 创建新 bitmap 成功');
            } else {
                console.warn('⚠️ RPG Maker MZ 环境未加载，无法创建 bitmap');
            }
        } catch (error) {
            console.error('❌ 创建 bitmap 失败:', error);
        }
    }

    /**
     * 同步模型属性到原始 bitmap
     */
    private syncToOriginalBitmap(): void {
        if (!this._originalBitmap) return;

        this._originalBitmap.fontBold = this.fontBold;
        this._originalBitmap.fontFace = this.fontFace;
        this._originalBitmap.fontItalic = this.fontItalic;
        this._originalBitmap.fontSize = this.fontSize;
        this._originalBitmap.textColor = this.textColor;
        this._originalBitmap.outlineColor = this.outlineColor;
        this._originalBitmap.outlineWidth = this.outlineWidth;
        this._originalBitmap._paintOpacity = this._paintOpacity;
        this._originalBitmap._smooth = this._smooth;
        this._originalBitmap.elements = this.elements;
        this._originalBitmap.regions = this.regions;
    }

    /**
     * 从普通数据对象初始化
     */
    private initializeFromData(data: any): void {
        // 同步基本属性
        this.width = data.width || 800;
        this.height = data.height || 600;

        // 同步字体属性
        this.fontBold = data.fontBold ?? false;
        this.fontFace = data.fontFace ?? 'GameFont';
        this.fontItalic = data.fontItalic ?? false;
        this.fontSize = data.fontSize ?? 16;

        // 同步颜色属性
        this.textColor = data.textColor ?? '#ffffff';
        this.outlineColor = data.outlineColor ?? 'rgba(0, 0, 0, 0.5)';
        this.outlineWidth = data.outlineWidth ?? 0;

        // 同步显示属性
        this._paintOpacity = data._paintOpacity ?? 255;
        this._smooth = data._smooth ?? false;

        // 🔧 同步内容属性，特别处理 image 元素
        this.elements = data.elements ? this.processElementsFromBitmap(data.elements) : [];
        this.regions = data.regions ? [...data.regions] : [];

        // 如果需要，创建新的 bitmap
        if (data.createBitmap !== false) {
            this.createNewBitmap();
        }

        console.log('🔧 BitmapModel: 从数据对象初始化完成', {
            width: this.width,
            height: this.height,
            fontFace: this.fontFace,
            fontSize: this.fontSize,
            elementsCount: this.elements.length,
            regionsCount: this.regions.length,
            hasBitmap: !!this._originalBitmap
        });
    }

    /**
     * 获取原始 bitmap 实例
     */
    getOriginalBitmap(): any {
        return this._originalBitmap;
    }

    /**
     * 设置更新回调函数
     */
    setUpdateCallback(callback: (() => void) | null): void {
        this._onUpdate = callback;
    }

    /**
     * 从现有 bitmap 对象创建模型
     */
    public static fromBitmap(bitmap: any): BitmapModel {
        const model = new BitmapModel();

        // 同步基本属性
        model.width = bitmap.width || 800;
        model.height = bitmap.height || 600;

        // 同步字体属性
        model.fontBold = bitmap.fontBold ?? false;
        model.fontFace = bitmap.fontFace ?? 'GameFont';
        model.fontItalic = bitmap.fontItalic ?? false;
        model.fontSize = bitmap.fontSize ?? 16;

        // 同步颜色属性
        model.textColor = bitmap.textColor ?? '#ffffff';
        model.outlineColor = bitmap.outlineColor ?? 'rgba(0, 0, 0, 0.5)';
        model.outlineWidth = bitmap.outlineWidth ?? 0;

        // 同步显示属性
        model._paintOpacity = bitmap._paintOpacity ?? 255;
        model._smooth = bitmap._smooth ?? false;

        // 🔧 同步内容属性，特别处理 image 元素
        model.elements = bitmap.elements ? model.processElementsFromBitmap(bitmap.elements) : [];
        model.regions = bitmap.regions ? [...bitmap.regions] : [];

        console.log('🔧 BitmapModel: 从现有 bitmap 创建模型完成', {
            width: model.width,
            height: model.height,
            fontFace: model.fontFace,
            fontSize: model.fontSize,
            elementsCount: model.elements.length,
            regionsCount: model.regions.length
        });

        return model;
    }

    /**
     * 处理从 bitmap 中获取的 elements 数组
     */
    private processElementsFromBitmap(elements: any[]): Element[] {
        return elements.map((element, index) => {
            const processedElement = { ...element };

            // 🔧 确保所有元素都有 id
            if (!processedElement.id) {
                processedElement.id = `${element.type}_${Date.now()}_${index}_${Math.random().toString(36).substring(2, 11)}`;
                console.log('🔧 BitmapModel: 为元素生成 id:', processedElement.id);
            }

            if (element.type === 'image' && element.source) {
                // 对于 image 元素，从 source bitmap 中提取 regions 数据
                if (element.source.regions) {
                    processedElement.regions = [...element.source.regions];
                    console.log('🔧 BitmapModel.processElementsFromBitmap: 从 image.source 提取 regions:', processedElement.regions);
                }
            }

            return processedElement;
        });
    }



    /**
     * 绑定到 bitmap 实例并设置单向同步
     * 注意：如果已经有原始 bitmap，会替换为新的 bitmap
     */
    bindToBitmap(bitmap: any): void {
        this._originalBitmap = bitmap;
        this.setupOneWaySync();
        console.log('🔧 BitmapModel: 绑定到 bitmap 实例', bitmap);
    }

    /**
     * 设置单向同步：模型变化 → bitmap
     */
    private setupOneWaySync(): void {
        $effect(() => {
            if (this._originalBitmap) {
                // 同步基本属性
                if (this._originalBitmap.width !== this.width || this._originalBitmap.height !== this.height) {
                    // 如果尺寸变化，需要重新创建 bitmap（这里只记录，实际重创建由外部处理）
                    console.log('🔧 BitmapModel: 尺寸变化', { width: this.width, height: this.height });
                }

                // 同步字体属性
                this._originalBitmap.fontBold = this.fontBold;
                this._originalBitmap.fontFace = this.fontFace;
                this._originalBitmap.fontItalic = this.fontItalic;
                this._originalBitmap.fontSize = this.fontSize;

                // 同步颜色属性
                this._originalBitmap.textColor = this.textColor;
                this._originalBitmap.outlineColor = this.outlineColor;
                this._originalBitmap.outlineWidth = this.outlineWidth;

                // 同步显示属性
                // 🔧 使用 paintOpacity 属性而不是 _paintOpacity，触发 setter 中的 globalAlpha 设置
                this._originalBitmap.paintOpacity = this._paintOpacity;
                this._originalBitmap.smooth = this._smooth;

                // 🔧 同步内容数组（创建副本，避免响应式引用导致无限循环）
                this._originalBitmap.elements = [...this.elements];
                this._originalBitmap.regions = [...this.regions];

                console.log('🔧 BitmapModel: 同步到 bitmap 的 elements:', this._originalBitmap.elements);
                console.log('🔧 BitmapModel: 第一个元素的 x 坐标:', this._originalBitmap.elements[0]?.x);
                console.log('🔧 BitmapModel: 同步透明度:', this._paintOpacity, '-> bitmap.paintOpacity:', this._originalBitmap.paintOpacity);
                console.log('🔧 BitmapModel: bitmap.context.globalAlpha:', this._originalBitmap.context?.globalAlpha);

                // 触发重绘
                if (typeof this._originalBitmap.redrawing === 'function') {
                    this._originalBitmap.redrawing();
                    console.log('🔧 BitmapModel: 触发 bitmap 重绘');
                }

                // 🔧 调用更新回调
                if (this._onUpdate) {
                    this._onUpdate();
                    console.log('🔧 BitmapModel: 调用更新回调');
                }
            }
        });
    }

    /**
     * 添加文本元素
     */
    addTextElement(text: string, x: number, y: number, options: Partial<TextElement> = {}): void {
        const element: TextElement = {
            type: 'text',
            id: options.id || `text_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            text,
            x,
            y,
            maxWidth: options.maxWidth || 0xffffffff,
            lineHeight: options.lineHeight || this.fontSize + 8,
            align: options.align || 'left'
        };

        this.elements.push(element);
        console.log('🔧 BitmapModel: 添加文本元素', element);
    }

    /**
     * 添加图像元素
     */
    addImageElement(source: any, sx: number, sy: number, sw: number, sh: number,
        dx: number, dy: number, dw: number, dh: number, options: Partial<ImageElement> = {}): void {
        const element: ImageElement = {
            type: 'image',
            id: options.id || `image_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            source,
            sx, sy, sw, sh,
            dx, dy, dw, dh,
            regions: options.regions || []
        };

        this.elements.push(element);
        console.log('🔧 BitmapModel: 添加图像元素', element);
    }

    /**
     * 更新元素（保持对象引用不变）
     */
    updateElement(index: number, updates: Partial<Element>): void {
        if (index >= 0 && index < this.elements.length) {
            // 🔧 直接修改现有对象的属性，保持引用不变
            const element = this.elements[index];

            // 逐个更新属性以确保响应式系统检测到变化
            for (const [key, value] of Object.entries(updates)) {
                (element as any)[key] = value;
            }

            console.log('🔧 BitmapModel: 更新元素', index, updates, '元素引用保持不变');
        }
    }

    /**
     * 删除元素
     */
    removeElement(index: number): void {
        if (index >= 0 && index < this.elements.length) {
            const removed = this.elements.splice(index, 1);
            console.log('🔧 BitmapModel: 删除元素', index, removed[0]);
        }
    }

    /**
     * 添加区域
     */
    addRegion(region: RegionData): void {
        this.regions.push(region);
        console.log('🔧 BitmapModel: 添加区域', region);
    }

    /**
     * 清空所有区域
     */
    clearRegions(): void {
        this.regions.length = 0;
        console.log('🔧 BitmapModel: 清空所有区域');
    }

    /**
     * 同步 regions 数据到 image 元素的 source bitmap
     */
    syncRegionsToImageSources(): void {
        this.elements.forEach(element => {
            if (element.type === 'image' && element.source && element.regions) {
                // 将 regions 数据同步到 source bitmap
                element.source.regions = [...element.regions];
                console.log('🔧 BitmapModel.syncRegionsToImageSources: 同步 regions 到 image.source:', element.regions);
            }
        });
    }

    /**
     * 导出为普通对象
     */
    toPlainObject(): any {
        // 🔧 处理 elements 数组，特别是 image 元素的 source
        const processedElements = this.elements.map(element => {
            if (element.type === 'image') {
                // 对于 image 元素，需要特殊处理 source
                const imageElement = { ...element };

                // 如果 source 是 bitmap 对象，提取其 regions 数据
                if (element.source && typeof element.source === 'object') {
                    // 保留 source 的引用，但确保 regions 数据被正确提取
                    if (element.source.regions) {
                        imageElement.regions = [...element.source.regions];
                        console.log('🔧 BitmapModel.toPlainObject: 从 image.source 提取 regions:', imageElement.regions);
                    }
                }

                return imageElement;
            }

            // 文本元素直接返回
            return { ...element };
        });

        return {
            width: this.width,
            height: this.height,
            fontBold: this.fontBold,
            fontFace: this.fontFace,
            fontItalic: this.fontItalic,
            fontSize: this.fontSize,
            textColor: this.textColor,
            outlineColor: this.outlineColor,
            outlineWidth: this.outlineWidth,
            _paintOpacity: this._paintOpacity,
            _smooth: this._smooth,
            elements: processedElements,  // 🔧 使用处理后的 elements
            regions: [...this.regions]
        };
    }
} // 结束 BitmapModel 类定义
