/**
 * 对象树操作接口
 * 所有状态变更都通过这些纯函数进行，避免直接修改状态
 */

import type { ObjectTreeState, PureObjectTreeNodeData } from './objectTreeStore';
import { getAllChildNodeIds } from './objectTreeStore';
import {
  getObjectTypeName,
  getObjectDisplayName,
  generateNodeId,
  isRPGMakerMZType,
  determineNodeType,
  getObjectChildren
} from './typeUtils';

/**
 * 对象树操作类
 */
export class ObjectTreeActions {

  /**
   * 设置根对象
   */
  static setRootObject(
    state: ObjectTreeState,
    rootObject: any,
    rootObjectType: string
  ): ObjectTreeState {
    console.log('🔍 === 设置根对象 ===');
    console.log('🔍 根对象:', rootObject);
    console.log('🔍 根对象类型:', rootObjectType);
    console.log('🔍 根对象子对象数量:', rootObject.children ? rootObject.children.length : 0);

    // 创建新状态
    const newState: ObjectTreeState = {
      ...state,
      nodes: new Map(),
      nodeParents: new Map(),
      nodeChildren: new Map(),
      expandedNodes: new Set(),
      selectedNodeId: null,
      lastUpdateTime: Date.now()
    };

    // 创建根节点
    const rootNodeId = generateNodeId(rootObjectType, rootObject);
    const typeInfo = determineNodeType(
      rootObject,
      undefined, // 没有父类型
      true // 是主类型节点
    );
    const rootNode: PureObjectTreeNodeData = {
      id: rootNodeId,
      currentObject: rootObject,
      displayName: getObjectDisplayName(rootObject, rootObjectType),
      isRPGMakerType: isRPGMakerMZType(rootObjectType),
      isRoot: true,
      depth: 0,
      objectType: rootObjectType,
      typeInfo: typeInfo
    };

    newState.nodes.set(rootNodeId, rootNode);
    newState.rootNodeId = rootNodeId;
    newState.expandedNodes.add(rootNodeId); // 根节点默认展开

    // 递归构建子节点
    console.log('🔍 开始构建子节点...');
    this.buildChildNodes(newState, rootNodeId, rootObject, 1);

    console.log('🔍 根对象设置完成，节点总数:', newState.nodes.size);
    console.log('🔍 子节点映射:', Array.from(newState.nodeChildren.entries()));
    return newState;
  }

  /**
   * 递归构建子节点
   */
  private static buildChildNodes(
    state: ObjectTreeState,
    parentNodeId: string,
    parentObject: any,
    depth: number
  ): void {
    if (depth > state.maxDepth) {
      console.log(`达到最大深度 ${state.maxDepth}，停止构建子节点`);
      return;
    }

    // 获取父节点的类型信息
    const parentNode = state.nodes.get(parentNodeId);
    const parentType = parentNode?.typeInfo?.classType || parentNode?.objectType;

    // 如果父节点是新类型节点，子节点应该使用父节点作为新的路径根
    const effectiveParentType = parentNode?.typeInfo?.isTypeNode ?
      parentNode.typeInfo.classType : parentType;

    // 获取子对象
    const children = getObjectChildren(parentObject);

    // 添加调试信息
    const parentTypeName = getObjectTypeName(parentObject);
    console.log(`🔍 [ObjectTreeActions] 构建子节点 - 父对象: ${parentTypeName}, 子对象数量: ${children.length}, 深度: ${depth}, 最大深度: ${state.maxDepth}`);

    if (children.length === 0) return;

    const childIds: string[] = [];

    for (let i = 0; i < children.length; i++) {
      const child = children[i];
      if (!child) continue;

      const childTypeName = getObjectTypeName(child);
      const childNodeId = generateNodeId(childTypeName, child);

      // 确定子节点类型信息
      const childTypeInfo = determineNodeType(
        child,
        effectiveParentType,
        false // 不是主类型节点
      );

      // 创建子节点
      const childNode: PureObjectTreeNodeData = {
        id: childNodeId,
        currentObject: child,
        displayName: getObjectDisplayName(child, childTypeName),
        isRPGMakerType: isRPGMakerMZType(childTypeName),
        isRoot: false,
        depth: depth,
        typeInfo: childTypeInfo
      };

      state.nodes.set(childNodeId, childNode);
      state.nodeParents.set(childNodeId, parentNodeId);
      childIds.push(childNodeId);

      // 递归构建更深层的子节点（窗口类型不构建子节点）
      if (!childTypeInfo.isWindowType) {
        this.buildChildNodes(state, childNodeId, child, depth + 1);
      }
    }

    if (childIds.length > 0) {
      state.nodeChildren.set(parentNodeId, childIds);
    }
  }



  /**
   * 切换节点展开状态
   */
  static toggleExpanded(state: ObjectTreeState, nodeId: string): ObjectTreeState {
    if (!state.nodes.has(nodeId)) {
      console.warn('节点不存在:', nodeId);
      return state;
    }

    const newExpandedNodes = new Set(state.expandedNodes);

    if (newExpandedNodes.has(nodeId)) {
      newExpandedNodes.delete(nodeId);
      console.log('节点已收起:', nodeId);
    } else {
      newExpandedNodes.add(nodeId);
      console.log('节点已展开:', nodeId);
    }

    return {
      ...state,
      expandedNodes: newExpandedNodes,
      lastUpdateTime: Date.now()
    };
  }

  /**
   * 选择节点
   */
  static selectNode(state: ObjectTreeState, nodeId: string | null): ObjectTreeState {
    if (nodeId && !state.nodes.has(nodeId)) {
      console.warn('节点不存在:', nodeId);
      return state;
    }

    console.log('选择节点:', nodeId);

    return {
      ...state,
      selectedNodeId: nodeId,
      lastUpdateTime: Date.now()
    };
  }

  /**
   * 展开所有节点
   */
  static expandAll(state: ObjectTreeState): ObjectTreeState {
    const newExpandedNodes = new Set<string>();

    // 展开所有非叶子节点
    for (const [nodeId] of state.nodeChildren) {
      newExpandedNodes.add(nodeId);
    }

    console.log('所有节点已展开');

    return {
      ...state,
      expandedNodes: newExpandedNodes,
      lastUpdateTime: Date.now()
    };
  }

  /**
   * 收起所有节点（保持根节点展开）
   */
  static collapseAll(state: ObjectTreeState): ObjectTreeState {
    const newExpandedNodes = new Set<string>();

    // 只保持根节点展开
    if (state.rootNodeId) {
      newExpandedNodes.add(state.rootNodeId);
    }

    console.log('所有非根节点已收起');

    return {
      ...state,
      expandedNodes: newExpandedNodes,
      lastUpdateTime: Date.now()
    };
  }

  /**
   * 增量添加节点（用于对象创建后的同步）
   */
  static addNode(
    state: ObjectTreeState,
    parentNodeId: string,
    newObject: any
  ): ObjectTreeState {
    if (!state.nodes.has(parentNodeId)) {
      console.warn('父节点不存在:', parentNodeId);
      return state;
    }

    const parentNode = state.nodes.get(parentNodeId)!;
    const newTypeName = getObjectTypeName(newObject);
    const newNodeId = generateNodeId(newTypeName, newObject);

    // 检查节点是否已存在
    if (state.nodes.has(newNodeId)) {
      console.log('节点已存在，跳过添加:', newNodeId);
      return state;
    }

    console.log('添加新节点:', newNodeId, '到父节点:', parentNodeId);

    // 获取父节点类型信息
    const parentType = parentNode.typeInfo?.classType || parentNode.objectType;

    // 如果父节点是新类型节点，子节点应该使用父节点作为新的路径根
    const effectiveParentType = parentNode.typeInfo?.isTypeNode ?
      parentNode.typeInfo.classType : parentType;

    // 创建新状态
    const newState: ObjectTreeState = {
      ...state,
      nodes: new Map(state.nodes),
      nodeParents: new Map(state.nodeParents),
      nodeChildren: new Map(state.nodeChildren),
      lastUpdateTime: Date.now()
    };

    // 确定新节点类型信息
    const newTypeInfo = determineNodeType(
      newObject,
      effectiveParentType,
      false // 不是主类型节点
    );

    // 创建新节点
    const newNode: PureObjectTreeNodeData = {
      id: newNodeId,
      currentObject: newObject,
      displayName: getObjectDisplayName(newObject, newTypeName),
      isRPGMakerType: isRPGMakerMZType(newTypeName),
      isRoot: false,
      depth: parentNode.depth + 1,
      typeInfo: newTypeInfo
    };

    newState.nodes.set(newNodeId, newNode);
    newState.nodeParents.set(newNodeId, parentNodeId);

    // 更新父节点的子节点列表
    const existingChildren = state.nodeChildren.get(parentNodeId) || [];
    newState.nodeChildren.set(parentNodeId, [...existingChildren, newNodeId]);

    // 递归构建新节点的子节点（窗口类型不构建子节点）
    if (!newTypeInfo.isWindowType) {
      this.buildChildNodes(newState, newNodeId, newObject, newNode.depth + 1);
    }

    return newState;
  }

  /**
   * 删除节点及其所有子节点
   */
  static removeNode(state: ObjectTreeState, nodeId: string): ObjectTreeState {
    if (!state.nodes.has(nodeId)) {
      console.warn('节点不存在:', nodeId);
      return state;
    }

    const node = state.nodes.get(nodeId)!;
    if (node.isRoot) {
      console.warn('不能删除根节点');
      return state;
    }

    console.log('删除节点:', nodeId);

    // 获取所有要删除的节点ID（包括子节点）
    const nodeIdsToDelete = [nodeId, ...getAllChildNodeIds(state, nodeId)];

    // 创建新状态
    const newState: ObjectTreeState = {
      ...state,
      nodes: new Map(state.nodes),
      nodeParents: new Map(state.nodeParents),
      nodeChildren: new Map(state.nodeChildren),
      expandedNodes: new Set(state.expandedNodes),
      selectedNodeId: state.selectedNodeId === nodeId ? null : state.selectedNodeId,
      lastUpdateTime: Date.now()
    };

    // 删除所有相关节点
    for (const idToDelete of nodeIdsToDelete) {
      newState.nodes.delete(idToDelete);
      newState.nodeParents.delete(idToDelete);
      newState.nodeChildren.delete(idToDelete);
      newState.expandedNodes.delete(idToDelete);
    }

    // 从父节点的子节点列表中移除
    const parentId = state.nodeParents.get(nodeId);
    if (parentId) {
      const siblings = newState.nodeChildren.get(parentId) || [];
      const newSiblings = siblings.filter(id => id !== nodeId);
      if (newSiblings.length > 0) {
        newState.nodeChildren.set(parentId, newSiblings);
      } else {
        newState.nodeChildren.delete(parentId);
      }
    }

    return newState;
  }

  /**
   * 清空整个对象树
   */
  static clear(state: ObjectTreeState): ObjectTreeState {
    console.log('清空对象树');

    return {
      ...state,
      nodes: new Map(),
      rootNodeId: null,
      nodeParents: new Map(),
      nodeChildren: new Map(),
      expandedNodes: new Set(),
      selectedNodeId: null,
      lastUpdateTime: Date.now()
    };
  }
}
