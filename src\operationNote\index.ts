/**
 * 操作记录保存系统
 * 
 * 这个模块提供了一个简洁的操作记录保存系统，用于记录编辑器中的所有操作
 * 并生成对应的 RPG Maker MZ 插件代码。
 * 
 * 主要特性：
 * - 树形结构完全镜像显示对象列表
 * - 智能删除抵消机制（编辑器创建的对象删除时直接抵消）
 * - 删除操作优先执行
 * - 自动生成插件代码
 * - 支持数据导出/导入
 */

import { OperationRecordSystem } from './OperationRecordSystem';

export { OperationNode, type DeletionOperation } from './OperationNode';
export { OperationRecordSystem } from './OperationRecordSystem';
export { type IPropertyModification, ObjectType } from './interfaces/IPropertyModification';
export { BasePropertyModification, baseProperties } from './implementations/BasePropertyModification';

// 系统窗口模板生成器
export {
  generateSystemWindowCode
} from './tempCode/systemWindowTemplate';

// WindowLayer UI管理器
export {
  extractWindowLayerObjects,
  generateWindowLayerCode,
  processWindowLayer,
  handleWindowLayer,
  type WindowLayerObjectInfo,
  type WindowLayerResult
} from './tempCode/windowLayer';

// Scene对象模板代码生成器
export {
  generateSceneCode,
  generateCompletePluginCode,
  filterMeaningfulObjects,
  createSceneTemplateWithWindowLayer,
  type SceneTemplateData
} from './tempCode/sceneTemplate';

// SpritePro - 新的分层 Sprite 处理器
export {
  processSpriteData,
  processMultipleSpriteData,
  type SpriteData
} from './tempCode/spritePro';

// 创建全局实例
export const operationRecordSystem = new OperationRecordSystem();

/**
 * 使用示例：
 * 
 * ```typescript
 * import { operationRecordSystem, BasePropertyModification } from './operationNote';
 *
 * // 1. 记录对象创建（自动处理初始属性）
 * operationRecordSystem.recordObjectCreation([0, 1], 'Sprite', {
 *   x: 100,
 *   y: 200,
 *   visible: true
 * });
 *
 * // 2. 记录属性修改（使用接口系统）
 * operationRecordSystem.recordPropertyModification([0, 1], 'alpha', 0.5);
 * operationRecordSystem.recordPropertyModification([0, 1], 'visible', false);
 *
 * // 3. 记录对象删除（智能抵消）
 * const engineObject = scene._spriteset._characterSprites[0];
 * const isCancel = operationRecordSystem.recordObjectDeletion([0, 2], engineObject);
 * console.log(isCancel ? '编辑器对象已抵消' : '引擎对象删除已记录');
 *
 * // 4. 生成插件代码（包含模板代码）
 * const pluginCode = operationRecordSystem.generatePlugin('MyEditorPlugin');
 *
 * // 5. 获取统计信息
 * const stats = operationRecordSystem.getStatistics();
 * console.log('统计:', stats);
 *
 * // 6. 导出/导入数据（支持接口序列化）
 * const saveData = operationRecordSystem.exportData();
 * operationRecordSystem.importData(saveData);
 * ```
 */
