/**
 * 对象创建逻辑
 */

import type { ObjectTreeNodeData, ObjectCreationParams } from './types';
import { CreatableObjectType } from './types';
import { globalObjectState } from '../../stores/objectState';
import { get } from 'svelte/store';
import { objectTreeState } from './objectTreeStore';
import { ObjectTreeSync } from './objectTreeSync';

/**
 * 检测对象是否在 WindowLayer 容器中（包括嵌套检测）
 * @param targetNode 目标节点
 * @returns 是否在 WindowLayer 容器中
 */
function isInWindowLayerContainer(targetNode: ObjectTreeNodeData): boolean {
  let currentNodeId: string | null = targetNode.id;

  while (currentNodeId) {
    const state = get(objectTreeState);
    const currentNode = state.nodes.get(currentNodeId);

    if (!currentNode) break;

    // 检查当前节点是否是 WindowLayer
    if (currentNode.currentObject?.constructor?.name === 'WindowLayer') {
      console.log('检测到对象在 WindowLayer 容器中:', targetNode.displayName);
      return true;
    }

    // 向上查找父节点
    currentNodeId = state.nodeParents.get(currentNodeId) || null;
  }

  console.log('检测到对象不在 WindowLayer 容器中:', targetNode.displayName);
  return false;
}

/**
 * 为对象添加地图滚动监听功能
 * @param object 要添加监听的对象
 * @param mapX 地图绝对X坐标
 * @param mapY 地图绝对Y坐标
 */
function addMapScrollListener(object: any, mapX: number = 0, mapY: number = 0): void {
  if (!object) return;

  console.log('为对象添加地图滚动监听:', object.name || object.constructor?.name, { mapX, mapY });

  // 保存地图坐标
  object.mapX = mapX;
  object.mapY = mapY;

  // 保存上一帧的屏幕坐标，用于检测外部修改
  object._lastScreenX = object.x;
  object._lastScreenY = object.y;

  // 添加标志来控制是否启用地图滚动跟踪
  object._mapScrollEnabled = true;

  // 保存原始的 update 方法
  const originalUpdate = object.update;

  // 重写 update 方法
  object.update = function () {
    // 调用原始 update 方法
    if (originalUpdate && typeof originalUpdate === 'function') {
      originalUpdate.call(this);
    }

    const gameMap = (window as any).$gameMap;
    if (!gameMap || this.mapX === undefined || this.mapY === undefined || !this._mapScrollEnabled) {
      return;
    }

    // 检测坐标是否被外部修改（比如编辑器）
    const currentScrollX = gameMap.displayX() * gameMap.tileWidth();
    const currentScrollY = gameMap.displayY() * gameMap.tileHeight();
    const expectedScreenX = this.mapX - currentScrollX;
    const expectedScreenY = this.mapY - currentScrollY;

    // 检测坐标变化：比较当前坐标与上一帧坐标
    const xChanged = Math.abs(this.x - this._lastScreenX) > 0.1;
    const yChanged = Math.abs(this.y - this._lastScreenY) > 0.1;

    // 检测是否是地图滚动引起的变化
    const expectedXChange = Math.abs(expectedScreenX - this._lastScreenX) > 0.1;
    const expectedYChange = Math.abs(expectedScreenY - this._lastScreenY) > 0.1;

    // 如果坐标发生了变化，但不是由地图滚动引起的，说明被外部修改了
    if ((xChanged && !expectedXChange) || (yChanged && !expectedYChange)) {
      // 更新地图坐标以反映新的屏幕位置
      this.mapX = this.x + currentScrollX;
      this.mapY = this.y + currentScrollY;
      console.log('检测到坐标被外部修改，更新地图坐标:', {
        oldScreenX: this._lastScreenX,
        oldScreenY: this._lastScreenY,
        newScreenX: this.x,
        newScreenY: this.y,
        newMapX: this.mapX,
        newMapY: this.mapY
      });
    } else {
      // 正常情况下，根据地图滚动更新屏幕位置
      this.x = expectedScreenX;
      this.y = expectedScreenY;
    }

    // 更新上一帧坐标
    this._lastScreenX = this.x;
    this._lastScreenY = this.y;
  };

  // 初始位置设置
  const gameMap = (window as any).$gameMap;
  if (gameMap) {
    const scrollX = gameMap.displayX() * gameMap.tileWidth();
    const scrollY = gameMap.displayY() * gameMap.tileHeight();
    object.x = mapX - scrollX;
    object.y = mapY - scrollY;
  }

  // 添加辅助方法：手动设置地图坐标
  object.setMapPosition = function (newMapX: number, newMapY: number) {
    this.mapX = newMapX;
    this.mapY = newMapY;

    // 立即更新屏幕位置
    const gameMap = (window as any).$gameMap;
    if (gameMap) {
      const scrollX = gameMap.displayX() * gameMap.tileWidth();
      const scrollY = gameMap.displayY() * gameMap.tileHeight();
      this.x = this.mapX - scrollX;
      this.y = this.mapY - scrollY;

      // 更新上一帧坐标
      this._lastScreenX = this.x;
      this._lastScreenY = this.y;
    }

    console.log('手动设置地图坐标:', { mapX: newMapX, mapY: newMapY, screenX: this.x, screenY: this.y });
  };

  // 添加辅助方法：设置屏幕坐标（会自动转换为地图坐标）
  object.setScreenPosition = function (newScreenX: number, newScreenY: number) {
    const gameMap = (window as any).$gameMap;
    if (gameMap) {
      const scrollX = gameMap.displayX() * gameMap.tileWidth();
      const scrollY = gameMap.displayY() * gameMap.tileHeight();
      this.mapX = newScreenX + scrollX;
      this.mapY = newScreenY + scrollY;
    }

    this.x = newScreenX;
    this.y = newScreenY;
    this._lastScreenX = this.x;
    this._lastScreenY = this.y;

    console.log('设置屏幕坐标:', { screenX: newScreenX, screenY: newScreenY, mapX: this.mapX, mapY: this.mapY });
  };

  // 添加辅助方法：获取当前地图坐标
  object.getMapPosition = function () {
    return { x: this.mapX, y: this.mapY };
  };

  console.log('地图滚动监听已添加，初始位置:', { x: object.x, y: object.y });
}

/**
 * 创建游戏对象的核心函数
 * 简化版本 - 直接创建基本对象
 */
function createGameObject(type: string, params: ObjectCreationParams = {}, parentContainer: any = null, targetIndex: number | null = null): any {
  console.log(`[GameObject] Creating new object: ${type}`, params);

  let newObject: any = null;

  switch (type) {
    case "Sprite":
      newObject = new (window as any).Sprite();
      newObject.name = params.name || "NewSprite";
      newObject.x = params.x || 0;
      newObject.y = params.y || 0;
      newObject.visible = params.visible !== undefined ? params.visible : true;
      break;

    case "Label":
      newObject = new (window as any).Sprite();
      newObject.name = params.name || "NewLabel";
      newObject.x = params.x || 0;
      newObject.y = params.y || 0;
      newObject.visible = params.visible !== undefined ? params.visible : true;

      const bitmap = new (window as any).Bitmap(200, 40);
      bitmap.fontSize = 20;
      bitmap.textColor = "#ffffff";
      bitmap.outlineColor = "rgba(0, 0, 0, 0.5)";
      bitmap.outlineWidth = 4;
      bitmap.drawText(params.text || "New Text", 0, 0, 200, 40, "left");
      bitmap.text = params.text || "New Text";

      newObject.bitmap = bitmap;
      break;

    case "Container":
      newObject = new (window as any).PIXI.Container();
      newObject.name = params.name || "NewContainer";
      newObject.x = params.x || 0;
      newObject.y = params.y || 0;
      newObject.visible = params.visible !== undefined ? params.visible : true;
      break;

    case "Window":
      const rect = new (window as any).Rectangle(0, 0, 200, 100);
      newObject = new (window as any).Window_Base(rect);
      newObject.name = params.name || "NewWindow";
      newObject.x = params.x || 0;
      newObject.y = params.y || 0;
      newObject.visible = params.visible !== undefined ? params.visible : true;
      break;

    case "Button":
      // 使用系统默认的 Sprite_Button，让它自动加载 ButtonSet.png
      newObject = new (window as any).Sprite_Button("ok");
      newObject.name = params.name || "NewButton";
      // 设置在可见位置，相对于父对象偏移
      newObject.x = params.x || 50;
      newObject.y = params.y || 50;
      newObject.visible = params.visible !== undefined ? params.visible : true;

      // 不设置自定义 bitmap，让 Sprite_Button 使用默认的 ButtonSet.png
      // newObject.bitmap 会在 setupFrames() 中自动设置为 ImageManager.loadSystem("ButtonSet")

      console.log('[Button Debug] 使用系统默认按钮，类型: ok，位置:', { x: newObject.x, y: newObject.y });
      break;

    default:
      console.log(`[GameObject] Unknown object type: ${type}`);
      return null;
  }

  // 如果提供了父容器，则添加到父容器中
  if (newObject && parentContainer && parentContainer.addChild) {
    if (targetIndex !== null && targetIndex !== undefined) {
      // 在指定索引位置插入对象
      if (parentContainer.children && targetIndex <= parentContainer.children.length) {
        parentContainer.addChildAt(newObject, targetIndex);
        console.log('[Create Object] 成功在索引 ' + targetIndex + ' 位置创建并添加 ' + type + ' 对象');
      } else {
        // 如果索引超出范围，直接添加到末尾
        parentContainer.addChild(newObject);
        console.log('[Create Object] 索引超出范围，添加 ' + type + ' 对象到末尾');
      }
    } else {
      // 没有指定索引，直接添加到末尾
      parentContainer.addChild(newObject);
      console.log('[Create Object] 成功创建并添加 ' + type + ' 对象到容器');
    }
  } else if (parentContainer && !parentContainer.addChild) {
    console.log('[Create Object] 父容器不支持 addChild 方法');
  }

  return newObject;
}

/**
 * 处理对象创建请求
 * @param objectType 要创建的对象类型
 * @param targetNode 目标节点（新对象将作为其子对象）
 */
export function handleObjectCreation(objectType: string, targetNode: ObjectTreeNodeData): void {
  console.log('开始创建对象:', {
    objectType,
    targetNode: targetNode.displayName,
    targetObject: targetNode.currentObject
  });

  try {
    // 提取实际的父容器对象
    let parentContainer = targetNode.currentObject;

    // 如果是包装对象，提取 displayObject
    if (parentContainer && typeof parentContainer === 'object' && parentContainer.displayObject) {
      console.log('检测到包装对象，提取 displayObject 作为父容器');
      parentContainer = parentContainer.displayObject;
    }

    console.log('使用的父容器:', {
      hasAddChild: !!(parentContainer && parentContainer.addChild),
      containerType: parentContainer?.constructor?.name,
      container: parentContainer
    });

    // 创建对象参数
    const params: ObjectCreationParams = {
      name: `${objectType}_${Date.now()}`,
      x: 0,
      y: 0,
      visible: true
    };

    // 如果是 Label 或 Button，添加默认文本
    if (objectType === CreatableObjectType.LABEL) {
      params.text = "New Text";
    } else if (objectType === CreatableObjectType.BUTTON) {
      params.text = "Button";
    }

    // 创建新对象
    const newObject = createGameObject(objectType, params, parentContainer);

    if (!newObject) {
      console.error('创建对象失败');
      return;
    }

    console.log('对象创建成功:', newObject);

    // 检查是否需要添加地图滚动监听
    const isInWindowLayer = isInWindowLayerContainer(targetNode);

    if (!isInWindowLayer) {
      // 不在 WindowLayer 容器中，添加地图滚动监听
      console.log('对象不在 WindowLayer 中，添加地图滚动监听');
      addMapScrollListener(newObject, params.x || 0, params.y || 0);
    } else {
      console.log('对象在 WindowLayer 中，保持固定在屏幕位置');
    }



    // 直接使用 ObjectTreeSync 来处理对象创建后的同步
    objectTreeState.update(state => {
      console.log('处理对象创建后的同步更新');
      return ObjectTreeSync.handleObjectCreated(state, parentContainer, newObject);
    });

    console.log('对象创建完成，对象树已同步更新');

  } catch (error) {
    console.error('创建对象时发生错误:', error);
  }
}