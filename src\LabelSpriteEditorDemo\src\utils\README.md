# Sprite数据转换工具

这个工具提供了在elements数组数据和RPG Maker sprite对象之间进行转换的功能。

## 主要功能

### 1. elementsToSprite - 数据转换为Sprite

将elements数组数据转换为RPG Maker sprite对象。使用 `ImageManager.loadBitmapFromUrl` 加载图片，所有信息保存在bitmap上。

```javascript
import { elementsToSprite } from './spriteDataConverter';

const elementsData = [
  {
    type: 'text',
    text: '山东广东富豪',
    x: 211,
    y: 259,
    maxWidth: 200,
    lineHeight: 24,
    align: 'left'
  },
  {
    type: 'image',
    source: {
      _url: '../projects/Project4/img/faces/Actor1.png',
      width: 144,
      height: 223
    },
    sx: 108,
    sy: 111.5,
    sw: 36,
    sh: 55.75,
    dx: 268,
    dy: 383,
    dw: 36,
    dh: 55.75,
    bounds: { x: 390, y: 284, width: 36, height: 55.75 },
    gridIndex: 11
  }
];

// 转换为sprite（返回完整的sprite对象）
const sprite = await elementsToSprite(elementsData, {
  canvasWidth: 816,
  canvasHeight: 624,
  padding: 200
});

console.log(sprite); // RPG Maker Sprite对象
console.log(sprite.bitmap.elements); // 完整的elements数组保存在bitmap上
console.log(sprite.bitmap.width, sprite.bitmap.height); // bitmap尺寸
```

### 2. spriteToElements - Sprite转换为数据

将RPG Maker sprite对象转换为elements数组数据。

```javascript
import { spriteToElements } from './spriteDataConverter';

// 假设你有一个sprite对象
const sprite = window.SpriteEditor.currentSprite;

const result = spriteToElements(sprite, {
  includeCanvasSize: true,
  preserveSourceObjects: false
});

console.log(result.elements); // elements数组
console.log(result.elementCount); // 元素总数
console.log(result.textCount); // 文本元素数量
console.log(result.imageCount); // 图片元素数量
```

### 3. validateElementsData - 数据验证

验证elements数组数据的有效性。

```javascript
import { validateElementsData } from './spriteDataConverter';

const validation = validateElementsData(elementsData);

if (validation.isValid) {
  console.log('数据验证通过');
  console.log('统计信息:', validation.statistics);
} else {
  console.log('验证失败:', validation.errors);
}

if (validation.warnings.length > 0) {
  console.log('警告:', validation.warnings);
}
```

### 4. 创建元素的辅助函数

```javascript
import { createTextElement, createImageElement } from './spriteDataConverter';

// 创建文本元素
const textElement = createTextElement({
  text: '新文本',
  x: 100,
  y: 100,
  maxWidth: 200,
  lineHeight: 36,
  align: 'left'
});

// 创建图片元素
const imageElement = createImageElement({
  source: { _url: 'image.png', width: 64, height: 64 },
  sx: 0, sy: 0, sw: 32, sh: 32,
  dx: 100, dy: 100, dw: 32, dh: 32
});
```

## 数据结构

### 文本元素结构

```javascript
{
  type: 'text',
  text: '文本内容',
  x: 100,              // X坐标
  y: 100,              // Y坐标
  maxWidth: 200,       // 最大宽度
  lineHeight: 36,      // 行高
  align: 'left'        // 对齐方式: 'left', 'center', 'right'
}
```

### 图片元素结构

```javascript
{
  type: 'image',
  source: {
    _url: 'image.png',  // 图片URL
    width: 144,         // 原始图片宽度
    height: 223         // 原始图片高度
  },
  sx: 108,             // 源图片X坐标
  sy: 111.5,           // 源图片Y坐标
  sw: 36,              // 源图片宽度
  sh: 55.75,           // 源图片高度
  dx: 268,             // 目标X坐标
  dy: 383,             // 目标Y坐标
  dw: 36,              // 目标宽度
  dh: 55.75,           // 目标高度
  bounds: {            // 边界信息
    x: 390,
    y: 284,
    width: 36,
    height: 55.75
  },
  gridIndex: 11        // 可选：网格索引
}
```

## 全局API

在Sprite Editor中，这些转换工具被集成到全局API中：

### setExternalSpriteForEdit

```javascript
// 设置外部sprite数据进行编辑
window.SpriteEditor.setExternalSpriteForEdit(elementsArray);
```

### saveCurrentSprite

```javascript
// 保存当前sprite为elements数组
const elementsData = window.SpriteEditor.saveCurrentSprite();
```

### 监听保存事件

```javascript
// 监听保存事件
document.addEventListener('spriteEditorSave', function(event) {
  console.log('保存的数据:', event.detail);
  console.log('元素数组:', event.detail.elements);
  console.log('画布尺寸:', event.detail.canvasSize);
});
```

## 使用示例

### 完整的往返转换示例

```javascript
// 1. 创建原始数据
const originalElements = [
  {
    type: 'text',
    text: '测试文本',
    x: 100,
    y: 100,
    maxWidth: 200,
    lineHeight: 36,
    align: 'left'
  }
];

// 2. 转换为sprite
const spriteResult = await elementsToSprite(originalElements);

// 3. 转换回elements
const elementsResult = spriteToElements(spriteResult.sprite);

// 4. 比较结果
console.log('原始:', originalElements);
console.log('转换后:', elementsResult.elements);
```

### 在浏览器中测试

```javascript
// 在浏览器控制台中运行
window.SpriteEditor.setExternalSpriteForEdit([
  {
    type: 'text',
    text: '测试文本',
    x: 100,
    y: 100,
    maxWidth: 200,
    lineHeight: 36,
    align: 'left'
  }
]);

// 等待一段时间后保存
setTimeout(() => {
  const result = window.SpriteEditor.saveCurrentSprite();
  console.log('保存结果:', result);
}, 2000);
```

## 错误处理

所有转换函数都包含错误处理：

```javascript
try {
  const result = await elementsToSprite(elementsData);
  console.log('转换成功:', result);
} catch (error) {
  console.error('转换失败:', error.message);
}
```

## 注意事项

1. **图片加载**：`elementsToSprite`函数会异步加载所有图片，确保在使用前等待Promise完成。

2. **RPG Maker API**：转换函数需要RPG Maker的`Bitmap`和`Sprite`类可用。

3. **内存管理**：大量图片转换时注意内存使用，建议分批处理。

4. **数据验证**：使用`validateElementsData`函数验证数据格式，避免转换错误。

5. **URL格式**：图片URL支持多种格式，包括blob URL、data URL和普通HTTP URL。
