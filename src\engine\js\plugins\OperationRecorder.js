//=============================================================================
// OperationRecorder.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc [v2.0.0] Operation Recorder
 * <AUTHOR> Team
 * @url 
 * @help OperationRecorder.js
 * 
 * ============================================================================
 * Operation Recorder Plugin - Object Modification Tracking
 * ============================================================================
 *
 * This plugin adds operation recording Map to each object to track modifications.
 * Each object gets a _operationRecord Map to store its operation history.
 *
 * Features:
 * - Adds _operationRecord Map to Sprite, Container, and Window_Base objects
 * - Records create, modify, delete operations
 * - Uses index-based positioning for object identification
 * 
 * ============================================================================
 */

(() => {
    'use strict';

    const DEBUG = false;
    const ENABLE_RECORDING = false;

    // 全局 ID 计数器（统一的 ID 系统）
    let globalIdCounter = 0;

    // Debug logging
    function log(...args) {
        if (DEBUG) {
            console.log('[OperationRecorder]', ...args);
        }
    }

    // ============================================================================
    // 统一 ID 系统
    // ============================================================================

    // 为对象分配统一的 ID
    function ensureObjectId(obj) {
        if (!obj._objectId) {
            obj._objectId = globalIdCounter++;
            log(`Assigned ID ${obj._objectId} to ${obj.constructor.name}`);
        }
        return obj._objectId;
    }

    // ============================================================================
    // 操作记录 Map 系统
    // ============================================================================

    // 为对象添加操作记录 Map
    function ensureOperationRecord(obj) {
        if (!obj._operationRecord && ENABLE_RECORDING) {
            // 确保对象有 ID
            ensureObjectId(obj);

            // 创建操作记录 Map，包含对象 ID
            obj._operationRecord = new Map();
            obj._operationRecord.set('_objectId', obj._objectId);  // 保存对象 ID

            log(`Added operation record Map to ${obj.constructor.name} (ID: ${obj._objectId})`);
        }
        return obj._operationRecord;
    }

    // ============================================================================
    // 重写构造函数以添加操作记录 Map
    // ============================================================================

    // 重写 Sprite 构造函数
    const _Sprite_initialize = Sprite.prototype.initialize;
    Sprite.prototype.initialize = function (bitmap) {
        // 调用原始初始化
        _Sprite_initialize.call(this, bitmap);

        // 添加操作记录 Map（使用统一ID系统）
        if (ENABLE_RECORDING) {
            ensureOperationRecord(this);
        }
    };

    // 添加获取对象ID的方法
    Sprite.prototype.getObjectId = function () {
        return this._objectId;
    };

    // 添加检查是否有操作记录的方法
    Sprite.prototype.hasOperationRecord = function () {
        return !!(this._operationRecord && this._operationRecord instanceof Map);
    };

    log('Sprite prototype extended with unified ID system and operation record Map');

    // 重写 PIXI.Container 构造函数（如果存在）
    if (typeof PIXI !== 'undefined' && PIXI.Container) {
        const _PIXI_Container = PIXI.Container;

        function PIXIContainerWithRecord() {
            // 调用原始构造函数
            _PIXI_Container.apply(this, arguments);

            // 添加操作记录 Map
            if (ENABLE_RECORDING) {
                ensureOperationRecord(this);
            }
        }

        // 继承原型
        PIXIContainerWithRecord.prototype = Object.create(_PIXI_Container.prototype);
        PIXIContainerWithRecord.prototype.constructor = PIXIContainerWithRecord;

        // 添加获取对象ID的方法
        PIXIContainerWithRecord.prototype.getObjectId = function () {
            return this._objectId;
        };

        // 添加检查是否有操作记录的方法
        PIXIContainerWithRecord.prototype.hasOperationRecord = function () {
            return !!(this._operationRecord && this._operationRecord instanceof Map);
        };

        // 替换全局的 PIXI.Container
        PIXI.Container = PIXIContainerWithRecord;

        log('PIXI.Container prototype extended with unified ID system and operation record Map');
    } else {
        log('PIXI.Container not found - skipping Container setup');
    }

    // 重写 Window_Base 构造函数（如果存在）
    if (typeof Window_Base !== 'undefined') {
        const _Window_Base_initialize = Window_Base.prototype.initialize;
        Window_Base.prototype.initialize = function (rect) {
            // 调用原始初始化
            _Window_Base_initialize.call(this, rect);

            // 添加操作记录 Map（使用统一ID系统）
            if (ENABLE_RECORDING) {
                ensureOperationRecord(this);
            }
        };

        // 添加获取对象ID的方法
        Window_Base.prototype.getObjectId = function () {
            return this._objectId;
        };

        // 添加检查是否有操作记录的方法
        Window_Base.prototype.hasOperationRecord = function () {
            return !!(this._operationRecord && this._operationRecord instanceof Map);
        };

        log('Window_Base prototype extended with unified ID system and operation record Map');
    } else {
        log('Window_Base class not found - may load later');
    }

    // ============================================================================
    // 工具函数
    // ============================================================================

    window.OperationRecorderUtils = {
        // 获取对象的操作记录 Map
        getOperationRecord: function (obj) {
            return obj._operationRecord;
        },

        // 获取对象的 ID
        getObjectId: function (obj) {
            return obj._objectId;
        },

        // 检查对象是否有操作记录
        hasOperationRecord: function (obj) {
            return !!(obj._operationRecord && obj._operationRecord instanceof Map);
        },

        // 检查对象是否有 ID
        hasObjectId: function (obj) {
            return obj._objectId !== undefined;
        },

        // 获取全局 ID 计数器当前值
        getCurrentIdCounter: function () {
            return globalIdCounter;
        },

        // 重置全局 ID 计数器（谨慎使用）
        resetIdCounter: function () {
            const oldCounter = globalIdCounter;
            globalIdCounter = 0;
            console.warn(`[OperationRecorder] ID 计数器已重置: ${oldCounter} -> 0`);
        },

        // 调试：打印统计信息
        debugPrintStats: function () {
            console.log('=== Operation Record Stats ===');
            console.log('Operation recording system loaded with unified ID system');
            console.log(`Current global ID counter: ${globalIdCounter}`);
            console.log('Objects will have _objectId and _operationRecord Map added automatically');
        }
    };

    // 插件加载完成
    log('Operation Recorder plugin loaded successfully');
    log('Objects will now have _operationRecord Map for tracking modifications');

})();
