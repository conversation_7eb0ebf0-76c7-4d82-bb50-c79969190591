/**
 * SpritePro 测试文件
 * 用于验证新的分层 Sprite 处理器
 */

import { processSpriteData, processMultipleSpriteData, type SpriteData } from './index';

// 测试数据1：基础 Sprite
const basicSprite: SpriteData = {
  className: 'Sprite',
  referenceName: '_backSprite1',
  properties: {
    x: 408,
    y: 312,
    width: 816,
    height: 624,
    anchorX: 0.5,
    anchorY: 0.5,
    url: 'img/titles1/Ruins.png'
  }
};

// 测试数据2：包含 elements 数组的 Sprite
const elementsSprite: SpriteData = {
  className: 'Sprite',
  referenceName: '_gameTitleSprite',
  properties: {
    width: 816,
    height: 624,
    fontBold: false,
    fontFace: "rmmz-mainfont, Microsoft Yahei, PingFang SC, sans-serif",
    fontItalic: false,
    fontSize: 72,
    outlineColor: "black",
    outlineWidth: 8,
    textColor: "#ffffff",
    _paintOpacity: 255,
    _smooth: true,
    elements: [
      {
        "type": "text",
        "text": "Project6",
        "x": 20,
        "y": 156,
        "maxWidth": 776,
        "lineHeight": 48,
        "align": "center",
        "bounds": {
          "x": 20,
          "y": 156,
          "width": 776,
          "height": 48
        }
      }
    ]
  }
};

// 测试数据3：WindowLayer
const windowLayer: SpriteData = {
  className: 'WindowLayer',
  referenceName: '_windowLayer',
  properties: {
    x: 4,
    y: 4
  }
};

// 测试数据4：Window 对象
const titleCommand: SpriteData = {
  className: 'Window_TitleCommand',
  referenceName: '_commandWindow',
  properties: {
    x: 100,
    y: 200,
    width: 240,
    height: 120
  }
};

// 测试数据5：复杂的嵌套结构
const complexSprite: SpriteData = {
  className: 'Sprite',
  referenceName: '_container',
  properties: {
    x: 50,
    y: 50,
    scaleX: 1.2,
    scaleY: 1.2,
    alpha: 0.9
  },
  children: [
    {
      className: 'Sprite',
      properties: {
        x: 10,
        y: 10,
        url: 'img/pictures/Actor1.png'
      }
    },
    {
      className: 'Sprite',
      properties: {
        x: 20,
        y: 20,
        elements: [
          {
            type: 'text',
            text: 'Child Text',
            x: 0,
            y: 0
          }
        ]
      }
    }
  ]
};

/**
 * 运行测试
 */
export function runSpritePro Tests() {
  console.log('=== SpritePro 测试开始 ===\n');
  
  // 测试1：基础 Sprite
  console.log('测试1：基础 Sprite');
  const code1 = processSpriteData(basicSprite);
  console.log(code1);
  console.log('---\n');
  
  // 测试2：Elements Sprite
  console.log('测试2：Elements Sprite');
  const code2 = processSpriteData(elementsSprite);
  console.log(code2);
  console.log('---\n');
  
  // 测试3：WindowLayer
  console.log('测试3：WindowLayer');
  const code3 = processSpriteData(windowLayer);
  console.log(code3);
  console.log('---\n');
  
  // 测试4：Window 对象
  console.log('测试4：Window 对象');
  const code4 = processSpriteData(titleCommand);
  console.log(code4);
  console.log('---\n');
  
  // 测试5：复杂嵌套结构
  console.log('测试5：复杂嵌套结构');
  const code5 = processSpriteData(complexSprite);
  console.log(code5);
  console.log('---\n');
  
  // 测试6：批量处理
  console.log('测试6：批量处理');
  const sprites = [basicSprite, elementsSprite, windowLayer];
  const batchCode = processMultipleSpriteData(sprites);
  console.log(batchCode);
  console.log('---\n');
  
  console.log('=== SpritePro 测试完成 ===');
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  // Node.js 环境
  runSpriteProTests();
}
