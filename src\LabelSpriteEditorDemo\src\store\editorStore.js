import { create } from 'zustand';

// 获取当前sprite的辅助函数
const getCurrentSprite = () => {
  if (window.SpriteEditor && window.SpriteEditor.currentSprite) {
    return window.SpriteEditor.currentSprite;
  }
  return null;
};

// 重绘函数
const redraw = () => {
  const sprite = getCurrentSprite();
  if (!sprite) {
    console.error('无法获取sprite');
    return false;
  }

  if (!sprite.bitmap) {
    console.error('sprite.bitmap不存在');
    return false;
  }

  if (!sprite.bitmap.elements) {
    console.error('sprite.bitmap.elements不存在');
    sprite.bitmap.elements = [];
  }

  if (!sprite.bitmap.redrawing) {
    console.error('sprite.bitmap.redrawing方法不存在');
    return false;
  }

  try {
    console.log("重绘", sprite.bitmap.elements);
    sprite.bitmap.redrawing();

    // 确保纹理更新
    if (sprite.bitmap._baseTexture && sprite.bitmap._baseTexture.update) {
      sprite.bitmap._baseTexture.update();
    }

    return true;
  } catch (error) {
    console.error('重绘失败:', error);
    return false;
  }
};

// 创建编辑器状态存储
const useEditorStore = create((set) => ({
  // 当前选中的元素
  selectedElement: null,
  // 当前选中元素的索引
  selectedElementIndex: -1,
  // 是否正在拖动元素
  isDragging: false,
  // 拖动开始的位置
  dragStartPosition: { x: 0, y: 0 },
  // 元素原始位置
  elementOriginalPosition: { x: 0, y: 0 },
  // 元素列表
  elements: [],
  // 元素列表更新时间戳，用于控制重新渲染
  elementsUpdateTimestamp: 0,
  // 保存状态
  isSaving: false,
  lastSaveTime: null,

  // 设置选中的元素
  setSelectedElement: (element, index) => set({
    selectedElement: element,
    selectedElementIndex: index,
    isDragging: false,
    dragStartPosition: { x: 0, y: 0 },
    elementOriginalPosition: {
      x: element?.type === 'text' ? element.x : element?.dx,
      y: element?.type === 'text' ? element.y : element?.dy
    }
  }),

  // 清除选中的元素
  clearSelectedElement: () => set({
    selectedElement: null,
    selectedElementIndex: -1,
    isDragging: false,
    dragStartPosition: { x: 0, y: 0 },
    elementOriginalPosition: { x: 0, y: 0 }
  }),

  // 开始拖动
  startDragging: (position) => set((state) => {
    // 获取当前sprite
    const sprite = window.SpriteEditor?.currentSprite;
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
      return state;
    }

    // 获取选中的元素
    const element = sprite.bitmap.elements[state.selectedElementIndex];
    if (!element) return state;

    // 获取元素的当前位置
    const elementPosition = {
      x: element.type === 'text' ? element.x : element.dx,
      y: element.type === 'text' ? element.y : element.dy
    };

    return {
      ...state,
      isDragging: true,
      dragStartPosition: position,
      elementOriginalPosition: elementPosition
    };
  }),

  // 更新元素列表
  updateElements: () => {
    const sprite = getCurrentSprite();
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
      return;
    }

    // 转换为我们需要的格式并设置ID
    const formattedElements = sprite.bitmap.elements.map((element, index) => ({
      ...element,
      id: element.id || `element_${Date.now()}_${index}` // 如果没有id，生成一个
    }));

    // 更新elements状态和时间戳
    set({
      elements: formattedElements,
      elementsUpdateTimestamp: Date.now()
    });
  },

  // 防抖的元素更新方法
  updateElementsDebounced: (() => {
    let timeoutId = null;
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(() => {
        const store = useEditorStore.getState();
        store.updateElements();
      }, 100); // 100ms 防抖
    };
  })(),

  // 重绘方法 - 更新后自动更新元素列表
  redraw: () => {
    redraw();

    // 调用updateElements方法更新元素列表
    const store = useEditorStore.getState();
    store.updateElements();
  },

  // 重绘并显示包围盒
  redrawWithBounds: () => {
    const store = useEditorStore.getState();
    store.redraw();

    // 延迟绘制包围盒，确保重绘完成
    setTimeout(() => {
      store.drawSelectedElementBounds();
    }, 10);
  },

  // 绘制选中元素的包围盒
  drawSelectedElementBounds: () => {
    const state = useEditorStore.getState();
    if (state.selectedElementIndex === -1) return;

    const sprite = getCurrentSprite();
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
      return;
    }

    const element = sprite.bitmap.elements[state.selectedElementIndex];
    if (!element) return;

    // 获取元素边界
    let bounds = {
      x: 0,
      y: 0,
      width: 0,
      height: 0
    };

    if (element.type === 'text') {
      // 应用RPG Maker相同的X和Y坐标调整逻辑
      const maxWidth = element.maxWidth || 0xffffffff;
      const align = element.align || 'left';
      let tx = element.x;

      if (align === "center") {
        tx += maxWidth / 2;
      } else if (align === "right") {
        tx += maxWidth;
      }

      const lineHeight = element.lineHeight || 36;
      const fontSize = sprite.bitmap.fontSize || 28;
      const adjustedY = element.y + lineHeight / 2 + fontSize * 0.35;

      // 计算实际文本宽度
      const context = sprite.bitmap.context;
      let textWidth = element.maxWidth || 200;
      if (context && element.text) {
        context.save();
        context.font = sprite.bitmap._makeFontNameText();
        textWidth = Math.min(context.measureText(element.text).width, textWidth);
        context.restore();
      }

      // 根据对齐方式计算包围盒的实际X位置
      let boundingX = tx;
      if (align === "center") {
        boundingX = tx - textWidth / 2;
      } else if (align === "right") {
        boundingX = tx - textWidth;
      }

      bounds = {
        x: boundingX,
        y: adjustedY - fontSize, // 从alphabetic基线向上延伸
        width: textWidth,
        height: fontSize
      };
    } else {
      bounds = {
        x: element.dx,
        y: element.dy,
        width: element.dw || 64,
        height: element.dh || 64
      };
    }

    // 绘制包围盒边框
    try {
      // 直接使用canvas context绘制，避免被记录到elements中
      const context = sprite.bitmap.context;
      if (context) {
        context.save();
        context.strokeStyle = '#ff0000';  // 红色边框
        context.lineWidth = 2;
        context.setLineDash([5, 5]);  // 虚线效果
        context.strokeRect(
          bounds.x - 2,
          bounds.y - 2,
          bounds.width + 4,
          bounds.height + 4
        );
        context.restore();

        // 更新纹理
        if (sprite.bitmap._baseTexture && sprite.bitmap._baseTexture.update) {
          sprite.bitmap._baseTexture.update();
        }

        console.log('绘制选中元素包围盒:', bounds);
      }
    } catch (error) {
      console.error('绘制包围盒失败:', error);
    }
  },

  // 拖动中
  updateDragging: (position) => {
    const state = useEditorStore.getState();
    if (!state.isDragging || !state.selectedElement) return;

    const dx = position.x - state.dragStartPosition.x;
    const dy = position.y - state.dragStartPosition.y;

    // 获取当前sprite
    const sprite = getCurrentSprite();
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
      return;
    }

    // 获取选中的元素
    const element = sprite.bitmap.elements[state.selectedElementIndex];
    if (!element) return;

    // 更新元素位置
    if (element.type === 'text') {
      element.x = state.elementOriginalPosition.x + dx;
      element.y = state.elementOriginalPosition.y + dy;
    } else {
      element.dx = state.elementOriginalPosition.x + dx;
      element.dy = state.elementOriginalPosition.y + dy;
    }

    // 直接重绘，不触发状态更新
    redraw();

    // 延迟绘制包围盒
    setTimeout(() => {
      const store = useEditorStore.getState();
      store.drawSelectedElementBounds();
    }, 0);
  },

  // 结束拖动
  stopDragging: () => set((state) => {
    if (!state.isDragging) return state;

    // 获取当前sprite
    const sprite = window.SpriteEditor?.currentSprite;
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
      return { ...state, isDragging: false };
    }

    // 获取选中的元素
    const element = sprite.bitmap.elements[state.selectedElementIndex];
    if (!element) return { ...state, isDragging: false };

    // 更新元素的原始位置，以便下次拖动时使用正确的起始位置
    const newElementOriginalPosition = {
      x: element.type === 'text' ? element.x : element.dx,
      y: element.type === 'text' ? element.y : element.dy
    };

    // 拖拽结束时使用防抖更新元素列表
    setTimeout(() => {
      const store = useEditorStore.getState();
      store.updateElementsDebounced();
      store.drawSelectedElementBounds();
    }, 0);

    return {
      ...state,
      isDragging: false,
      elementOriginalPosition: newElementOriginalPosition
    };
  }),

  // 检查点击位置是否在元素包围盒内
  isPointInElementBounds: (point) => {
    const state = useEditorStore.getState();
    if (state.selectedElementIndex === -1) return false;

    // 获取当前sprite中的实时元素数据，而不是使用缓存的selectedElement
    const sprite = getCurrentSprite();
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
      return false;
    }

    const element = sprite.bitmap.elements[state.selectedElementIndex];
    if (!element) return false;

    let bounds = {
      x: 0,
      y: 0,
      width: 0,
      height: 0
    };

    if (element.type === 'text') {
      bounds = {
        x: element.x,
        y: element.y,
        width: element.maxWidth || 200,
        height: element.lineHeight || 36
      };
    } else {
      bounds = {
        x: element.dx,
        y: element.dy,
        width: element.dw || 64,
        height: element.dh || 64
      };
    }

    console.log('检查边界:', { point, bounds, element: { type: element.type, x: element.x, y: element.y, dx: element.dx, dy: element.dy } });

    return (
      point.x >= bounds.x &&
      point.x <= bounds.x + bounds.width &&
      point.y >= bounds.y &&
      point.y <= bounds.y + bounds.height
    );
  },

  // 获取指定点击位置下的所有元素（按深度排序，最上层的在前面）
  getElementsAtPoint: (point) => {
    const sprite = getCurrentSprite();
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
      return [];
    }

    const elementsAtPoint = [];

    // 遍历所有元素，检查点击位置是否在元素边界内
    sprite.bitmap.elements.forEach((element, index) => {
      let bounds = {
        x: 0,
        y: 0,
        width: 0,
        height: 0
      };

      if (element.type === 'text') {
        bounds = {
          x: element.x,
          y: element.y,
          width: element.maxWidth || 200,
          height: element.lineHeight || 36
        };
      } else {
        bounds = {
          x: element.dx,
          y: element.dy,
          width: element.dw || 64,
          height: element.dh || 64
        };
      }

      // 检查点击位置是否在元素边界内
      if (
        point.x >= bounds.x &&
        point.x <= bounds.x + bounds.width &&
        point.y >= bounds.y &&
        point.y <= bounds.y + bounds.height
      ) {
        elementsAtPoint.push({
          element,
          index,
          bounds
        });
      }
    });

    // 按索引倒序排列，因为后添加的元素在上层（深度更大）
    elementsAtPoint.reverse();

    console.log('点击位置下的元素:', { point, elementsAtPoint });
    return elementsAtPoint;
  },

  // 🎯 优化的元素选择逻辑（只有点击在包围盒内且是最上层元素才显示包围盒）
  selectElementAtPoint: (point) => {
    const store = useEditorStore.getState();
    const elementsAtPoint = store.getElementsAtPoint(point);

    console.log('🎯 点击位置:', point, '找到元素数量:', elementsAtPoint.length);

    if (elementsAtPoint.length === 0) {
      // 🎯 没有元素，清除选中状态和所有包围盒
      console.log('点击位置没有元素，清除选中状态和包围盒');
      store.clearSelectedElement();

      // 重绘canvas，清除所有包围盒
      setTimeout(() => {
        redraw(); // 直接调用底层redraw函数，清除包围盒
      }, 10);

      return null;
    }

    // 🎯 选择深度最高的元素（数组第一个，因为已经按深度倒序排列）
    const topElement = elementsAtPoint[0];
    console.log('🎯 选中深度最高的元素:', {
      type: topElement.element.type,
      index: topElement.index,
      bounds: topElement.bounds,
      clickPoint: point
    });

    // 🎯 验证点击确实在包围盒内部（双重检查）
    const isInBounds = (
      point.x >= topElement.bounds.x &&
      point.x <= topElement.bounds.x + topElement.bounds.width &&
      point.y >= topElement.bounds.y &&
      point.y <= topElement.bounds.y + topElement.bounds.height
    );

    if (!isInBounds) {
      console.log('🎯 点击位置不在最上层元素包围盒内，清除选中状态');
      store.clearSelectedElement();
      setTimeout(() => {
        redraw();
      }, 10);
      return null;
    }

    // 🎯 设置选中元素
    store.setSelectedElement(topElement.element, topElement.index);

    // 🎯 只为选中的元素绘制包围盒
    setTimeout(() => {
      // 先重绘清除所有包围盒
      redraw();
      // 再绘制选中元素的包围盒
      setTimeout(() => {
        store.drawSelectedElementBounds();
      }, 5);
    }, 10);

    return topElement;
  },

  // 保存当前sprite
  saveSprite: async () => {
    // 设置保存状态
    set({ isSaving: true });

    try {
      if (window.SpriteEditor && window.SpriteEditor.saveCurrentSprite) {
        const result = window.SpriteEditor.saveCurrentSprite();

        // 更新保存时间
        set({
          isSaving: false,
          lastSaveTime: new Date().toISOString()
        });

        return result;
      } else {
        throw new Error('保存方法不可用');
      }
    } catch (error) {
      set({ isSaving: false });
      throw error;
    }
  }
}));

export default useEditorStore;
