/**
 * URL 处理模块
 * 负责处理 Sprite 的 bitmap URL 或图片路径
 */

/**
 * 处理 URL 或图片路径
 * @param urlSource URL 或图片路径
 * @param varName 变量名
 * @param indent 缩进字符串
 * @returns 生成的代码字符串
 */
export function processUrl(
  urlSource: string,
  varName: string,
  indent: string
): string {
  console.log('=== 开始处理 URL/图片路径 ===');
  console.log(`URL 源: ${urlSource}`);
  
  // 跳过无效的 URL
  if (!urlSource || urlSource.startsWith('blob:')) {
    console.log('跳过无效的 URL');
    return '';
  }
  
  let code = '';
  
  // 检查是否为特殊的引用名称，使用对应的默认加载方法
  const specialBitmapCode = generateSpecialBitmapCode(varName, indent);
  if (specialBitmapCode) {
    code += specialBitmapCode;
  } else {
    // 普通 URL 处理
    code += generateBitmapLoadingCode(urlSource, varName, indent);
  }
  
  console.log('URL 处理完成');
  return code;
}

/**
 * 生成特殊 bitmap 的代码（基于变量名判断）
 */
function generateSpecialBitmapCode(varName: string, indent: string): string {
  // 检查是否为标准背景精灵的引用名称
  if (varName === '_backSprite1') {
    return `${indent}${varName}.bitmap = ImageManager.loadTitle1($dataSystem.title1Name);\n`;
  } else if (varName === '_backSprite2') {
    return `${indent}${varName}.bitmap = ImageManager.loadTitle2($dataSystem.title2Name);\n`;
  } else if (varName === '_gameTitleSprite') {
    return `${indent}${varName}.bitmap = new Bitmap(Graphics.width, Graphics.height);\n`;
  }
  
  return '';
}

/**
 * 生成普通 bitmap 加载代码
 */
function generateBitmapLoadingCode(
  urlSource: string,
  varName: string,
  indent: string
): string {
  let code = '';
  
  // 解析路径，分离文件夹和文件名
  const { folder, filename } = parseBitmapPath(urlSource);
  
  if (folder && filename) {
    // 使用 ImageManager.loadBitmap 方法
    code += `${indent}${varName}.bitmap = ImageManager.loadBitmap('${folder}', '${filename}');\n`;
  } else {
    // 使用 ImageManager.loadBitmapFromUrl 方法
    code += `${indent}${varName}.bitmap = ImageManager.loadBitmapFromUrl('${urlSource}');\n`;
  }
  
  return code;
}

/**
 * 解析位图路径，分离文件夹和文件名
 */
function parseBitmapPath(bitmapPath: string): { folder: string; filename: string } {
  // 移除 .png 扩展名
  const pathWithoutExt = bitmapPath.replace(/\.png$/i, '');
  
  // 查找最后一个斜杠
  const lastSlashIndex = pathWithoutExt.lastIndexOf('/');
  
  if (lastSlashIndex === -1) {
    // 没有斜杠，整个路径就是文件名
    return { folder: '', filename: pathWithoutExt };
  }
  
  // 分离文件夹和文件名
  const folder = pathWithoutExt.substring(0, lastSlashIndex + 1); // 包含最后的斜杠
  const filename = pathWithoutExt.substring(lastSlashIndex + 1);
  
  return { folder, filename };
}

/**
 * 检查 URL 是否有效
 */
export function isValidUrl(url: string): boolean {
  if (!url || typeof url !== 'string') {
    return false;
  }
  
  // 跳过 blob URL
  if (url.startsWith('blob:')) {
    return false;
  }
  
  // 检查是否为空字符串
  if (url.trim() === '') {
    return false;
  }
  
  return true;
}

/**
 * 获取 URL 的文件扩展名
 */
export function getUrlExtension(url: string): string {
  const match = url.match(/\.([^.]+)$/);
  return match ? match[1].toLowerCase() : '';
}

/**
 * 检查是否为图片 URL
 */
export function isImageUrl(url: string): boolean {
  const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'];
  const extension = getUrlExtension(url);
  return imageExtensions.includes(extension);
}
