/**
 * 对象树同步机制
 * 监听RPG Maker对象变化，增量同步到树状态
 */

import type { ObjectTreeState } from './objectTreeStore';
import { ObjectTreeActions } from './objectTreeActions';
import { generateNodeId, getObjectTypeName } from './typeUtils';

/**
 * 对象树同步器
 */
export class ObjectTreeSync {

  /**
   * 同步根对象变化
   * 比较当前对象结构和状态中的结构，只更新变化的部分
   */
  static syncRootObject(
    state: ObjectTreeState,
    newRootObject: any,
    newRootObjectType: string
  ): ObjectTreeState {
    console.log('=== 同步根对象变化 ===');

    // 如果没有现有的根对象，直接设置新的
    if (!state.rootNodeId || state.nodes.size === 0) {
      console.log('没有现有根对象，直接设置新的');
      return ObjectTreeActions.setRootObject(state, newRootObject, newRootObjectType);
    }

    const currentRootNode = state.nodes.get(state.rootNodeId);
    if (!currentRootNode) {
      console.log('当前根节点不存在，重新设置');
      return ObjectTreeActions.setRootObject(state, newRootObject, newRootObjectType);
    }

    // 检查根对象是否发生变化（只比较对象引用和类型，不生成新ID）
    if (currentRootNode.currentObject !== newRootObject ||
      currentRootNode.objectType !== newRootObjectType) {
      console.log('根对象发生变化，重新构建');
      // 保存当前的展开状态
      const expandedNodes = new Set(state.expandedNodes);
      const selectedNodeId = state.selectedNodeId;

      // 重新设置根对象
      let newState = ObjectTreeActions.setRootObject(state, newRootObject, newRootObjectType);

      // 尝试恢复展开状态
      newState = this.restoreExpandedStates(newState, expandedNodes);

      // 尝试恢复选中状态
      if (selectedNodeId && newState.nodes.has(selectedNodeId)) {
        newState = ObjectTreeActions.selectNode(newState, selectedNodeId);
      }

      return newState;
    }

    // 根对象没有变化，检查子对象变化
    console.log('🔍 根对象未变化，检查子对象变化');
    console.log('🔍 根对象子对象数量:', newRootObject.children ? newRootObject.children.length : 0);
    return this.syncChildrenChanges(state, state.rootNodeId, newRootObject);
  }

  /**
   * 同步子对象变化（优化版本，减少不必要的重建）
   */
  private static syncChildrenChanges(
    state: ObjectTreeState,
    parentNodeId: string,
    parentObject: any
  ): ObjectTreeState {
    const parentNode = state.nodes.get(parentNodeId);
    if (!parentNode) return state;

    // 获取当前的子对象
    const currentChildren = this.getObjectChildren(parentObject);

    // 获取状态中的子节点
    const stateChildIds = state.nodeChildren.get(parentNodeId) || [];
    const stateChildNodes = stateChildIds.map(id => state.nodes.get(id)).filter(node => node);

    console.log(`🔍 [syncChildrenChanges] 父对象: ${parentObject.constructor.name}`);
    console.log(`🔍 [syncChildrenChanges] 当前子对象数量: ${currentChildren.length}`);
    console.log(`🔍 [syncChildrenChanges] 状态中子节点数量: ${stateChildNodes.length}`);
    console.log(`🔍 [syncChildrenChanges] 当前子对象:`, currentChildren.map(child => child.constructor.name));

    // 比较子对象数量，如果数量不同，说明有增删
    if (currentChildren.length !== stateChildNodes.length) {
      console.log(`🔍 子对象数量变化: ${stateChildNodes.length} -> ${currentChildren.length}`);

      // 简单处理：如果数量变化，重新构建这个分支
      let newState = { ...state };

      // 移除所有旧的子节点
      for (const childId of stateChildIds) {
        newState = ObjectTreeActions.removeNode(newState, childId);
      }

      // 添加所有新的子节点
      for (const child of currentChildren) {
        newState = ObjectTreeActions.addNode(newState, parentNodeId, child);
      }

      return newState;
    }

    // 数量相同，检查对象引用是否变化
    let hasChanges = false;
    for (let i = 0; i < currentChildren.length; i++) {
      const currentChild = currentChildren[i];
      const stateChild = stateChildNodes[i];

      if (stateChild && stateChild.currentObject !== currentChild) {
        hasChanges = true;
        break;
      }
    }

    if (hasChanges) {
      console.log('子对象引用发生变化，重新构建');
      let newState = { ...state };

      // 移除所有旧的子节点
      for (const childId of stateChildIds) {
        newState = ObjectTreeActions.removeNode(newState, childId);
      }

      // 添加所有新的子节点
      for (const child of currentChildren) {
        newState = ObjectTreeActions.addNode(newState, parentNodeId, child);
      }

      return newState;
    }

    // 没有变化，递归检查子节点
    let newState = state;
    for (let i = 0; i < currentChildren.length; i++) {
      const child = currentChildren[i];
      const childId = stateChildIds[i];

      if (newState.nodes.has(childId)) {
        newState = this.syncChildrenChanges(newState, childId, child);
      }
    }

    return newState;
  }

  /**
   * 获取对象的子对象
   */
  private static getObjectChildren(obj: any): any[] {
    let actualObj = obj;

    // 如果是包装对象，提取 displayObject
    if (obj && typeof obj === 'object' && obj.displayObject) {
      actualObj = obj.displayObject;
    }

    if (actualObj && actualObj.children && Array.isArray(actualObj.children)) {
      return actualObj.children.filter((child: any) => child != null);
    }

    return [];
  }

  /**
   * 恢复展开状态
   */
  private static restoreExpandedStates(
    state: ObjectTreeState,
    expandedNodes: Set<string>
  ): ObjectTreeState {
    const newExpandedNodes = new Set<string>();

    // 只恢复仍然存在的节点的展开状态
    for (const nodeId of expandedNodes) {
      if (state.nodes.has(nodeId)) {
        newExpandedNodes.add(nodeId);
      }
    }

    // 确保根节点展开
    if (state.rootNodeId) {
      newExpandedNodes.add(state.rootNodeId);
    }

    console.log('恢复展开状态，节点数量:', newExpandedNodes.size);

    return {
      ...state,
      expandedNodes: newExpandedNodes,
      lastUpdateTime: Date.now()
    };
  }

  /**
   * 处理对象创建事件
   */
  static handleObjectCreated(
    state: ObjectTreeState,
    parentObject: any,
    newObject: any
  ): ObjectTreeState {
    console.log('=== 处理对象创建事件 ===');
    console.log('父对象:', parentObject);
    console.log('新对象:', newObject);
    console.log('当前状态节点数:', state.nodes.size);

    if (!state.rootNodeId) {
      console.warn('没有根对象，无法添加子对象');
      return state;
    }

    // 查找父节点
    const parentNodeId = this.findNodeByObject(state, parentObject);
    console.log('查找到的父节点ID:', parentNodeId);

    if (!parentNodeId) {
      console.warn('找不到父节点，重新同步整个树');
      console.log('所有现有节点:');
      for (const [nodeId, node] of state.nodes) {
        console.log(`  ${nodeId}: ${node.displayName} (${node.currentObject?.constructor?.name})`);
      }

      // 如果找不到父节点，可能是结构发生了变化，重新同步
      const rootNode = state.nodes.get(state.rootNodeId);
      if (rootNode) {
        console.log('重新同步整个树');
        return this.syncRootObject(state, rootNode.currentObject, rootNode.objectType || '');
      }
      return state;
    }

    console.log('在父节点添加新对象:', parentNodeId);
    return ObjectTreeActions.addNode(state, parentNodeId, newObject);
  }

  /**
   * 处理对象删除事件
   */
  static handleObjectDeleted(
    state: ObjectTreeState,
    deletedObject: any
  ): ObjectTreeState {
    console.log('=== 处理对象删除事件 ===');

    // 查找要删除的节点
    const nodeIdToDelete = this.findNodeByObject(state, deletedObject);
    if (!nodeIdToDelete) {
      console.warn('找不到要删除的节点');
      return state;
    }

    console.log('删除节点:', nodeIdToDelete);
    return ObjectTreeActions.removeNode(state, nodeIdToDelete);
  }

  /**
   * 根据对象查找节点ID
   */
  private static findNodeByObject(state: ObjectTreeState, targetObject: any): string | null {
    for (const [nodeId, node] of state.nodes) {
      if (node.currentObject === targetObject) {
        return nodeId;
      }

      // 如果是包装对象，也检查 displayObject
      if (node.currentObject &&
        typeof node.currentObject === 'object' &&
        node.currentObject.displayObject === targetObject) {
        return nodeId;
      }
    }

    return null;
  }

  /**
   * 强制重新同步整个树（用于调试或恢复）
   */
  static forceResync(
    state: ObjectTreeState,
    rootObject: any,
    rootObjectType: string
  ): ObjectTreeState {
    console.log('=== 强制重新同步 ===');

    // 保存当前状态
    const expandedNodes = new Set(state.expandedNodes);
    const selectedNodeId = state.selectedNodeId;

    // 重新构建
    let newState = ObjectTreeActions.setRootObject(state, rootObject, rootObjectType);

    // 恢复状态
    newState = this.restoreExpandedStates(newState, expandedNodes);
    if (selectedNodeId && newState.nodes.has(selectedNodeId)) {
      newState = ObjectTreeActions.selectNode(newState, selectedNodeId);
    }

    return newState;
  }
}
