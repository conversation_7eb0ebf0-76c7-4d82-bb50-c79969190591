/**
 * 右键菜单逻辑处理
 */

import type { ContextMenuData, ObjectTreeNodeData } from './types';

/**
 * 右键菜单状态管理
 */
export class ContextMenuManager {
  private contextMenuData: ContextMenuData = {
    x: 0,
    y: 0,
    visible: false,
    targetNode: null
  };

  private callbacks: {
    onShow?: (data: ContextMenuData) => void;
    onHide?: () => void;
    onCreateObject?: (objectType: string, targetNode: ObjectTreeNodeData) => void;
    onDeleteObject?: (targetNode: ObjectTreeNodeData) => void;
  } = {};

  /**
   * 设置回调函数
   */
  setCallbacks(callbacks: {
    onShow?: (data: ContextMenuData) => void;
    onHide?: () => void;
    onCreateObject?: (objectType: string, targetNode: ObjectTreeNodeData) => void;
    onDeleteObject?: (targetNode: ObjectTreeNodeData) => void;
  }) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * 显示右键菜单
   * @param event 鼠标事件
   * @param targetNode 目标节点
   */
  showContextMenu(event: MouseEvent, targetNode: ObjectTreeNodeData) {
    console.log('显示右键菜单:', {
      targetNode: targetNode.displayName,
      position: { x: event.clientX, y: event.clientY }
    });

    // 阻止默认右键菜单
    event.preventDefault();
    event.stopPropagation();

    // 计算菜单位置，确保不超出屏幕边界
    const menuWidth = 200; // 预估菜单宽度
    const menuHeight = 300; // 预估菜单高度
    
    let x = event.clientX;
    let y = event.clientY;

    // 检查右边界
    if (x + menuWidth > window.innerWidth) {
      x = window.innerWidth - menuWidth - 10;
    }

    // 检查下边界
    if (y + menuHeight > window.innerHeight) {
      y = window.innerHeight - menuHeight - 10;
    }

    // 确保不超出左上边界
    x = Math.max(10, x);
    y = Math.max(10, y);

    // 更新菜单数据
    this.contextMenuData = {
      x,
      y,
      visible: true,
      targetNode
    };

    // 触发显示回调
    this.callbacks.onShow?.(this.contextMenuData);
  }

  /**
   * 隐藏右键菜单
   */
  hideContextMenu() {
    console.log('隐藏右键菜单');
    
    this.contextMenuData = {
      x: 0,
      y: 0,
      visible: false,
      targetNode: null
    };

    // 触发隐藏回调
    this.callbacks.onHide?.();
  }

  /**
   * 处理创建对象
   * @param objectType 对象类型
   */
  handleCreateObject(objectType: string) {
    if (!this.contextMenuData.targetNode) {
      console.warn('没有选中的目标节点');
      return;
    }

    console.log('处理创建对象:', {
      objectType,
      targetNode: this.contextMenuData.targetNode.displayName
    });

    // 触发创建对象回调
    this.callbacks.onCreateObject?.(objectType, this.contextMenuData.targetNode);

    // 隐藏菜单
    this.hideContextMenu();
  }

  /**
   * 处理删除对象
   */
  handleDeleteObject() {
    if (!this.contextMenuData.targetNode) {
      console.warn('没有选中的目标节点');
      return;
    }

    if (this.contextMenuData.targetNode.isRoot) {
      console.warn('不能删除根节点');
      return;
    }

    console.log('处理删除对象:', {
      targetNode: this.contextMenuData.targetNode.displayName
    });

    // 触发删除对象回调
    this.callbacks.onDeleteObject?.(this.contextMenuData.targetNode);

    // 隐藏菜单
    this.hideContextMenu();
  }

  /**
   * 获取当前菜单数据
   */
  getContextMenuData(): ContextMenuData {
    return { ...this.contextMenuData };
  }

  /**
   * 检查菜单是否可见
   */
  isVisible(): boolean {
    return this.contextMenuData.visible;
  }

  /**
   * 获取目标节点
   */
  getTargetNode(): ObjectTreeNodeData | null {
    return this.contextMenuData.targetNode;
  }
}

/**
 * 全局右键菜单管理器实例
 */
export const contextMenuManager = new ContextMenuManager();

/**
 * 处理右键点击事件的工具函数
 * @param event 鼠标事件
 * @param targetNode 目标节点
 */
export function handleContextMenu(event: MouseEvent, targetNode: ObjectTreeNodeData) {
  contextMenuManager.showContextMenu(event, targetNode);
}

/**
 * 关闭右键菜单的工具函数
 */
export function closeContextMenu() {
  contextMenuManager.hideContextMenu();
}

/**
 * 检查点击位置是否在菜单外部
 * @param event 鼠标事件
 * @param menuElement 菜单元素
 */
export function isClickOutsideMenu(event: MouseEvent, menuElement: HTMLElement | null): boolean {
  if (!menuElement) return true;
  
  const rect = menuElement.getBoundingClientRect();
  const x = event.clientX;
  const y = event.clientY;
  
  return x < rect.left || x > rect.right || y < rect.top || y > rect.bottom;
}

/**
 * 获取安全的菜单位置
 * @param x 原始 x 坐标
 * @param y 原始 y 坐标
 * @param menuWidth 菜单宽度
 * @param menuHeight 菜单高度
 */
export function getSafeMenuPosition(
  x: number, 
  y: number, 
  menuWidth: number = 200, 
  menuHeight: number = 300
): { x: number; y: number } {
  let safeX = x;
  let safeY = y;

  // 检查右边界
  if (safeX + menuWidth > window.innerWidth) {
    safeX = window.innerWidth - menuWidth - 10;
  }

  // 检查下边界
  if (safeY + menuHeight > window.innerHeight) {
    safeY = window.innerHeight - menuHeight - 10;
  }

  // 确保不超出左上边界
  safeX = Math.max(10, safeX);
  safeY = Math.max(10, safeY);

  return { x: safeX, y: safeY };
}
