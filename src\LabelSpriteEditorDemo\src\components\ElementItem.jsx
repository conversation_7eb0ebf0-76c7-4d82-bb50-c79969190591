import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Divider,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardDoubleArrowUpIcon from '@mui/icons-material/KeyboardDoubleArrowUp';
import KeyboardDoubleArrowDownIcon from '@mui/icons-material/KeyboardDoubleArrowDown';
import NumberInput from './NumberInput';
import useEditorStore from '../store/editorStore';

const ElementItem = ({ element, index }) => {
  const [textValue, setTextValue] = useState(element.type === 'text' ? element.text : '');
  const [align, setAlign] = useState(element.align || 'left');
  const [elementState, setElementState] = useState({
    // 文本元素属性
    x: element.x || 0,
    y: element.y || 0,
    maxWidth: element.maxWidth || 200,
    lineHeight: element.lineHeight || 36,
    align: element.align || 'left',
    // 图片元素属性
    dx: element.dx || 0,
    dy: element.dy || 0,
    dw: element.dw || 64,
    dh: element.dh || 64
  });

  // 只订阅需要的状态和方法，避免拖拽时的重新渲染
  const selectedElementIndex = useEditorStore((state) => state.selectedElementIndex);
  const setSelectedElement = useEditorStore((state) => state.setSelectedElement);
  const redraw = useEditorStore((state) => state.redraw);
  const redrawWithBounds = useEditorStore((state) => state.redrawWithBounds);

  // 获取当前sprite
  const getCurrentSprite = () => {
    if (window.SpriteEditor && window.SpriteEditor.currentSprite) {
      return window.SpriteEditor.currentSprite;
    }
    return null;
  };

  // 当元素属性变化时更新状态
  useEffect(() => {
    if (element.type === 'text') {
      setTextValue(element.text || '');
      setAlign(element.align || 'left');
      setElementState(prev => ({
        ...prev,
        x: element.x || 0,
        y: element.y || 0,
        maxWidth: element.maxWidth || 200,
        lineHeight: element.lineHeight || 36,
        align: element.align || 'left'
      }));
    } else {
      setElementState(prev => ({
        ...prev,
        dx: element.dx || 0,
        dy: element.dy || 0,
        dw: element.dw || 64,
        dh: element.dh || 64
      }));
    }
  }, [element]);

  // 更新元素并触发重绘
  const updateElement = (updates) => {
    const sprite = getCurrentSprite();
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements || !sprite.bitmap.redrawing) {
      console.error('无法获取sprite或相关属性');
      return;
    }

    try {
      // 直接使用传入的索引
      if (index < 0 || index >= sprite.bitmap.elements.length) {
        console.error('元素索引超出范围:', index, '数组长度:', sprite.bitmap.elements.length);
        return;
      }

      // 获取原始元素
      const originalElement = sprite.bitmap.elements[index];

      // 更新元素
      const updatedElement = {
        ...originalElement,
        ...updates
      };

      sprite.bitmap.elements[index] = updatedElement;

      // 如果是图片元素且更新了sx, sy, sw, sh等属性，同时更新source中的regions
      if (originalElement.type === 'image' &&
        originalElement.source &&
        originalElement.source.regions &&
        (updates.sx !== undefined || updates.sy !== undefined ||
          updates.sw !== undefined || updates.sh !== undefined)) {

        // 查找匹配的区域
        const regionIndex = originalElement.source.regions.findIndex(r =>
          r.sx === originalElement.sx &&
          r.sy === originalElement.sy &&
          r.sw === originalElement.sw &&
          r.sh === originalElement.sh
        );

        if (regionIndex !== -1) {
          // 更新区域
          originalElement.source.regions[regionIndex] = {
            ...originalElement.source.regions[regionIndex],
            sx: updatedElement.sx,
            sy: updatedElement.sy,
            sw: updatedElement.sw,
            sh: updatedElement.sh
          };
          console.log('更新source中的区域:', originalElement.source.regions[regionIndex]);
        }
      }

      // 重新绘制
      redraw();
    } catch (e) {
      console.error('更新元素失败:', e);
    }
  };

  // 删除元素
  const deleteElement = () => {
    const sprite = getCurrentSprite();
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements || !sprite.bitmap.redrawing) {
      console.error('无法获取sprite或相关属性');
      return;
    }

    try {
      // 直接使用传入的索引
      if (index < 0 || index >= sprite.bitmap.elements.length) {
        console.error('元素索引超出范围:', index, '数组长度:', sprite.bitmap.elements.length);
        return;
      }

      // 从数组中删除元素
      sprite.bitmap.elements.splice(index, 1);

      // 重新绘制
      redraw();
    } catch (e) {
      console.error('删除元素失败:', e);
    }
  };

  // 复制元素
  const copyElement = () => {
    const sprite = getCurrentSprite();
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements || !sprite.bitmap.redrawing) {
      console.error('无法获取sprite或相关属性');
      return;
    }

    try {
      // 直接使用传入的索引
      if (index < 0 || index >= sprite.bitmap.elements.length) {
        console.error('元素索引超出范围:', index, '数组长度:', sprite.bitmap.elements.length);
        return;
      }

      // 获取原始元素
      const originalElement = sprite.bitmap.elements[index];

      // 手动创建新对象，避免循环引用问题
      let clonedElement;

      // 根据元素类型创建不同的克隆对象
      if (originalElement.type === 'text') {
        // 文本元素
        clonedElement = {
          type: 'text',
          id: `${originalElement.id || 'text'}_copy_${Date.now()}`,
          text: originalElement.text || '',
          x: (originalElement.x || 0) + 20,
          y: (originalElement.y || 0) + 20,
          maxWidth: originalElement.maxWidth || 200,
          lineHeight: originalElement.lineHeight || 36,
          align: originalElement.align || 'left',
          // 复制字体相关属性
          fontFace: originalElement.fontFace,
          fontSize: originalElement.fontSize,
          fontBold: originalElement.fontBold,
          fontItalic: originalElement.fontItalic,
          textColor: originalElement.textColor,
          outlineColor: originalElement.outlineColor,
          outlineWidth: originalElement.outlineWidth
        };
      } else {
        // 图片元素
        clonedElement = {
          type: 'image',
          id: `${originalElement.id || 'image'}_copy_${Date.now()}`,
          source: originalElement.source, // 直接引用原始source
          sx: originalElement.sx || 0,
          sy: originalElement.sy || 0,
          sw: originalElement.sw || 64,
          sh: originalElement.sh || 64,
          dx: (originalElement.dx || 0) + 20,
          dy: (originalElement.dy || 0) + 20,
          dw: originalElement.dw || 64,
          dh: originalElement.dh || 64,
          label: originalElement.label,
          gridIndex: originalElement.gridIndex,
          // 复制边界框信息
          bounds: originalElement.bounds ? {
            x: (originalElement.bounds.x || 0) + 20,
            y: (originalElement.bounds.y || 0) + 20,
            width: originalElement.bounds.width || 64,
            height: originalElement.bounds.height || 64
          } : undefined
        };
      }

      // 添加到elements数组
      sprite.bitmap.elements.push(clonedElement);
      console.log('元素已复制:', clonedElement);

      // 重新绘制
      redraw();
    } catch (e) {
      console.error('复制元素失败:', e);
    }
  };

  // 深度控制方法
  // 增加一个深度（向上移动一层）
  const moveUpOneLayer = () => {
    const sprite = getCurrentSprite();
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
      console.error('无法获取sprite或相关属性');
      return;
    }

    const elements = sprite.bitmap.elements;
    if (index >= elements.length - 1) {
      console.log('元素已经在最上层');
      return;
    }

    // 与下一个元素交换位置（数组中后面的元素显示在上层）
    [elements[index], elements[index + 1]] = [elements[index + 1], elements[index]];
    console.log(`元素从索引 ${index} 移动到索引 ${index + 1}`);

    // 强制重绘并更新元素列表
    console.log('深度交换完成，强制重绘');

    // 直接调用 bitmap 的 redrawing 方法
    if (sprite.bitmap && sprite.bitmap.redrawing) {
      sprite.bitmap.redrawing();
    }

    // 延迟更新包围盒和元素列表
    setTimeout(() => {
      redrawWithBounds();
    }, 50);
  };

  // 减少一个深度（向下移动一层）
  const moveDownOneLayer = () => {
    const sprite = getCurrentSprite();
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
      console.error('无法获取sprite或相关属性');
      return;
    }

    const elements = sprite.bitmap.elements;
    if (index <= 0) {
      console.log('元素已经在最下层');
      return;
    }

    // 与前一个元素交换位置
    [elements[index], elements[index - 1]] = [elements[index - 1], elements[index]];
    console.log(`元素从索引 ${index} 移动到索引 ${index - 1}`);

    // 强制重绘并更新元素列表
    console.log('深度交换完成，强制重绘');

    // 直接调用 bitmap 的 redrawing 方法
    if (sprite.bitmap && sprite.bitmap.redrawing) {
      sprite.bitmap.redrawing();
    }

    // 延迟更新包围盒和元素列表
    setTimeout(() => {
      redrawWithBounds();
    }, 50);
  };

  // 移动到最上层
  const moveToTop = () => {
    const sprite = getCurrentSprite();
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
      console.error('无法获取sprite或相关属性');
      return;
    }

    const elements = sprite.bitmap.elements;
    if (index >= elements.length - 1) {
      console.log('元素已经在最上层');
      return;
    }

    // 移除当前元素并添加到数组末尾
    const element = elements.splice(index, 1)[0];
    elements.push(element);
    console.log(`元素从索引 ${index} 移动到最上层（索引 ${elements.length - 1}）`);

    // 强制重绘并更新元素列表
    console.log('深度交换完成，强制重绘');

    // 直接调用 bitmap 的 redrawing 方法
    if (sprite.bitmap && sprite.bitmap.redrawing) {
      sprite.bitmap.redrawing();
    }

    // 延迟更新包围盒和元素列表
    setTimeout(() => {
      redrawWithBounds();
    }, 50);
  };

  // 移动到最下层
  const moveToBottom = () => {
    const sprite = getCurrentSprite();
    if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
      console.error('无法获取sprite或相关属性');
      return;
    }

    const elements = sprite.bitmap.elements;
    if (index <= 0) {
      console.log('元素已经在最下层');
      return;
    }

    // 移除当前元素并添加到数组开头
    const element = elements.splice(index, 1)[0];
    elements.unshift(element);
    console.log(`元素从索引 ${index} 移动到最下层（索引 0）`);

    // 强制重绘并更新元素列表
    console.log('深度交换完成，强制重绘');

    // 直接调用 bitmap 的 redrawing 方法
    if (sprite.bitmap && sprite.bitmap.redrawing) {
      sprite.bitmap.redrawing();
    }

    // 延迟更新包围盒和元素列表
    setTimeout(() => {
      redrawWithBounds();
    }, 50);
  };

  // 处理文本内容变更
  const handleTextChange = (e) => {
    const newText = e.target.value;
    setTextValue(newText);
    // 不再立即更新，而是等待确定按钮点击
  };

  // 处理确定按钮点击
  const handleConfirmText = (e) => {
    // 阻止事件冒泡，避免触发元素选中
    if (e) {
      e.stopPropagation();
    }
    // 更新文本并重绘
    updateElement({ text: textValue });
  };

  // 处理键盘事件
  const handleKeyDown = (e) => {
    // 按下Enter键确认修改
    if (e.key === 'Enter') {
      handleConfirmText();
    }
  };

  // 处理对齐方式变更
  const handleAlignChange = (e) => {
    // 阻止事件冒泡，避免触发元素选中
    e.stopPropagation();
    const newAlign = e.target.value;
    setAlign(newAlign);
    updateElement({ align: newAlign });
  };

  // 处理输入框点击事件
  const handleInputClick = (e) => {
    // 阻止事件冒泡，避免触发元素选中
    e.stopPropagation();
  };

  // 处理输入框焦点事件
  const handleInputFocus = (e) => {
    // 阻止事件冒泡，避免触发元素选中
    e.stopPropagation();
  };

  // 处理属性变更
  const handlePropChange = (propName, value) => {
    setElementState(prev => ({
      ...prev,
      [propName]: value
    }));
    updateElement({ [propName]: value });
  };

  // 处理删除按钮点击
  const handleDeleteClick = (e) => {
    // 阻止事件冒泡，避免触发元素选中
    e.stopPropagation();

    if (window.confirm('确定要删除这个元素吗？')) {
      deleteElement();
    }
  };

  // 处理复制按钮点击
  const handleCopyClick = (e) => {
    // 阻止事件冒泡，避免触发元素选中
    e.stopPropagation();

    copyElement();
  };

  // 处理深度控制按钮点击
  const handleMoveUpOneClick = (e) => {
    e.stopPropagation();
    moveUpOneLayer();
  };

  const handleMoveDownOneClick = (e) => {
    e.stopPropagation();
    moveDownOneLayer();
  };

  const handleMoveToTopClick = (e) => {
    e.stopPropagation();
    moveToTop();
  };

  const handleMoveToBottomClick = (e) => {
    e.stopPropagation();
    moveToBottom();
  };

  // 渲染文本元素
  const renderTextElement = () => {
    return (
      <Box sx={{ width: '100%' }}>
        <Box sx={{ mb: 0.25 }}>
          <Box sx={{ display: 'flex', mb: 0.25 }}>
            <TextField
              fullWidth
              size="small"
              value={textValue}
              onChange={handleTextChange}
              onKeyDown={handleKeyDown}
              onClick={handleInputClick}
              onFocus={handleInputFocus}
              placeholder="输入文本"
              sx={{
                mr: 0.5,
                '& .MuiInputBase-root': {
                  height: '28px',
                  fontSize: '0.8rem'
                }
              }}
            />
            <Button
              variant="contained"
              color="primary"
              size="small"
              onClick={handleConfirmText}
              title="确认修改"
              sx={{
                minWidth: '40px',
                px: 0.5,
                height: '28px',
                fontSize: '0.75rem'
              }}
            >
              确定
            </Button>
          </Box>
        </Box>
        <Divider sx={{ my: 0.25 }} />
        <Grid container spacing={0.25}>
          <Grid item xs={6}>
            <Box onClick={handleInputClick}>
              <NumberInput
                label="X位置"
                value={elementState.x}
                onChange={(value) => handlePropChange('x', value)}
                min={0}
                max={816}
              />
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box onClick={handleInputClick}>
              <NumberInput
                label="Y位置"
                value={elementState.y}
                onChange={(value) => handlePropChange('y', value)}
                min={0}
                max={624}
              />
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box onClick={handleInputClick}>
              <NumberInput
                label="最大宽度"
                value={elementState.maxWidth}
                onChange={(value) => handlePropChange('maxWidth', value)}
                min={10}
                max={816}
              />
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box onClick={handleInputClick}>
              <NumberInput
                label="行高"
                value={elementState.lineHeight}
                onChange={(value) => handlePropChange('lineHeight', value)}
                min={10}
                max={100}
              />
            </Box>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth size="small" sx={{ mt: 0.25, mb: 0 }} onClick={handleInputClick}>
              <InputLabel id="align-label" sx={{ fontSize: '0.75rem' }}>对齐方式</InputLabel>
              <Select
                labelId="align-label"
                id="align"
                value={align}
                onChange={handleAlignChange}
                onFocus={handleInputFocus}
                label="对齐方式"
                size="small"
                sx={{
                  height: '24px',
                  fontSize: '0.75rem',
                  '.MuiSelect-select': {
                    paddingTop: '2px',
                    paddingBottom: '2px'
                  }
                }}
              >
                <MenuItem value="left" sx={{ fontSize: '0.75rem', minHeight: '24px' }}>左对齐</MenuItem>
                <MenuItem value="center" sx={{ fontSize: '0.75rem', minHeight: '24px' }}>居中</MenuItem>
                <MenuItem value="right" sx={{ fontSize: '0.75rem', minHeight: '24px' }}>右对齐</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // 渲染图片元素
  const renderImageElement = () => {
    // 检查是否有网格信息 - 现在从source.gridSettings中获取
    const hasGridInfo = element.source &&
      element.source.gridSettings &&
      element.source.gridSettings.rows &&
      element.source.gridSettings.columns;

    // 处理网格单元格选择
    const handleGridCellSelect = (row, col) => {
      if (!element.source || !element.source.gridSettings) return;

      const { rows, columns, rowGap, columnGap } = element.source.gridSettings;
      const newIndex = row * columns + col;

      // 计算新的源区域坐标，考虑间距
      const totalColumnGap = columnGap * (columns - 1);
      const totalRowGap = rowGap * (rows - 1);
      const cellWidth = Math.max(1, (element.source._image.width - totalColumnGap) / columns);
      const cellHeight = Math.max(1, (element.source._image.height - totalRowGap) / rows);

      // 计算实际位置，考虑间距
      let sx = 0;
      for (let i = 0; i < col; i++) {
        sx += cellWidth + (i < columns - 1 ? columnGap : 0);
      }

      let sy = 0;
      for (let i = 0; i < row; i++) {
        sy += cellHeight + (i < rows - 1 ? rowGap : 0);
      }
      const sw = cellWidth;
      const sh = cellHeight;

      // 更新元素
      updateElement({
        sx,
        sy,
        sw,
        sh,
        // 保存网格索引，但不再使用gridInfo对象
        gridIndex: newIndex
      });

      // 如果source中有regions数组，尝试查找匹配的区域并更新
      if (element.source && element.source.regions) {
        // 查找匹配的区域（基于网格索引）
        const regionIndex = element.source.regions.findIndex(
          r => r.gridIndex === newIndex &&
            r.sx === sx &&
            r.sy === sy &&
            r.sw === sw &&
            r.sh === sh
        );

        if (regionIndex !== -1) {
          console.log('找到匹配的区域:', element.source.regions[regionIndex]);
        } else {
          // 如果没有找到匹配的区域，创建一个新的
          const newRegion = {
            id: `region_${Date.now()}`,
            label: `网格 ${row + 1}x${col + 1}`,
            sx,
            sy,
            sw,
            sh,
            gridIndex: newIndex
          };

          element.source.regions.push(newRegion);
          console.log('添加新区域到source:', newRegion);
        }
      }
    };

    return (
      <Box sx={{ width: '100%' }}>
        <Box sx={{ mb: 0.25 }}>
          {element.source && (
            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', display: 'block', lineHeight: 1.2 }}>
              {element.label || '图片元素'}
              {element.useExternalResource ? ' (外部资源)' : ''}
            </Typography>
          )}
        </Box>

        {/* 如果有网格信息，显示选区下拉列表 */}
        {hasGridInfo && (
          <Box sx={{ mb: 1 }}>
            <Typography variant="caption" sx={{ fontSize: '0.75rem', mb: 0.5, display: 'block' }}>
              选区列表 ({element.source.gridSettings.rows}x{element.source.gridSettings.columns})
            </Typography>
            <FormControl fullWidth size="small" onClick={handleInputClick}>
              <Select
                value={element.gridIndex || 0}
                onChange={(e) => {
                  // 阻止事件冒泡
                  e.stopPropagation();
                  const idx = e.target.value;
                  const row = Math.floor(idx / element.source.gridSettings.columns);
                  const col = idx % element.source.gridSettings.columns;
                  handleGridCellSelect(row, col);
                }}
                onFocus={handleInputFocus}
                sx={{
                  height: '28px',
                  fontSize: '0.75rem',
                  '.MuiSelect-select': {
                    paddingTop: '2px',
                    paddingBottom: '2px'
                  }
                }}
              >
                {Array.from({ length: element.source.gridSettings.rows * element.source.gridSettings.columns }).map((_, idx) => {
                  const row = Math.floor(idx / element.source.gridSettings.columns);
                  const col = idx % element.source.gridSettings.columns;
                  return (
                    <MenuItem
                      key={idx}
                      value={idx}
                      sx={{ fontSize: '0.75rem', minHeight: '24px' }}
                    >
                      选区 {row + 1}x{col + 1}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
          </Box>
        )}

        <Divider sx={{ my: 0.25 }} />
        <Grid container spacing={0.25}>
          <Grid item xs={6}>
            <Box onClick={handleInputClick}>
              <NumberInput
                label="X位置"
                value={elementState.dx}
                onChange={(value) => handlePropChange('dx', value)}
                min={0}
                max={816}
              />
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box onClick={handleInputClick}>
              <NumberInput
                label="Y位置"
                value={elementState.dy}
                onChange={(value) => handlePropChange('dy', value)}
                min={0}
                max={624}
              />
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box onClick={handleInputClick}>
              <NumberInput
                label="宽度"
                value={elementState.dw}
                onChange={(value) => handlePropChange('dw', value)}
                min={1}
                max={816}
              />
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box onClick={handleInputClick}>
              <NumberInput
                label="高度"
                value={elementState.dh}
                onChange={(value) => handlePropChange('dh', value)}
                min={1}
                max={624}
              />
            </Box>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // 处理元素点击
  const handleElementClick = () => {
    setSelectedElement(element, index);
    // 延迟一点时间绘制包围盒，确保状态已更新
    setTimeout(() => {
      redrawWithBounds();
    }, 10);
  };

  return (
    <Paper
      elevation={1}
      sx={{
        p: 0.75,
        mb: 0.5,
        borderLeft: element.type === 'text' ? '3px solid #1976d2' : '3px solid #9c27b0',
        cursor: 'pointer',
        backgroundColor: selectedElementIndex === index ? 'rgba(25, 118, 210, 0.08)' : 'transparent'
      }}
      onClick={handleElementClick}
    >
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 0.25,
        mt: -0.25
      }}>
        <Typography
          variant="subtitle2"
          color={element.type === 'text' ? 'primary' : 'secondary'}
          sx={{ fontSize: '0.85rem', fontWeight: 'bold' }}
        >
          {element.type === 'text' ? '文本元素' : '图片元素'}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {/* 深度控制按钮组 */}
          <Box sx={{ display: 'flex', marginRight: '4px', border: '1px solid #e0e0e0', borderRadius: '4px' }}>
            <IconButton
              size="small"
              onClick={handleMoveToTopClick}
              title="移到最上层"
              sx={{
                padding: '1px',
                borderRadius: '2px 0 0 2px',
                minWidth: '16px',
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
              }}
            >
              <KeyboardDoubleArrowUpIcon sx={{ fontSize: '0.8rem' }} />
            </IconButton>
            <IconButton
              size="small"
              onClick={handleMoveUpOneClick}
              title="上移一层"
              sx={{
                padding: '1px',
                borderRadius: 0,
                minWidth: '16px',
                borderLeft: '1px solid #e0e0e0',
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
              }}
            >
              <KeyboardArrowUpIcon sx={{ fontSize: '0.8rem' }} />
            </IconButton>
            <IconButton
              size="small"
              onClick={handleMoveDownOneClick}
              title="下移一层"
              sx={{
                padding: '1px',
                borderRadius: 0,
                minWidth: '16px',
                borderLeft: '1px solid #e0e0e0',
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
              }}
            >
              <KeyboardArrowDownIcon sx={{ fontSize: '0.8rem' }} />
            </IconButton>
            <IconButton
              size="small"
              onClick={handleMoveToBottomClick}
              title="移到最下层"
              sx={{
                padding: '1px',
                borderRadius: '0 2px 2px 0',
                minWidth: '16px',
                borderLeft: '1px solid #e0e0e0',
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
              }}
            >
              <KeyboardDoubleArrowDownIcon sx={{ fontSize: '0.8rem' }} />
            </IconButton>
          </Box>

          {/* 功能按钮组 */}
          <IconButton
            size="small"
            color="primary"
            onClick={handleCopyClick}
            title="复制元素"
            sx={{ padding: '1px', marginRight: '2px' }}
          >
            <ContentCopyIcon sx={{ fontSize: '0.9rem' }} />
          </IconButton>
          <IconButton
            size="small"
            color="error"
            onClick={handleDeleteClick}
            title="删除元素"
            sx={{ padding: '1px', marginRight: '-4px' }}
          >
            <DeleteIcon sx={{ fontSize: '0.9rem' }} />
          </IconButton>
        </Box>
      </Box>
      {element.type === 'text' ? renderTextElement() : renderImageElement()}
    </Paper>
  );
};

ElementItem.propTypes = {
  element: PropTypes.shape({
    type: PropTypes.oneOf(['text', 'image']).isRequired,
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    // 文本元素特有属性
    text: PropTypes.string,
    x: PropTypes.number,
    y: PropTypes.number,
    maxWidth: PropTypes.number,
    lineHeight: PropTypes.number,
    align: PropTypes.string,
    // 图像元素特有属性
    source: PropTypes.any,
    dx: PropTypes.number,
    dy: PropTypes.number,
    dw: PropTypes.number,
    dh: PropTypes.number,
    useExternalResource: PropTypes.bool
  }).isRequired,
  index: PropTypes.number.isRequired
};

export default ElementItem;

