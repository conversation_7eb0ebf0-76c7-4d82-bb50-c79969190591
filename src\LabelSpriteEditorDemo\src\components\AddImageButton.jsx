import { useState, useEffect } from 'react';
import {
  Button
} from '@mui/material';
import ImageIcon from '@mui/icons-material/Image';
import ImgEditor from './ImgEditor';

const AddImageButton = () => {
  const [isEditorOpen, setIsEditorOpen] = useState(false);

  // 打开图片编辑器
  const openEditor = () => {
    setIsEditorOpen(true);
  };

  // 关闭图片编辑器
  const closeEditor = () => {
    setIsEditorOpen(false);
  };

  // 监听外部打开编辑器事件
  useEffect(() => {
    const handleOpenImgEditor = (event) => {
      console.log('AddImageButton收到打开编辑器请求:', event.detail);
      setIsEditorOpen(true);
    };

    document.addEventListener('openImgEditor', handleOpenImgEditor);

    return () => {
      document.removeEventListener('openImgEditor', handleOpenImgEditor);
    };
  }, []);

  return (
    <>
      <Button
        variant="contained"
        color="secondary"
        onClick={openEditor}
        startIcon={<ImageIcon />}
        size="small"
        sx={{ fontSize: '0.8rem', py: 0.5 }}
      >
        添加图片
      </Button>

      <ImgEditor open={isEditorOpen} onClose={closeEditor} />
    </>
  );
};

export default AddImageButton;
