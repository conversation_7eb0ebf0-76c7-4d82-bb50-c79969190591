/**
 * 对象树统一状态管理
 * 单一数据源，避免状态混乱和循环依赖
 */

import { writable, derived } from 'svelte/store';
import type { ObjectTreeNodeData } from './types';
import type { NodeTypeInfo } from './typeUtils';

/**
 * 纯数据的节点结构（不包含UI状态）
 */
export interface PureObjectTreeNodeData {
  id: string;
  currentObject: any;
  displayName: string;
  isRPGMakerType: boolean;
  isRoot: boolean;
  depth: number;
  objectType?: string;
  typeInfo?: NodeTypeInfo;
}

/**
 * 对象树状态接口
 */
export interface ObjectTreeState {
  // 树结构数据（扁平化存储，便于查找和更新）
  nodes: Map<string, PureObjectTreeNodeData>;
  rootNodeId: string | null;

  // UI状态
  expandedNodes: Set<string>;
  selectedNodeId: string | null;

  // 树结构关系
  nodeParents: Map<string, string>; // nodeId -> parentId
  nodeChildren: Map<string, string[]>; // nodeId -> childIds[]

  // 元数据
  maxDepth: number;
  lastUpdateTime: number;
}

/**
 * 初始状态
 */
const initialState: ObjectTreeState = {
  nodes: new Map(),
  rootNodeId: null,
  expandedNodes: new Set(),
  selectedNodeId: null,
  nodeParents: new Map(),
  nodeChildren: new Map(),
  maxDepth: 10,
  lastUpdateTime: 0
};

/**
 * 主要的状态存储
 */
export const objectTreeState = writable<ObjectTreeState>(initialState);

/**
 * 缓存相关变量
 */
let cachedTreeData: ObjectTreeNodeData[] = [];
let lastCachedUpdateTime = 0;

/**
 * 派生状态：根据扁平化数据构建层次结构用于渲染（带缓存优化）
 */
export const hierarchicalTreeData = derived(
  objectTreeState,
  ($state) => {
    // 缓存优化：如果状态没有变化，直接返回缓存的数据
    if ($state.lastUpdateTime === lastCachedUpdateTime && cachedTreeData.length > 0) {
      return cachedTreeData;
    }

    if (!$state.rootNodeId) {
      cachedTreeData = [];
      lastCachedUpdateTime = $state.lastUpdateTime;
      return cachedTreeData;
    }

    function buildHierarchy(nodeId: string): ObjectTreeNodeData | null {
      const node = $state.nodes.get(nodeId);
      if (!node) return null;

      const children = $state.nodeChildren.get(nodeId) || [];
      const childNodes = children
        .map(childId => buildHierarchy(childId))
        .filter(child => child !== null) as ObjectTreeNodeData[];

      return {
        ...node,
        children: childNodes,
        expanded: $state.expandedNodes.has(nodeId)
      };
    }

    const rootNode = buildHierarchy($state.rootNodeId);
    cachedTreeData = rootNode ? [rootNode] : [];
    lastCachedUpdateTime = $state.lastUpdateTime;

    console.log('hierarchicalTreeData 重新构建，节点数:', cachedTreeData.length);
    return cachedTreeData;
  }
);

/**
 * 派生状态：选中的节点数据
 */
export const selectedNode = derived(
  objectTreeState,
  ($state) => {
    if (!$state.selectedNodeId) return null;
    return $state.nodes.get($state.selectedNodeId) || null;
  }
);

/**
 * 派生状态：树统计信息
 */
export const treeStats = derived(
  objectTreeState,
  ($state) => {
    const totalNodes = $state.nodes.size;
    let rpgMakerNodes = 0;
    let customNodes = 0;
    let rootNodes = 0;

    for (const node of $state.nodes.values()) {
      if (node.isRPGMakerType) {
        rpgMakerNodes++;
      } else {
        customNodes++;
      }

      if (node.isRoot) {
        rootNodes++;
      }
    }

    return {
      totalNodes,
      rpgMakerNodes,
      customNodes,
      rootNodes
    };
  }
);

/**
 * 工具函数：获取节点的所有子节点ID（递归）
 */
export function getAllChildNodeIds(state: ObjectTreeState, nodeId: string): string[] {
  const result: string[] = [];
  const children = state.nodeChildren.get(nodeId) || [];

  for (const childId of children) {
    result.push(childId);
    result.push(...getAllChildNodeIds(state, childId));
  }

  return result;
}

/**
 * 工具函数：获取节点路径（从根到指定节点）
 */
export function getNodePath(state: ObjectTreeState, nodeId: string): string[] {
  const path: string[] = [];
  let currentId: string | undefined = nodeId;

  while (currentId) {
    path.unshift(currentId);
    currentId = state.nodeParents.get(currentId);
  }

  return path;
}

/**
 * 工具函数：检查节点是否存在
 */
export function nodeExists(state: ObjectTreeState, nodeId: string): boolean {
  return state.nodes.has(nodeId);
}

/**
 * 工具函数：获取节点深度
 */
export function getNodeDepth(state: ObjectTreeState, nodeId: string): number {
  const node = state.nodes.get(nodeId);
  return node ? node.depth : 0;
}
