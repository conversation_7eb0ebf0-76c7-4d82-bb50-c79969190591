/**
 * 场景分析器 - 简化版本
 * 使用扁平化结构分析场景对象，分离场景对象和WindowLayer对象
 */

import { get } from 'svelte/store';
import { globalObjectState } from '../stores/objectState';
import { ObjectReconstructor } from './objectReconstructor';
import { detectWindowType } from './utils/windowTypeDetector';

/**
 * 场景分析器类
 */
export class SceneAnalyzer {

  /**
   * 主方法：从全局store获取rootObject并分析场景
   * 返回符合type.ts中SceneAnalysisResult接口的数据结构
   */
  static analyzeCurrentScene(): any {
    console.log('开始分析当前场景...');

    // 1. 从全局store获取rootObject
    const currentState = get(globalObjectState);
    if (!currentState.rootObject) {
      console.warn('全局store中没有rootObject');
      return null;
    }

    const rootObject = currentState.rootObject;
    const sceneClassName = currentState.rootObjectType || rootObject.constructor.name;

    // 2. 获取地图ID（如果是Scene_Map）
    let mapId: number | undefined;
    if (sceneClassName === 'Scene_Map') {
      mapId = this.getCurrentMapId();
    }

    // 3. 分析场景对象和WindowLayer对象（扁平化）
    const sceneObjects = new Map<string, any>();
    const windowLayerObjects = new Map<string, any>();

    this.analyzeObjectsFlat(rootObject, '', sceneObjects, windowLayerObjects);

    // 4. 提取场景本身的属性
    const sceneProperties = this.extractSceneProperties(rootObject);

    // 5. 构建符合type.ts定义的maps结构
    const maps = new Map<string, Map<string, any>>();
    if (mapId !== undefined) {
      maps.set(mapId.toString(), sceneObjects);
    } else {
      maps.set('default', sceneObjects);
    }

    // 返回符合SceneAnalysisResult接口的结构
    return {
      sceneClassName,
      sceneProperties,
      maps,
      windowLayer: windowLayerObjects
    };
  }

  /**
   * 获取当前地图ID（仅用于 Scene_Map）
   */
  private static getCurrentMapId(): number {
    try {
      const gameMap = (window as any).$gameMap;
      if (gameMap && gameMap.mapId) {
        return gameMap.mapId();
      }
      return 1;
    } catch (error) {
      console.error('获取地图ID时出错:', error);
      return 1;
    }
  }

  /**
   * 提取场景本身的属性
   */
  private static extractSceneProperties(sceneObject: any): any {
    // 使用ObjectReconstructor提取场景属性
    const properties = ObjectReconstructor.extractEssentialProperties(sceneObject);
    const windowName = detectWindowType(sceneObject);

    return {
      className: sceneObject.constructor.name,
      isWindow: !!windowName,
      needsMapScrollListener: false, // 场景本身不需要地图滚动监听
      name: sceneObject.name || '',
      ...properties
    };
  }

  /**
   * 扁平化分析对象树
   * @param parentObject 父对象
   * @param parentPath 父路径
   * @param sceneObjects 场景对象Map
   * @param windowLayerObjects WindowLayer对象Map
   */
  private static analyzeObjectsFlat(
    parentObject: any,
    parentPath: string,
    sceneObjects: Map<string, any>,
    windowLayerObjects: Map<string, any>
  ): void {
    if (!parentObject || !parentObject.children) {
      return;
    }

    for (let i = 0; i < parentObject.children.length; i++) {
      const child = parentObject.children[i];
      if (!child || !child.constructor) continue;

      const className = child.constructor.name;
      const path = parentPath ? `${parentPath}.children[${i}]` : `scene.children[${i}]`;

      // 提取对象属性
      const properties = ObjectReconstructor.extractEssentialProperties(child);
      const windowName = detectWindowType(child);

      const objectData = {
        className,
        isWindow: !!windowName,
        needsMapScrollListener: this.needsMapScrollListener(className, parentPath),
        name: child.name || '',
        ...properties
      };

      // 根据对象类型和位置分类
      if (className === 'WindowLayer') {
        // WindowLayer的子对象单独处理
        this.analyzeWindowLayerFlat(child, path, windowLayerObjects);
      } else if (parentPath.includes('windowLayer') || windowName) {
        // WindowLayer中的对象
        windowLayerObjects.set(path, objectData);
      } else {
        // 场景对象
        sceneObjects.set(path, objectData);

        // 递归处理子对象（除了特殊对象）
        if (className !== 'Spriteset_Map') {
          this.analyzeObjectsFlat(child, path, sceneObjects, windowLayerObjects);
        }
      }
    }
  }

  /**
   * 分析WindowLayer的子对象
   */
  private static analyzeWindowLayerFlat(
    windowLayerObject: any,
    windowLayerPath: string,
    windowLayerObjects: Map<string, any>
  ): void {
    if (!windowLayerObject || !windowLayerObject.children) return;

    for (let i = 0; i < windowLayerObject.children.length; i++) {
      const child = windowLayerObject.children[i];
      if (!child || !child.constructor) continue;

      const className = child.constructor.name;
      const path = `${windowLayerPath}.children[${i}]`;

      const properties = ObjectReconstructor.extractEssentialProperties(child);
      const windowName = detectWindowType(child);

      const objectData = {
        className,
        isWindow: !!windowName,
        needsMapScrollListener: false, // WindowLayer中的对象不需要地图滚动监听
        name: child.name || '',
        ...properties
      };

      windowLayerObjects.set(path, objectData);
    }
  }

  /**
   * 判断是否需要地图滚动监听
   */
  private static needsMapScrollListener(className: string, parentPath: string): boolean {
    // WindowLayer中的对象不需要地图滚动监听
    if (parentPath.includes('windowLayer') || detectWindowType(className)) {
      return false;
    }

    // 场景中的精灵对象需要地图滚动监听
    return className.startsWith('Sprite') || className === 'TilingSprite';
  }

  /**
   * 打印分析摘要（兼容方法）
   */
  static printAnalysisSummary(result: any): void {
    console.log('=== 场景分析摘要 ===');
    console.log(`场景类型: ${result.sceneClassName}`);

    // 计算总对象数量
    let totalObjects = 0;
    const objectTypes = new Set<string>();

    // 统计maps中的对象
    if (result.maps) {
      for (const [mapId, objectMap] of result.maps) {
        totalObjects += objectMap.size;
        for (const obj of objectMap.values()) {
          objectTypes.add(obj.className);
        }
      }
    }

    // 统计windowLayer中的对象
    if (result.windowLayer) {
      totalObjects += result.windowLayer.size;
      for (const obj of result.windowLayer.values()) {
        objectTypes.add(obj.className);
      }
    }

    console.log(`总对象数量: ${totalObjects}`);
    console.log(`对象类型: ${Array.from(objectTypes).join(', ')}`);
    console.log('=== 分析摘要结束 ===');
  }

  /**
   * 生成场景重写代码（兼容方法）
   * @param result 分析结果
   * @returns 生成的插件代码
   */
  static generateSceneRewriteCode(result: any): string {
    console.log('=== 生成场景重写代码 ===');

    // 简单的代码生成，返回基本的插件结构
    const { sceneClassName, timestamp } = result;

    let code = `// 场景重写代码 - ${sceneClassName}\n`;
    code += `// 基于编辑器对象结构生成\n`;
    code += `// 生成时间: ${new Date(timestamp).toISOString()}\n\n`;

    code += `(() => {\n`;
    code += `    'use strict';\n\n`;
    code += `    console.log('加载 ${sceneClassName} 重写插件');\n`;
    code += `    // TODO: 实现具体的场景重写逻辑\n`;
    code += `})();\n`;

    console.log(`插件代码生成完成，长度: ${code.length}`);
    return code;
  }

}
