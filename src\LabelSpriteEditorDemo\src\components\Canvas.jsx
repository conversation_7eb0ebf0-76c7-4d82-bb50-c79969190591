import React, { useRef, useEffect, useCallback } from 'react';
import useEditorStore from '../store/editorStore';
import { elementsToSpriteByBuf, spriteToElements } from '../utils/spriteDataConverter';

// 创建一个全局对象，用于存储对外暴露的方法
window.SpriteEditor = window.SpriteEditor || {};

// 预先定义对外暴露的方法，确保它们在组件渲染前就可用
window.SpriteEditor.setExternalSpriteForEdit = (elementsArray) => {
  console.log('预定义的setExternalSpriteForEdit被调用，等待RPG Maker引擎初始化后处理:', elementsArray);
  // 暂存数据，等待引擎初始化后处理
  window.SpriteEditor.pendingElementsData = elementsArray;
};

window.SpriteEditor.setExternalResourcePathForAccess = (path) => {
  console.log('预定义的setExternalResourcePathForAccess被调用，path:', path);
  window.SpriteEditor.externalResourcePath = path;
};

// 新增：从外部加载图片文件的方法
window.SpriteEditor.loadExternalImageFile = (filePath) => {
  console.log('预定义的loadExternalImageFile被调用，filePath:', filePath);
  // 暂存数据，等待引擎初始化后处理
  window.SpriteEditor.pendingImagePath = filePath;
};

// 新增：从外部加载图片ArrayBuffer的方法
window.SpriteEditor.loadExternalImageBuffer = (filePath, arrayBuffer) => {
  console.log('预定义的loadExternalImageBuffer被调用，filePath:', filePath, 'bufferSize:', arrayBuffer?.byteLength);
  // 暂存数据，等待引擎初始化后处理
  window.SpriteEditor.pendingImageData = { filePath, arrayBuffer };
};
window.SpriteEditor.setGlobalFonts = []
// 预先定义保存方法
window.SpriteEditor.saveCurrentSprite = () => {
  console.log('预定义的saveCurrentSprite被调用');
  return null;
};

// 使用React.memo包装组件，避免不必要的重新渲染
const Canvas = React.memo(() => {
  console.log('Canvas渲染');
  const containerRef = useRef(null);
  const canvasRef = useRef(null);
  const scriptLoadedRef = useRef(false);
  const lastUpdateTimeRef = useRef(0);
  const requestAnimationFrameIdRef = useRef(null);
  const updateElements = useEditorStore((state) => state.updateElements);

  // 从 store 中获取状态和方法
  const {
    isPointInElementBounds,
    selectElementAtPoint,
    startDragging,
    updateDragging,
    stopDragging
  } = useEditorStore();

  // 处理鼠标按下事件
  const handleMouseDown = useCallback((e) => {
    // 获取鼠标相对于canvas的坐标
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    console.log('鼠标点击位置:', { x, y });

    // 获取当前状态
    const state = useEditorStore.getState();

    // 如果已经有选中的元素，先检查是否点击在当前选中元素上（用于拖拽）
    if (state.selectedElement && isPointInElementBounds({ x, y })) {
      console.log('点击在当前选中元素上，开始拖动');
      startDragging({ x, y });
      return;
    }

    // 否则，尝试在点击位置选中新元素
    console.log('尝试在点击位置选中元素');
    selectElementAtPoint({ x, y });
  }, [isPointInElementBounds, selectElementAtPoint, startDragging]);

  // 处理鼠标移动事件
  const handleMouseMove = useCallback((e) => {
    // 获取当前状态
    const state = useEditorStore.getState();
    if (!state.isDragging || !state.selectedElement) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // 使用 requestAnimationFrame 限制更新频率
    if (requestAnimationFrameIdRef.current) {
      return; // 如果已经有一个更新请求在等待，就不再创建新的请求
    }

    requestAnimationFrameIdRef.current = requestAnimationFrame(() => {
      const now = performance.now();
      // 限制更新频率为每秒60次（约16.7毫秒一次）
      if (now - lastUpdateTimeRef.current >= 16.7) {
        updateDragging({ x, y });
        lastUpdateTimeRef.current = now;
      }
      requestAnimationFrameIdRef.current = null;
    });
  }, [updateDragging]);

  // 处理鼠标松开事件
  const handleMouseUp = useCallback(() => {
    // 取消任何待处理的 requestAnimationFrame
    if (requestAnimationFrameIdRef.current) {
      cancelAnimationFrame(requestAnimationFrameIdRef.current);
      requestAnimationFrameIdRef.current = null;
    }
    stopDragging();
  }, [stopDragging]);

  // 加载 runtime.js（只执行一次）
  useEffect(() => {
    if (scriptLoadedRef.current) return;

    // 创建script标签加载runtime.js
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = 'src/spriteEditor/runtime.js';
    script.async = true;
    script.onload = () => {
      console.log('runtime.js加载完成');
      scriptLoadedRef.current = true;
    };
    script.onerror = (error) => {
      console.error('加载runtime.js失败:', error);
    };

    // 检查是否已经加载过 runtime.js
    if (!document.querySelector('script[src="src/spriteEditor/runtime.js"]')) {
      document.body.appendChild(script);
      // window.SpriteEditor.setExternalSpriteForEdit(new sprite());
    } else {
      scriptLoadedRef.current = true;
    }
  }, []); // 空依赖数组，确保只执行一次

  // 初始化画布（只执行一次）
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // 检查是否已经创建了canvas
    let canvas = document.getElementById('react-canvas');
    if (!canvas) {
      // 清除之前的canvas
      while (container.firstChild) {
        container.removeChild(container.firstChild);
      }

      // 创建canvas元素
      canvas = document.createElement('canvas');
      canvas.id = 'react-canvas';
      // 🎯 初始化时设置最小尺寸，避免黑屏闪烁
      canvas.width = 1;
      canvas.height = 1;

      // 设置样式使canvas居中
      canvas.style.position = 'absolute';
      canvas.style.margin = 'auto';
      canvas.style.top = 0;
      canvas.style.left = 0;
      canvas.style.right = 0;
      canvas.style.bottom = 0;
      // 🎯 初始时隐藏canvas，等sprite加载完成后再显示
      canvas.style.visibility = 'hidden';

      // 将canvas添加到container中
      container.appendChild(canvas);

      console.log('Canvas组件创建了canvas元素，id为react-canvas');
    }

    // 保存canvas引用
    canvasRef.current = canvas;

    // 初始化RPG Maker引擎
    const initializeRpgMaker = () => {
      // 检查全局对象是否已经初始化
      if (window.Graphics && window.Bitmap && window.Sprite) {
        try {
          console.log('Canvas组件初始化RPG Maker引擎');
          // 获取RPG Maker API
          const Graphics = window.Graphics;
          const Bitmap = window.Bitmap;
          const Sprite = window.Sprite;

          // 不再自动创建默认sprite，统一使用setExternalSpriteForEdit初始化
          console.log('RPG Maker引擎已初始化，等待通过setExternalSpriteForEdit设置sprite');
          window.SpriteEditor.setExternalResourcePathForAccess("src/assets")
          // 更新全局方法，使用最新的API
          window.SpriteEditor.setExternalSpriteForEdit = async (elementsArray, globalFonts) => {
            console.log('Canvas组件更新后的setExternalSpriteForEdit被调用，elements:', elementsArray, '全局字体:', globalFonts);

            if (!elementsArray || !Array.isArray(elementsArray)) {
              console.error('setExternalSpriteForEdit需要一个elements数组参数');
              return;
            }

            try {
              // 🎯 设置全局字体到window.SpriteEditor.globalFonts
              if (globalFonts && Array.isArray(globalFonts) && globalFonts.length > 0) {
                window.SpriteEditor.globalFonts = globalFonts;
                console.log('已设置全局字体:', globalFonts);

                // 触发全局字体更新事件
                const globalFontsEvent = new CustomEvent('globalFontsUpdated', {
                  detail: { fonts: globalFonts }
                });
                document.dispatchEvent(globalFontsEvent);
                console.log('已触发globalFontsUpdated事件');
              }

              // 从elementsArray中提取sprite信息（如果有的话）
              const spriteInfo = elementsArray.spriteInfo || {};

              // 计算所需的sprite尺寸
              let spriteWidth = spriteInfo.canvasWidth || 816;
              let spriteHeight = spriteInfo.canvasHeight || 624;

              // 如果没有提供sprite尺寸，根据elements计算
              if (!spriteInfo.canvasWidth || !spriteInfo.canvasHeight) {
                elementsArray.forEach(element => {
                  if (element.type === 'text') {
                    spriteWidth = Math.max(spriteWidth, element.x + (element.maxWidth || 200));
                    spriteHeight = Math.max(spriteHeight, element.y + (element.lineHeight || 36));
                  } else if (element.type === 'image') {
                    spriteWidth = Math.max(spriteWidth, element.dx + element.dw);
                    spriteHeight = Math.max(spriteHeight, element.dy + element.dh);
                  }
                });
              }

              // 使用转换工具将elements数组转换为sprite
              const newSprite = await elementsToSpriteByBuf(elementsArray, {
                canvasWidth: spriteWidth,
                canvasHeight: spriteHeight,
                spriteX: spriteInfo.x || 0,
                spriteY: spriteInfo.y || 0,
                anchorX: spriteInfo.anchorX || 0,
                anchorY: spriteInfo.anchorY || 0,
                scaleX: spriteInfo.scaleX || 1,
                scaleY: spriteInfo.scaleY || 1,
                rotation: spriteInfo.rotation || 0,
                // 字体属性
                fontBold: spriteInfo.fontBold !== undefined ? spriteInfo.fontBold : false,
                fontFace: spriteInfo.fontFace || "sans-serif",
                fontItalic: spriteInfo.fontItalic !== undefined ? spriteInfo.fontItalic : false,
                fontSize: spriteInfo.fontSize || 16,
                outlineColor: spriteInfo.outlineColor || "rgba(0, 0, 0, 0.5)",
                outlineWidth: spriteInfo.outlineWidth || 3,
                textColor: spriteInfo.textColor || "#ffffff"
              });

              // canvas大小等于sprite的大小
              const canvasSize = {
                width: newSprite.bitmap.width,
                height: newSprite.bitmap.height
              };

              // 设置canvas尺寸等于sprite尺寸
              Graphics._app.renderer.resize(canvasSize.width, canvasSize.height);
              Graphics._app.screen.width = canvasSize.width;
              Graphics._app.screen.height = canvasSize.height;

              // 更新HTML canvas元素尺寸
              const canvasElement = Graphics._app.view;
              if (canvasElement) {
                canvasElement.width = canvasSize.width;
                canvasElement.height = canvasSize.height;
                canvasElement.style.width = canvasSize.width + 'px';
                canvasElement.style.height = canvasSize.height + 'px';
                // 🎯 sprite加载完成后显示canvas，避免黑屏闪烁
                canvasElement.style.visibility = 'visible';
              }
              console.log('canvas尺寸设置为sprite尺寸:', canvasSize);

              // sprite位置设置为(0,0)，不需要居中
              newSprite.x = 0;
              newSprite.y = 0;

              // 保存到全局对象
              window.SpriteEditor.currentSprite = newSprite;

              // 触发sprite更新事件，通知TextSettings等组件更新
              const spriteUpdatedEvent = new CustomEvent('spriteUpdated', {
                detail: { sprite: newSprite }
              });
              document.dispatchEvent(spriteUpdatedEvent);
              console.log('已触发spriteUpdated事件');

              // 如果RPG Maker引擎已初始化，可以立即处理这个sprite
              if (Graphics && Graphics._app && Graphics._app.stage) {
                // 清除舞台上的所有sprite，避免重复
                while (Graphics._app.stage.children.length > 0) {
                  Graphics._app.stage.removeChild(Graphics._app.stage.children[0]);
                }

                // 清除边界引用
                window.SpriteEditor.spriteBounds = null;
                updateElements()
                newSprite.bitmap.redrawing()
                // 将新sprite添加到舞台
                Graphics._app.stage.addChild(newSprite);
                console.log('已将新sprite添加到舞台');

                // 绘制sprite边界
                drawSpriteBounds(newSprite);

                console.log('sprite设置完成，bitmap包含完整的elements信息');
              }

            } catch (error) {
              console.error('setExternalSpriteForEdit执行失败:', error);
            }
          };
          // 测试初始化
          // window.SpriteEditor.setExternalSpriteForEdit(createTestElementsData())
          // 实现保存方法
          window.SpriteEditor.saveCurrentSprite = () => {
            console.log('saveCurrentSprite被调用');

            const sprite = window.SpriteEditor.currentSprite;
            if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
              console.warn('没有可保存的sprite或elements');
              return null;
            }

            try {
              // 使用转换工具将sprite转换为elements数据
              // sprite对象包含所有需要的属性
              const result = spriteToElements(sprite);

              console.log('准备导出的数据:', result);

              // 触发自定义事件，向外部传递数据

              const saveEvent = new CustomEvent('spriteEditorSave', {
                detail: result
              });
              document.dispatchEvent(saveEvent);
              console.log('已触发spriteEditorSave事件', result.elements);
              // window.SpriteEditor.saveCurrentSprite(result.elements);
              return result.elements;

            } catch (error) {
              console.error('保存过程中发生错误:', error);
              return null;
            }
          };

          // 实现从外部加载图片文件的方法
          window.SpriteEditor.loadExternalImageFile = (filePath) => {
            console.log('loadExternalImageFile被调用，filePath:', filePath);

            if (!filePath) {
              console.error('loadExternalImageFile需要一个文件路径参数');
              return;
            }

            // 触发ImgEditor打开并加载指定文件
            const loadImageEvent = new CustomEvent('loadExternalImage', {
              detail: { filePath }
            });
            document.dispatchEvent(loadImageEvent);
            console.log('已触发loadExternalImage事件');
          };


          // 检查是否有待处理的elements数据
          if (window.SpriteEditor.pendingElementsData) {
            console.log('发现待处理的elements数据，自动初始化sprite');
            const pendingData = window.SpriteEditor.pendingElementsData;
            window.SpriteEditor.pendingElementsData = null; // 清除待处理数据

            // 自动调用setExternalSpriteForEdit处理待处理的数据
            setTimeout(() => {
              window.SpriteEditor.setExternalSpriteForEdit(pendingData);
            }, 100);
          }

          // 检查是否有待处理的图片路径
          if (window.SpriteEditor.pendingImagePath) {
            console.log('发现待处理的图片路径，自动加载图片');
            const pendingPath = window.SpriteEditor.pendingImagePath;
            window.SpriteEditor.pendingImagePath = null; // 清除待处理数据

            // 自动调用loadExternalImageFile处理待处理的路径
            setTimeout(() => {
              window.SpriteEditor.loadExternalImageFile(pendingPath);
            }, 100);
          }

          // 检查是否有待处理的图片ArrayBuffer数据
          if (window.SpriteEditor.pendingImageData) {
            console.log('发现待处理的图片ArrayBuffer数据，自动加载图片');
            const pendingData = window.SpriteEditor.pendingImageData;
            window.SpriteEditor.pendingImageData = null; // 清除待处理数据

            // 自动调用loadExternalImageBuffer处理待处理的数据
            setTimeout(() => {
              window.SpriteEditor.loadExternalImageBuffer(pendingData.filePath, pendingData.arrayBuffer);
            }, 100);
          }

          // 绘制sprite边界的函数
          const drawSpriteBounds = (sprite) => {
            if (!sprite || !sprite.bitmap) return;

            try {
              const spriteWidth = sprite.bitmap.width;
              const spriteHeight = sprite.bitmap.height;

              // 清除之前的边界
              if (window.SpriteEditor.spriteBounds) {
                try {
                  Graphics._app.stage.removeChild(window.SpriteEditor.spriteBounds);
                } catch (e) {
                  console.warn('清除旧边界失败:', e);
                }
              }

              // 使用canvas context直接绘制边界
              const context = sprite.bitmap.context;
              if (context) {
                context.save();
                context.strokeStyle = '#00ff00'; // 绿色边框
                context.lineWidth = 3;
                context.setLineDash([10, 5]); // 虚线效果
                context.strokeRect(0, 0, spriteWidth, spriteHeight);
                context.restore();

                // 更新纹理
                if (sprite.bitmap._baseTexture && sprite.bitmap._baseTexture.update) {
                  sprite.bitmap._baseTexture.update();
                }

                console.log('已绘制sprite边界:', { width: spriteWidth, height: spriteHeight });
              } else {
                // 备用方案：创建一个新的bitmap来绘制边界
                const boundsBitmap = new Bitmap(spriteWidth + 6, spriteHeight + 6);
                boundsBitmap.strokeRect(3, 3, spriteWidth, spriteHeight, '#00ff00');

                const boundsSprite = new Sprite(boundsBitmap);
                boundsSprite.x = -3;
                boundsSprite.y = -3;

                // 添加到舞台
                Graphics._app.stage.addChild(boundsSprite);

                // 保存边界对象的引用，以便后续清理
                window.SpriteEditor.spriteBounds = boundsSprite;

                console.log('已使用备用方案绘制sprite边界:', { width: spriteWidth, height: spriteHeight });
              }

            } catch (error) {
              console.error('绘制sprite边界失败:', error);
            }
          };

          // 🎯 第一时间对外暴露初始化完成事件
          const initEvent = new CustomEvent('spriteEditorInitialized', {
            detail: {
              timestamp: Date.now(),
              message: 'Sprite Editor初始化完成',
              apis: {
                setExternalResourcePathForAccess: 'window.SpriteEditor.setExternalResourcePathForAccess',
                setExternalSpriteForEdit: 'window.SpriteEditor.setExternalSpriteForEdit',
                loadExternalImageFile: 'window.SpriteEditor.loadExternalImageFile',
                loadExternalImageBuffer: 'window.SpriteEditor.loadExternalImageBuffer',
                saveCurrentSprite: 'window.SpriteEditor.saveCurrentSprite'
              }
            }
          });
          document.dispatchEvent(initEvent);
          console.log('已触发spriteEditorInitialized事件');

        } catch (e) {
          console.error('初始化RPG Maker引擎失败:', e);
          console.error('错误详情:', e.message, e.stack);
        }
      } else {
        console.log('RPG Maker API尚未加载完成，等待事件触发');

        // 监听RPG Maker引擎初始化完成的事件
        const handleRpgMakerInitialized = (event) => {
          console.log('Canvas组件接收到RPG Maker引擎初始化完成事件', event.detail);

          // 获取RPG Maker API
          const { Graphics, Bitmap, Sprite } = event.detail;

          if (Graphics && Bitmap && Sprite) {
            // 移除事件监听器，避免重复处理
            document.removeEventListener('rpgmakerInitialized', handleRpgMakerInitialized);

            // 重新调用初始化方法
            setTimeout(() => {
              initializeRpgMaker();

              // 🎯 第一时间对外暴露初始化完成事件
              const initEvent = new CustomEvent('spriteEditorInitialized', {
                detail: {
                  timestamp: Date.now(),
                  message: 'Sprite Editor初始化完成',
                  apis: {
                    setExternalResourcePathForAccess: 'window.SpriteEditor.setExternalResourcePathForAccess',
                    setExternalSpriteForEdit: 'window.SpriteEditor.setExternalSpriteForEdit',
                    loadExternalImageFile: 'window.SpriteEditor.loadExternalImageFile',
                    loadExternalImageBuffer: 'window.SpriteEditor.loadExternalImageBuffer',
                    saveCurrentSprite: 'window.SpriteEditor.saveCurrentSprite'
                  }
                }
              });
              document.dispatchEvent(initEvent);
              console.log('已触发spriteEditorInitialized事件');
            }, 100);
          }
        };

        // 添加事件监听器
        document.addEventListener('rpgmakerInitialized', handleRpgMakerInitialized);
      }
    };

    // 调用初始化方法
    initializeRpgMaker();

    return () => {
      // 清理函数 - 不需要移除特定的事件监听器，因为它们在各自的作用域内已经处理了
      // 如果有其他需要清理的资源，可以在这里添加
    };
  }, []); // 空依赖数组，确保只执行一次

  // 添加和移除事件监听器
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // 添加鼠标事件监听器
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('mouseleave', handleMouseUp);

    // 添加窗口大小变化的监听器，确保canvas始终居中
    const handleResize = () => {
      // 在这里可以添加调整canvas大小或位置的逻辑
    };

    window.addEventListener('resize', handleResize);

    return () => {
      // 移除鼠标事件监听器
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseup', handleMouseUp);
      canvas.removeEventListener('mouseleave', handleMouseUp);

      // 移除窗口大小变化的监听器
      window.removeEventListener('resize', handleResize);

      // 取消任何待处理的 requestAnimationFrame
      if (requestAnimationFrameIdRef.current) {
        cancelAnimationFrame(requestAnimationFrameIdRef.current);
        requestAnimationFrameIdRef.current = null;
      }
    };
  }, [handleMouseDown, handleMouseMove, handleMouseUp]); // 依赖于鼠标事件处理函数

  return (
    <div
      ref={containerRef}
      className="canvas-wrapper"
      style={{
        width: '100%',
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
        backgroundColor: '#CCCCCC'
      }}
    />
  );
});

export default Canvas;
