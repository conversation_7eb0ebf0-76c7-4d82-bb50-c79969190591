import { type BaseDisplayProperties } from './type';

/**
 * 基类序列化器 - 处理BaseDisplayProperties属性
 */
export class BaseObjectSerializer {
  /**
   * 序列化对象为BaseDisplayProperties
   */
  serialize(obj: any): BaseDisplayProperties {
    return {
      className: obj.constructor.name,
      name: obj.name || '',
      // 位置和变换
      x: obj.x || 0,
      y: obj.y || 0,
      scaleX: obj.scale?.x || 1,
      scaleY: obj.scale?.y || 1,
      skewX: obj.skew?.x || 0,
      skewY: obj.skew?.y || 0,
      rotation: obj.rotation || 0,
      width: obj.width || 0,
      height: obj.height || 0,
      // 显示属性
      alpha: obj.alpha !== undefined ? obj.alpha : 1,
      visible: obj.visible !== undefined ? obj.visible : true,
      // 锚点和轴心
      anchorX: obj.anchor?.x || 0,
      anchorY: obj.anchor?.y || 0,
      pivotX: obj.pivot?.x || 0,
      pivotY: obj.pivot?.y || 0,
    };
  }

  /**
   * 反序列化BaseDisplayProperties为对象
   */
  deserialize(data: BaseDisplayProperties): any {
    // 根据className创建对象实例
    const obj = this.createObject(data.className);

    // 设置基础属性
    obj.name = data.name;
    obj.x = data.x;
    obj.y = data.y;

    // 设置scale
    if (!obj.scale) obj.scale = { x: 1, y: 1 };
    obj.scale.x = data.scaleX;
    obj.scale.y = data.scaleY;

    // 设置skew
    if (!obj.skew) obj.skew = { x: 0, y: 0 };
    obj.skew.x = data.skewX;
    obj.skew.y = data.skewY;

    obj.rotation = data.rotation;
    obj.width = data.width;
    obj.height = data.height;
    obj.alpha = data.alpha;
    obj.visible = data.visible;

    // 设置anchor
    if (!obj.anchor) obj.anchor = { x: 0, y: 0 };
    obj.anchor.x = data.anchorX;
    obj.anchor.y = data.anchorY;

    // 设置pivot
    if (!obj.pivot) obj.pivot = { x: 0, y: 0 };
    obj.pivot.x = data.pivotX;
    obj.pivot.y = data.pivotY;

    return obj;
  }



  /**
   * 根据类名创建RPG Maker MZ对象实例
   */
  protected createObject(className: string): any {
    switch (className) {
      case 'Sprite':
        return this.createRPGSprite();
      case 'Container':
        return this.createRPGContainer();
      case 'WindowLayer':
        return this.createRPGWindowLayer();
      default:
        return this.createRPGDisplayObject(className);
    }
  }

  /**
   * 创建RPG Maker MZ的Sprite对象
   */
  protected createRPGSprite(): any {
    // 检查是否在RPG Maker MZ环境中
    if (typeof window !== 'undefined' && (window as any).Sprite) {
      return new (window as any).Sprite();
    }

    // 模拟环境下返回基础结构
    return this.createMockSprite();
  }

  /**
   * 创建RPG Maker MZ的Container对象
   */
  protected createRPGContainer(): any {
    // 检查是否在RPG Maker MZ环境中
    if (typeof window !== 'undefined' && (window as any).PIXI && (window as any).PIXI.Container) {
      return new (window as any).PIXI.Container();
    }

    // 模拟环境下返回基础结构
    return this.createMockContainer();
  }

  /**
   * 创建RPG Maker MZ的WindowLayer对象
   */
  protected createRPGWindowLayer(): any {
    // 检查是否在RPG Maker MZ环境中
    if (typeof window !== 'undefined' && (window as any).WindowLayer) {
      return new (window as any).WindowLayer();
    }

    // 模拟环境下返回基础结构
    return this.createMockContainer();
  }

  /**
   * 创建其他RPG Maker MZ显示对象
   */
  protected createRPGDisplayObject(className: string): any {
    // 检查是否在RPG Maker MZ环境中
    if (typeof window !== 'undefined' && (window as any)[className]) {
      return new (window as any)[className]();
    }

    // 模拟环境下返回基础结构
    return this.createMockDisplayObject(className);
  }

  // 模拟对象创建方法（用于测试环境）
  protected createMockDisplayObject(className: string): any {
    return {
      constructor: { name: className },
      x: 0, y: 0,
      scale: { x: 1, y: 1 },
      skew: { x: 0, y: 0 },
      rotation: 0,
      width: 0, height: 0,
      alpha: 1, visible: true,
      anchor: { x: 0, y: 0 },
      pivot: { x: 0, y: 0 },
      name: ''
    };
  }

  protected createMockContainer(): any {
    const obj = this.createMockDisplayObject('Container');
    obj.children = [];
    return obj;
  }

  protected createMockSprite(): any {
    const obj = this.createMockDisplayObject('Sprite');
    obj.blendMode = 0;
    obj.zIndex = 0;
    obj.bitmap = null;
    obj._bitmap = null;
    obj._frame = { x: 0, y: 0, width: 0, height: 0 };
    obj._hue = 0;
    obj._blendColor = [0, 0, 0, 0];
    obj._colorTone = [0, 0, 0, 0];
    obj._hidden = false;
    return obj;
  }

  // ===== 代码生成方法 =====

  /**
   * 生成基础对象的完整创建代码
   * @param path 对象路径
   * @param properties 对象属性
   * @param indent 缩进字符串
   * @returns 生成的代码字符串
   */
  generateObjectCode(path: string, properties: BaseDisplayProperties, indent: string): string {
    const varName = `obj_${path.replace(/\./g, '_')}`;
    const codes: string[] = [];

    // 1. 对象创建
    codes.push(this.generateObjectCreation(properties.className, varName, properties, indent));

    // 2. 基础属性设置
    codes.push(this.generateBasicProperties(varName, properties, indent));

    // 3. 添加到父容器（传入路径用于判断父对象）
    codes.push(this.generateAddToParent(varName, path, indent));

    return codes.filter(code => code.trim()).join('\n');
  }

  /**
   * 生成对象创建代码
   */
  protected generateObjectCreation(className: string, varName: string, properties: BaseDisplayProperties, indent: string): string {
    if (className.startsWith('Window_')) {
      return `${indent}const ${varName} = new ${className}(new Rectangle(${properties.x}, ${properties.y}, ${properties.width}, ${properties.height}));`;
    }

    return `${indent}const ${varName} = new ${className}();`;
  }

  /**
   * 生成基础属性设置代码
   */
  protected generateBasicProperties(varName: string, properties: BaseDisplayProperties, indent: string): string {
    const codes: string[] = [];

    // 位置属性
    if (properties.x !== undefined) codes.push(`${indent}${varName}.x = ${properties.x};`);
    if (properties.y !== undefined) codes.push(`${indent}${varName}.y = ${properties.y};`);

    // 尺寸属性
    if (properties.width !== undefined) codes.push(`${indent}${varName}.width = ${properties.width};`);
    if (properties.height !== undefined) codes.push(`${indent}${varName}.height = ${properties.height};`);

    // 锚点属性
    if (properties.anchorX !== undefined) codes.push(`${indent}${varName}.anchor.x = ${properties.anchorX};`);
    if (properties.anchorY !== undefined) codes.push(`${indent}${varName}.anchor.y = ${properties.anchorY};`);

    // 轴心属性
    if (properties.pivotX !== undefined) codes.push(`${indent}${varName}.pivot.x = ${properties.pivotX};`);
    if (properties.pivotY !== undefined) codes.push(`${indent}${varName}.pivot.y = ${properties.pivotY};`);

    // 缩放属性
    if (properties.scaleX !== undefined) codes.push(`${indent}${varName}.scale.x = ${properties.scaleX};`);
    if (properties.scaleY !== undefined) codes.push(`${indent}${varName}.scale.y = ${properties.scaleY};`);

    // 倾斜属性
    if (properties.skewX !== undefined) codes.push(`${indent}${varName}.skew.x = ${properties.skewX};`);
    if (properties.skewY !== undefined) codes.push(`${indent}${varName}.skew.y = ${properties.skewY};`);

    // 其他属性
    if (properties.rotation !== undefined) codes.push(`${indent}${varName}.rotation = ${properties.rotation};`);
    if (properties.alpha !== undefined) codes.push(`${indent}${varName}.alpha = ${properties.alpha};`);
    if (properties.visible !== undefined) codes.push(`${indent}${varName}.visible = ${properties.visible};`);

    return codes.join('\n');
  }

  /**
   * 生成添加到父容器的代码
   * 根据路径判断添加到哪个父对象
   */
  protected generateAddToParent(varName: string, path: string, indent: string): string {
    // 解析路径，确定父对象
    const pathParts = path.split('.');

    if (pathParts.length === 1) {
      // 根级对象，添加到 this
      return `${indent}this.addChild(${varName});`;
    } else {
      // 嵌套对象，添加到父对象
      const parentPath = pathParts.slice(0, -1).join('_');
      const parentVarName = `obj_${parentPath}`;
      return `${indent}${parentVarName}.addChild(${varName});`;
    }
  }
}
