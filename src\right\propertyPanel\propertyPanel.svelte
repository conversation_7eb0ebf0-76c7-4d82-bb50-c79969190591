<script lang="ts">
  import { globalObjectState } from '../../stores/objectState';
  import BasePropertyPanel from './panels/basePropertyPanel.svelte';
  import ImagePropertyPanel from './panels/imagePropertyPanel.svelte';
  import LabelPropertyPanel from './panels/labelPropertyPanel.svelte';
  import type { spriteProperties } from '../../generators/type';

  // 监听选中的对象
  let selectedObject = $derived($globalObjectState.selectedObject);
  let selectedObjectType = $derived($globalObjectState.selectedObjectType);

  // 判断是否应该显示图片属性面板
  let shouldShowImagePanel = $derived(() => {
    if (!selectedObject || !Array.isArray(selectedObject) || selectedObject.length === 0) {
      return false;
    }

    const firstObject = selectedObject[0];
    const firstObjectType = Array.isArray(selectedObjectType) ? selectedObjectType[0] : selectedObjectType;

    // 检查是否为Sprite对象
    if (firstObjectType !== 'Sprite') {
      return false;
    }

    // 检查是否有bitmap和url
    const spriteData = firstObject as spriteProperties;
    const hasValidUrl = spriteData?.bitmap?.url && spriteData.bitmap.url.trim() !== '';

    return hasValidUrl;
  });

  // 判断是否应该显示标签属性面板
  let shouldShowLabelPanel = $derived(() => {
    if (!selectedObject || !Array.isArray(selectedObject) || selectedObject.length === 0) {
      return false;
    }

    const firstObject = selectedObject[0];
    const firstObjectType = Array.isArray(selectedObjectType) ? selectedObjectType[0] : selectedObjectType;

    // 检查是否为Sprite对象
    if (firstObjectType !== 'Sprite') {
      return false;
    }

    // 检查是否有bitmap和elements
    const spriteData = firstObject as spriteProperties;
    const hasElements = spriteData?.bitmap?.elements &&
                       Array.isArray(spriteData.bitmap.elements) &&
                       spriteData.bitmap.elements.length > 0;

    return hasElements;
  });

  // 获取对象类型的简短显示名称
  function getShortTypeName(typeNameOrArray: string | string[] | null): string {
    if (!typeNameOrArray) return '未知';

    // 如果是数组，取第一个元素
    const typeName = Array.isArray(typeNameOrArray) ? typeNameOrArray[0] : typeNameOrArray;

    if (!typeName) return '未知';

    // 简化常见类型名称
    const typeMap: Record<string, string> = {
      'Sprite_Character': 'Sprite',
      'Game_Player': 'Player',
      'Game_Follower': 'Follower',
      'Game_Vehicle': 'Vehicle',
      'Game_Event': 'Event'
    };

    return typeMap[typeName] || typeName.replace(/^(Game_|Sprite_)/, '');
  }

  console.log('PropertyPanel 组件已加载');
</script>

<div class="property-panel">
  <!-- 紧凑头部 -->
  <div class="panel-header">
    <span class="panel-title">属性面板</span>

    {#if selectedObject}
      <div class="object-info">
        <span class="object-type-badge">
          {getShortTypeName(selectedObjectType)}
        </span>
      </div>
    {:else}
      <div class="no-object-indicator">
        <span class="indicator-dot"></span>
      </div>
    {/if}
  </div>

  <!-- 内容区域 -->
  <div class="panel-content">
    {#if selectedObject}
      <BasePropertyPanel />
      {#if shouldShowImagePanel()}
        <ImagePropertyPanel />
      {/if}
      {#if shouldShowLabelPanel()}
        <LabelPropertyPanel />
      {/if}
    {:else}
      <div class="empty-state">
        <div class="empty-content">
          <div class="empty-icon">🎯</div>
          <div class="empty-text">
            <span class="empty-title">未选中对象</span>
            <span class="empty-subtitle">点击左侧对象树选择</span>
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .property-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--theme-surface, #ffffff);
    border-left: 1px solid var(--theme-border, #e2e8f0);
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 8px;
    background: var(--theme-surface-light, #f8f9fa);
    border-bottom: 1px solid var(--theme-border, #e2e8f0);
    min-height: 32px;
  }

  .panel-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--theme-text, #1a202c);
  }

  .object-info {
    display: flex;
    align-items: center;
  }

  .object-type-badge {
    font-size: 9px;
    font-weight: 500;
    color: var(--theme-primary, #3182ce);
    background: var(--theme-primary-light, rgba(49, 130, 206, 0.1));
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid var(--theme-primary-light, rgba(49, 130, 206, 0.2));
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .no-object-indicator {
    display: flex;
    align-items: center;
  }

  .indicator-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--theme-text-muted, #cbd5e0);
    opacity: 0.5;
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 4px;
    min-height: 0;
    max-height: calc(100vh - var(--title-bar-height, 32px) - var(--status-bar-height, 24px) - 100px);
  }

  /* 自定义滚动条样式 */
  .panel-content::-webkit-scrollbar {
    width: 6px;
  }

  .panel-content::-webkit-scrollbar-track {
    background: var(--theme-surface-dark, #1a202c);
    border-radius: 3px;
  }

  .panel-content::-webkit-scrollbar-thumb {
    background: var(--theme-border, rgba(255, 255, 255, 0.2));
    border-radius: 3px;
    transition: background-color var(--transition-fast, 0.15s ease);
  }

  .panel-content::-webkit-scrollbar-thumb:hover {
    background: var(--theme-border-dark, rgba(255, 255, 255, 0.3));
  }

  .empty-state {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 8px;
  }

  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
  }

  .empty-icon {
    font-size: 24px;
    opacity: 0.3;
  }

  .empty-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .empty-title {
    font-size: 11px;
    font-weight: 600;
    color: var(--theme-text, #1a202c);
  }

  .empty-subtitle {
    font-size: 9px;
    color: var(--theme-text-secondary, #718096);
    line-height: 1.3;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .panel-header {
      padding: 4px 6px;
      min-height: 28px;
    }

    .panel-title {
      font-size: 11px;
    }

    .object-type-badge {
      font-size: 8px;
      padding: 1px 4px;
    }

    .empty-icon {
      font-size: 20px;
    }

    .empty-title {
      font-size: 10px;
    }

    .empty-subtitle {
      font-size: 8px;
    }
  }
</style>