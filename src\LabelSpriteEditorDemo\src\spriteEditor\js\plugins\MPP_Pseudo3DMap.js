//=============================================================================
// MPP_Pseudo3DMap.js
//=============================================================================
// Copyright (c) 2023 Mokusei Penguin
// Released under the MIT license
// http://opensource.org/licenses/mit-license.php
//=============================================================================

/*:
 * @target MZ
 * @plugindesc 为地图添加伪3D效果，实现镜头拉伸距离效果。
 * <AUTHOR> Penguin
 * @url
 *
 * @help [version 1.0.0]
 * 此插件用于RPG Maker MZ。
 *
 * ▼ 插件功能
 *  - 为地图场景添加伪3D效果
 *  - 实现基础的镜头拉伸距离效果
 *
 * ================================
 *
 */

(() => {
  "use strict";

  //-------------------------------------------------------------------------
  // Spriteset_Map

  const _Spriteset_Map_createLowerLayer =
    Spriteset_Map.prototype.createLowerLayer;
  Spriteset_Map.prototype.createLowerLayer = function () {
    _Spriteset_Map_createLowerLayer.call(this);
    this.createPseudo3dContainer();
  };

  Spriteset_Map.prototype.createPseudo3dContainer = function () {
    // 设置_baseSprite作为3D容器
    this._baseSprite.pivot.x = Graphics.width / 2;
    this._baseSprite.pivot.y = Graphics.height / 2;
    this._baseSprite.x = Graphics.width / 2;
    this._baseSprite.y = Graphics.height / 2;

    // 确保所有精灵在tilemap之上
    for (const sprite of this._characterSprites) {
      sprite.z = 1;
      // 更新精灵的位置计算
      sprite.x = sprite.x - this._baseSprite.pivot.x;
      sprite.y = sprite.y - this._baseSprite.pivot.y;
      // 确保精灵可见
      sprite.visible = true;
    }

    // 启用容器的排序功能
    this._baseSprite.sortableChildren = true;
  };

  const _Spriteset_Map_update = Spriteset_Map.prototype.update;
  Spriteset_Map.prototype.update = function () {
    _Spriteset_Map_update.call(this);
    this.updatePseudo3dContainer();
  };

  Spriteset_Map.prototype.updatePseudo3dContainer = function () {
    // 创建变换矩阵
    const matrix = new PIXI.Matrix();

    // 设置基础缩放和倾斜参数
    const scaleY = 2.5; // Y轴缩放（增强纵深感但不过分）
    const skewY = 0.6; // Y轴倾斜（适度的透视效果）
    const skewX = 0.15; // X轴倾斜（轻微的横向透视感）

    // 设置矩阵参数
    matrix.a = 1.1; // X轴缩放（轻微拉伸）
    matrix.b = skewX; // X轴倾斜
    matrix.c = skewY; // Y轴倾斜
    matrix.d = scaleY; // Y轴缩放
    matrix.tx = 0; // X轴平移
    matrix.ty = -Graphics.height * 0.25; // Y轴平移（适度上移以优化视角）

    // 应用变换矩阵到地图容器
    this._baseSprite.transform.localTransform = matrix;
    this._baseSprite.transform.onChange();
  };
})();
