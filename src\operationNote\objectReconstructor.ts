/**
 * 对象重建器
 * 负责提取对象的必须属性并生成重建代码
 */

import { getEssentialProperties, needsSpecialHandling } from './essentialProperties';

/**
 * 对象数据接口
 */
export interface ObjectData {
  className: string;
  properties: Record<string, any>;
  constructorParams?: any;
  referenceName?: string;  // 如 '_backSprite1', '_commandWindow'
  children: ObjectData[];
}

/**
 * 场景重建数据接口
 */
export interface SceneReconstructionData {
  sceneClassName: string;
  objects: ObjectData[];
  metadata: {
    timestamp: number;
    version: string;
    editorVersion: string;
  };
}

/**
 * 对象重建器类
 */
export class ObjectReconstructor {
  // 存储需要重写的窗口类型
  private static windowTypesToRewrite: Map<string, any> = new Map();

  /**
   * 检查是否是新的窗口类型
   */
  private static isNewWindowType(className: string): boolean {
    // 检查是否是窗口类型
    const isWindowType = className.startsWith('Window_') || className === 'Window';

    // 如果是窗口类型且还没有被标记为需要重写
    if (isWindowType && !this.windowTypesToRewrite.has(className)) {
      console.log(`检测到新的窗口类型: ${className}`);
      return true;
    }

    return false;
  }

  /**
   * 提取完整的窗口结构
   */
  private static extractCompleteWindowStructure(windowObj: any): any {
    console.log(`=== 开始提取 ${windowObj.constructor.name} 的完整结构 ===`);

    const structure = {
      className: windowObj.constructor.name,
      properties: this.extractEssentialProperties(windowObj),
      children: this.extractAllChildNodes(windowObj),
      metadata: {
        childCount: windowObj.children ? windowObj.children.length : 0,
        extractTime: Date.now()
      }
    };

    console.log(`提取完成，子节点数量: ${structure.children.length}`);
    console.log('窗口结构:', structure);

    return structure;
  }

  /**
   * 提取所有子节点（递归）
   */
  private static extractAllChildNodes(obj: any): ObjectData[] {
    const result: ObjectData[] = [];

    if (!obj || !obj.children) {
      return result;
    }

    console.log(`提取 ${obj.constructor.name} 的 ${obj.children.length} 个子节点`);

    for (let i = 0; i < obj.children.length; i++) {
      const child = obj.children[i];
      if (!child || !child.constructor) continue;

      const className = child.constructor.name;
      const properties = this.extractEssentialProperties(child);
      const constructorParams = this.getConstructorParams(child);
      const referenceName = this.getReferenceName(obj, child, i);

      console.log(`  - 子节点 ${i}: ${className} ${referenceName ? `(${referenceName})` : ''}`);

      const objectData: ObjectData = {
        className,
        properties,
        constructorParams,
        referenceName,
        children: this.extractAllChildNodes(child) // 递归提取
      };

      result.push(objectData);
    }

    return result;
  }

  /**
   * 标记窗口类型需要重写
   */
  private static markWindowTypeForRewrite(className: string, windowData: any): void {
    console.log(`=== 标记 ${className} 需要重写 ===`);
    this.windowTypesToRewrite.set(className, windowData);
    console.log(`当前需要重写的窗口类型数量: ${this.windowTypesToRewrite.size}`);
  }

  /**
   * 获取需要重写的窗口类型
   */
  static getWindowTypesToRewrite(): Map<string, any> {
    return this.windowTypesToRewrite;
  }

  /**
   * 清空窗口重写标记
   */
  static clearWindowRewriteMarks(): void {
    this.windowTypesToRewrite.clear();
  }

  /**
   * 从实际对象提取必须属性
   * @param obj 要提取的对象
   * @returns 提取的属性数据
   */
  static extractEssentialProperties(obj: any): Record<string, any> {
    if (!obj || !obj.constructor) {
      return {};
    }

    const className = obj.constructor.name;
    const essentialProps = getEssentialProperties(className);
    const extractedProps: Record<string, any> = {};

    console.log(`提取 ${className} 的必须属性:`, essentialProps);

    for (const propName of essentialProps) {
      try {
        const value = this.getPropertyValue(obj, propName);
        if (value !== undefined && value !== null) {
          extractedProps[propName] = this.processPropertyValue(propName, value);
        }
      } catch (error) {
        console.warn(`提取属性 ${propName} 失败:`, error);
      }
    }

    // 特殊处理：检查 Sprite 对象的 bitmap 是否有 elements 数组（RPGEditor_BitmapTracker 插件）
    if (className === 'Sprite' && obj._bitmap && obj._bitmap.elements && Array.isArray(obj._bitmap.elements)) {
      console.log(`发现 Sprite 的 bitmap 包含 elements 数组，长度: ${obj._bitmap.elements.length}`);

      // 处理 elements 数组，特别是图片元素的 source
      const processedElements = obj._bitmap.elements.map((element: any, index: number) => {
        if (element.type === 'image' && element.source) {
          console.log(`处理图片元素 ${index}:`, element);

          // 提取图片的 URL，不保存整个 bitmap 对象
          const imageUrl = this.extractBitmapInfo(element.source);

          return {
            ...element,
            source: imageUrl // 只保存 URL 字符串
          };
        }

        // 文字元素或其他类型直接保存
        return element;
      });

      extractedProps.elements = processedElements;
      console.log(`处理后的 elements 数组:`, processedElements);

      // 同时保存 bitmap 的文字相关属性
      const bitmapTextProps = this.extractBitmapTextProperties(obj._bitmap);
      Object.assign(extractedProps, bitmapTextProps);
      console.log(`提取的 bitmap 文字属性:`, bitmapTextProps);
    }

    return extractedProps;
  }

  /**
   * 获取对象属性值，处理特殊的属性访问方式
   */
  private static getPropertyValue(obj: any, propName: string): any {
    switch (propName) {
      // PIXI 对象的特殊属性访问
      case 'scaleX':
        return obj.scale?.x;
      case 'scaleY':
        return obj.scale?.y;
      case 'skewX':
        return obj.skew?.x;
      case 'skewY':
        return obj.skew?.y;
      case 'anchorX':
        return obj.anchor?.x;
      case 'anchorY':
        return obj.anchor?.y;
      case 'pivotX':
        return obj.pivot?.x;
      case 'pivotY':
        return obj.pivot?.y;

      // Sprite 特殊属性
      case 'frameX':
        return obj._frame?.x;
      case 'frameY':
        return obj._frame?.y;
      case 'frameWidth':
        return obj._frame?.width;
      case 'frameHeight':
        return obj._frame?.height;
      case 'blendColor':
        return obj._blendColor?.slice(); // 复制数组
      case 'colorTone':
        return obj._colorTone?.slice(); // 复制数组
      case 'hue':
        return obj._hue;
      case 'hidden':
        return obj._hidden;
      case 'bitmap':
        return this.extractBitmapInfo(obj._bitmap);
      case 'elements':
        // RPGEditor_BitmapTracker 插件的 elements 数组
        return obj.elements;

      // Window 特殊属性
      case 'cursorRectX':
        return obj._cursorRect?.x;
      case 'cursorRectY':
        return obj._cursorRect?.y;
      case 'cursorRectWidth':
        return obj._cursorRect?.width;
      case 'cursorRectHeight':
        return obj._cursorRect?.height;
      case 'originX':
        return obj.origin?.x;
      case 'originY':
        return obj.origin?.y;
      case 'colorTone':
        return obj._colorTone?.slice(); // 复制数组
      case 'windowskin':
        return this.extractBitmapInfo(obj._windowskin);
      case 'contents':
        return this.extractBitmapInfo(obj.contents);
      case 'contentsBack':
        return this.extractBitmapInfo(obj.contentsBack);
      case 'opening':
        return obj._opening;
      case 'closing':
        return obj._closing;

      // 场景特殊属性
      case 'started':
        return obj._started;
      case 'fadeSign':
        return obj._fadeSign;
      case 'fadeDuration':
        return obj._fadeDuration;
      case 'fadeWhite':
        return obj._fadeWhite;
      case 'fadeOpacity':
        return obj._fadeOpacity;

      // 默认直接访问（安全版本）
      default:
        const value = obj[propName];
        // 确保返回的值是可序列化的
        return this.sanitizeValue(value);
    }
  }

  /**
   * 清理值，确保可序列化（避免循环引用）
   */
  private static sanitizeValue(value: any): any {
    if (value === null || value === undefined) {
      return value;
    }

    // 基础类型直接返回
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      return value;
    }

    // 数组处理
    if (Array.isArray(value)) {
      return value.map(item => this.sanitizeValue(item));
    }

    // 对象处理
    if (typeof value === 'object') {
      // 检查是否是 PIXI 对象或其他可能包含循环引用的对象
      if (value.constructor && (
        value.constructor.name.startsWith('PIXI') ||
        value.constructor.name.includes('Texture') ||
        value.constructor.name.includes('Canvas') ||
        value.constructor.name.includes('WebGL') ||
        value.constructor.name.includes('Bitmap')
      )) {
        // 对于可能包含循环引用的对象，返回安全的表示
        return `[${value.constructor.name}]`;
      }

      // 对于普通对象，递归清理属性
      try {
        const cleaned: any = {};
        for (const key in value) {
          if (value.hasOwnProperty(key)) {
            cleaned[key] = this.sanitizeValue(value[key]);
          }
        }
        return cleaned;
      } catch (error) {
        // 如果处理失败，返回安全的表示
        return `[Object:${value.constructor?.name || 'Unknown'}]`;
      }
    }

    // 函数和其他类型返回安全的表示
    return `[${typeof value}]`;
  }

  /**
   * 处理属性值，进行必要的转换
   */
  private static processPropertyValue(propName: string, value: any): any {
    if (needsSpecialHandling(propName)) {
      switch (propName) {
        case 'bitmap':
        case 'windowskin':
        case 'contents':
        case 'contentsBack':
          // 图片相关属性，返回路径或特殊标识
          return value;

        case 'blendColor':
        case 'colorTone':
          // 数组属性，确保是数组
          return Array.isArray(value) ? value : [0, 0, 0, 0];

        case 'mapData':
          // 大数据，可能需要压缩或特殊处理
          return Array.isArray(value) ? value : null;

        default:
          return value;
      }
    }

    return value;
  }

  /**
   * 提取位图信息
   */
  private static extractBitmapInfo(bitmap: any): string | null {
    if (!bitmap) return null;

    // 优先使用原始路径（CustomResourcePath 插件保存的）
    if (bitmap._originalPath) {
      console.log(`使用原始路径: ${bitmap._originalPath}`);
      return bitmap._originalPath;
    }

    // 如果是 Bitmap 对象，尝试获取 URL
    if (bitmap._url) {
      console.log(`使用 _url 路径: ${bitmap._url}`);
      return bitmap._url;
    }

    // 如果是字符串路径
    if (typeof bitmap === 'string') {
      return bitmap;
    }

    // 其他情况返回 null
    return null;
  }

  /**
   * 提取 Bitmap 对象的文字相关属性
   */
  private static extractBitmapTextProperties(bitmap: any): Record<string, any> {
    if (!bitmap) return {};

    const textProps: Record<string, any> = {};

    // 文字样式属性
    if (bitmap.fontBold !== undefined) textProps.fontBold = bitmap.fontBold;
    if (bitmap.fontFace !== undefined) textProps.fontFace = bitmap.fontFace;
    if (bitmap.fontItalic !== undefined) textProps.fontItalic = bitmap.fontItalic;
    if (bitmap.fontSize !== undefined) textProps.fontSize = bitmap.fontSize;
    if (bitmap.outlineColor !== undefined) textProps.outlineColor = bitmap.outlineColor;
    if (bitmap.outlineWidth !== undefined) textProps.outlineWidth = bitmap.outlineWidth;
    if (bitmap.textColor !== undefined) textProps.textColor = bitmap.textColor;

    // 其他 bitmap 属性
    if (bitmap._paintOpacity !== undefined) textProps._paintOpacity = bitmap._paintOpacity;
    if (bitmap._smooth !== undefined) textProps._smooth = bitmap._smooth;

    return textProps;
  }

  /**
   * 从对象树提取完整的场景数据
   * @param rootObject 根对象（通常是场景）
   * @returns 场景重建数据
   */
  static extractSceneData(rootObject: any): SceneReconstructionData {
    const sceneClassName = rootObject.constructor.name;
    const objects = this.extractObjectTree(rootObject);

    return {
      sceneClassName,
      objects,
      metadata: {
        timestamp: Date.now(),
        version: '1.0.0',
        editorVersion: '1.0.0'
      }
    };
  }

  /**
   * 递归提取对象树
   */
  private static extractObjectTree(obj: any, referenceName?: string): ObjectData[] {
    const result: ObjectData[] = [];

    if (!obj || !obj.children) {
      return result;
    }

    // 遍历所有子对象
    for (let i = 0; i < obj.children.length; i++) {
      const child = obj.children[i];
      if (!child || !child.constructor) continue;

      const className = child.constructor.name;

      // 检查是否是新的窗口类型
      if (this.isNewWindowType(className)) {
        console.log(`=== 发现新的窗口类型: ${className} ===`);
        console.log('提取当前节点的所有子节点...');

        // 提取当前窗口节点的完整结构
        const windowData = this.extractCompleteWindowStructure(child);

        // 标记需要重写这个窗口类型
        this.markWindowTypeForRewrite(className, windowData);

        // 继续处理这个窗口对象
        const properties = this.extractEssentialProperties(child);
        const constructorParams = this.getConstructorParams(child);
        const childReferenceName = this.getReferenceName(obj, child, i);

        const objectData: ObjectData = {
          className,
          properties,
          constructorParams,
          referenceName: childReferenceName,
          children: windowData.children // 使用提取的完整结构
        };

        result.push(objectData);
      } else {
        // 普通对象的处理
        const properties = this.extractEssentialProperties(child);
        const constructorParams = this.getConstructorParams(child);
        const childReferenceName = this.getReferenceName(obj, child, i);

        const objectData: ObjectData = {
          className,
          properties,
          constructorParams,
          referenceName: childReferenceName,
          children: this.extractObjectTree(child)
        };

        result.push(objectData);
      }
    }

    return result;
  }

  /**
   * 获取构造函数参数
   */
  private static getConstructorParams(obj: any): any {
    const className = obj.constructor.name;

    switch (className) {
      case 'Window_Base':
      case 'Window_TitleCommand':
      case 'Window_MenuCommand':
        // 窗口对象需要 Rectangle 参数
        return {
          rect: {
            x: obj.x || 0,
            y: obj.y || 0,
            width: obj.width || 0,
            height: obj.height || 0
          }
        };

      case 'Sprite':
        // 精灵可能需要初始位图
        const bitmap = this.extractBitmapInfo(obj._bitmap);
        return bitmap ? { bitmap } : {};

      default:
        return {};
    }
  }

  /**
   * 获取对象的引用名称
   */
  private static getReferenceName(parent: any, child: any, index: number): string | undefined {
    // 检查父对象是否有对这个子对象的命名引用
    for (const key in parent) {
      if (parent[key] === child && key.startsWith('_')) {
        return key;
      }
    }

    // 如果没有找到命名引用，返回 undefined
    return undefined;
  }

  /**
   * 生成对象重建代码
   * @param sceneData 场景数据
   * @returns 生成的 JavaScript 代码
   */
  static generateReconstructionCode(sceneData: SceneReconstructionData): string {
    const { sceneClassName, objects } = sceneData;

    let code = `// 场景重建代码 - ${sceneClassName}\n`;
    code += `// 基于编辑器对象结构生成\n`;
    code += `// 生成时间: ${new Date().toISOString()}\n\n`;

    code += `(() => {\n`;
    code += `    'use strict';\n\n`;

    // 首先生成所有需要重写的窗口类型
    const windowTypes = this.getWindowTypesToRewrite();
    if (windowTypes.size > 0) {
      for (const [className, windowData] of windowTypes) {
        code += this.generateWindowClassRewrite(className, windowData);
      }
    }

    // 然后生成场景重写代码
    code += `    // === 重写 ${sceneClassName} 类 ===\n\n`;
    code += `    // 重写 ${sceneClassName}.prototype.create\n`;
    code += `    ${sceneClassName}.prototype.create = function() {\n`;
    code += `        Scene_Base.prototype.create.call(this);\n\n`;

    // 生成基础设施代码
    if (sceneClassName.includes('Scene')) {
      code += `        // 创建基础设施\n`;
      code += `        this.createWindowLayer();\n\n`;
    }

    code += `        // === 根据编辑器结构重建对象 ===\n`;

    // 生成每个对象的创建代码
    for (const objData of objects) {
      code += this.generateObjectCreationCode(objData, '        ');
    }

    code += `    };\n\n`;
    code += `})();\n`;

    return code;
  }

  /**
   * 生成窗口类重写代码
   */
  private static generateWindowClassRewrite(className: string, windowData: any): string {
    let code = `    // === 重写 ${className} 类 ===\n\n`;

    code += `    // 统一重建模式 - ${className}.prototype.initialize\n`;
    code += `    ${className}.prototype.initialize = function(rect) {\n`;
    code += `        // 只调用 PIXI.Container 基类\n`;
    code += `        PIXI.Container.call(this);\n`;
    code += `        \n`;
    code += `        // 设置必要的窗口属性（不设置 _isWindow，通过属性定义器处理）\n`;
    code += `        this._openness = 0;\n`;
    code += `        this._width = rect.width;\n`;
    code += `        this._height = rect.height;\n`;
    code += `        this._padding = 12;\n`;
    code += `        this._margin = 4;\n`;
    code += `        this._colorTone = [0, 0, 0, 0];\n`;
    code += `        this._cursorRect = new Rectangle();\n`;
    code += `        this._animationCount = 0;\n`;
    code += `        \n`;
    code += `        // 设置位置和显示属性\n`;
    code += `        this.x = rect.x || 0;\n`;
    code += `        this.y = rect.y || 0;\n`;
    code += `        this.visible = true;\n`;
    code += `        this.alpha = 1;\n`;
    code += `        \n`;

    // 根据窗口类型设置特定属性
    if (className.includes('Command')) {
      code += `        // Command 窗口属性\n`;
      code += `        this._list = [];\n`;
      code += `        this._index = -1;\n`;
      code += `        this._cursorFixed = false;\n`;
      code += `        this._cursorAll = false;\n`;
      code += `        this._helpWindow = null;\n`;
      code += `        this._handlers = {};\n`;
      code += `        \n`;
    }

    code += `        // 调试信息\n`;
    code += `        console.log('=== ${className} 重建完成 ===');\n`;
    code += `        console.log('窗口属性:', {\n`;
    code += `            className: '${className}',\n`;
    code += `            x: this.x, y: this.y,\n`;
    code += `            width: this._width, height: this._height,\n`;
    code += `            openness: this._openness,\n`;
    code += `            visible: this.visible\n`;
    code += `        });\n`;
    code += `        \n`;
    code += `        // 重建窗口内容\n`;
    code += `        this.rebuildContent();\n`;
    code += `    };\n\n`;

    // 生成内容重建方法
    code += this.generateWindowRebuildMethod(className, windowData);

    // 生成属性访问器
    code += this.generateWindowPropertyAccessors(className);

    return code;
  }

  /**
   * 生成窗口内容重建方法
   */
  private static generateWindowRebuildMethod(className: string, windowData: any): string {
    let code = `    // 重建窗口内容\n`;
    code += `    ${className}.prototype.rebuildContent = function() {\n`;
    code += `        console.log('=== 开始重建 ${className} 内容 ===');\n`;
    code += `        \n`;

    // 根据提取的窗口结构生成重建代码
    if (windowData.children && windowData.children.length > 0) {
      code += `        // 根据编辑器中的实际对象结构重建\n`;
      for (const child of windowData.children) {
        code += this.generateChildObjectCode(child, '        ');
      }
    } else {
      code += `        // 生成默认的窗口结构\n`;
      code += this.generateDefaultWindowStructure(className, '        ');
    }

    code += `        \n`;
    code += `        console.log('=== ${className} 内容重建完成 ===');\n`;
    code += `        console.log('子对象数量:', this.children.length);\n`;
    code += `    };\n\n`;

    return code;
  }

  /**
   * 生成子对象创建代码
   */
  private static generateChildObjectCode(childData: ObjectData, indent: string): string {
    const { className, properties, referenceName } = childData;

    let code = `${indent}// 创建 ${className}${referenceName ? ` (${referenceName})` : ''}\n`;

    // 生成变量名
    const varName = referenceName || `${className.toLowerCase()}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

    code += `${indent}const ${varName} = new ${className}();\n`;

    // 设置属性
    for (const [propName, value] of Object.entries(properties)) {
      code += this.generatePropertySetting(varName, propName, value, indent, properties);
    }

    code += `${indent}this.addChild(${varName});\n`;

    // 递归处理子对象
    if (childData.children && childData.children.length > 0) {
      for (const grandChild of childData.children) {
        const grandChildCode = this.generateChildObjectCode(grandChild, indent + '    ');
        // 将子对象添加到当前对象而不是根对象
        const modifiedCode = grandChildCode.replace(
          /this\.addChild\(/g,
          `${varName}.addChild(`
        );
        code += modifiedCode;
      }
    }

    code += `${indent}\n`;
    return code;
  }

  /**
   * 生成属性设置代码
   */
  private static generatePropertySetting(varName: string, propName: string, value: any, indent: string, allProperties?: Record<string, any>): string {
    // 处理特殊属性
    switch (propName) {
      case 'scaleX':
        return `${indent}${varName}.scale.x = ${value};\n`;
      case 'scaleY':
        return `${indent}${varName}.scale.y = ${value};\n`;
      case 'anchorX':
        return `${indent}${varName}.anchor.x = ${value};\n`;
      case 'anchorY':
        return `${indent}${varName}.anchor.y = ${value};\n`;
      case 'bitmap':
        if (value && typeof value === 'string') {
          // 解析路径
          const pathParts = value.split('/');
          if (pathParts.length >= 2) {
            const folder = pathParts[pathParts.length - 2];
            const filename = pathParts[pathParts.length - 1].replace(/\.[^/.]+$/, ""); // 移除扩展名
            return `${indent}${varName}.bitmap = ImageManager.loadBitmap('img/${folder}/', '${filename}');\n`;
          }
        }
        return '';
      case 'elements':
        // RPGEditor_BitmapTracker 插件的 elements 数组
        if (Array.isArray(value) && value.length > 0) {
          let code = `${indent}// 确保 bitmap 存在（RPGEditor_BitmapTracker 插件）\n`;
          code += `${indent}if (!${varName}.bitmap) {\n`;
          code += `${indent}    ${varName}.bitmap = new Bitmap(Graphics.width, Graphics.height);\n`;
          code += `${indent}}\n`;

          // 设置 bitmap 文字属性
          const textProps = ['fontBold', 'fontFace', 'fontItalic', 'fontSize', 'outlineColor', 'outlineWidth', 'textColor', '_paintOpacity', '_smooth'];
          for (const prop of textProps) {
            if (allProperties && allProperties[prop] !== undefined) {
              code += `${indent}${varName}.bitmap.${prop} = ${JSON.stringify(allProperties[prop])};\n`;
            }
          }

          code += `${indent}// 重建 elements 数组\n`;
          code += `${indent}${varName}.bitmap.elements = [];\n`;
          code += `${indent}${varName}.bitmap.elements = ${JSON.stringify(value, null, 12).replace(/\n/g, '\n' + indent)};\n`;
          code += `${indent}// 调用重绘方法\n`;
          code += `${indent}${varName}.bitmap.redrawing();\n`;
          return code;
        }
        return '';
      default:
        if (typeof value === 'string') {
          return `${indent}${varName}.${propName} = "${value}";\n`;
        } else {
          return `${indent}${varName}.${propName} = ${JSON.stringify(value)};\n`;
        }
    }
  }

  /**
   * 生成默认窗口结构
   */
  private static generateDefaultWindowStructure(className: string, indent: string): string {
    let code = `${indent}// 生成默认的 ${className} 结构\n`;
    code += `${indent}console.log('使用默认窗口结构');\n`;
    return code;
  }

  /**
   * 生成窗口属性访问器
   */
  private static generateWindowPropertyAccessors(className: string): string {
    let code = `    // 重写 ${className} 的属性访问器\n`;

    // _isWindow 属性
    code += `    Object.defineProperty(${className}.prototype, "_isWindow", {\n`;
    code += `        get: function() { return true; },\n`;
    code += `        configurable: true\n`;
    code += `    });\n\n`;

    // openness 属性
    code += `    Object.defineProperty(${className}.prototype, "openness", {\n`;
    code += `        get: function() { return this._openness || 255; },\n`;
    code += `        set: function(value) {\n`;
    code += `            this._openness = value;\n`;
    code += `            this.visible = value > 0;\n`;
    code += `            // 应用缩放效果\n`;
    code += `            if (this.children) {\n`;
    code += `                const scale = value / 255;\n`;
    code += `                for (const child of this.children) {\n`;
    code += `                    if (child.scale) child.scale.y = scale;\n`;
    code += `                }\n`;
    code += `            }\n`;
    code += `        },\n`;
    code += `        configurable: true\n`;
    code += `    });\n\n`;

    // 基本方法
    code += `    // 重写基本方法\n`;
    code += `    ${className}.prototype.isOpen = function() {\n`;
    code += `        return (this._openness || 255) >= 255;\n`;
    code += `    };\n\n`;

    code += `    ${className}.prototype.open = function() {\n`;
    code += `        this.openness = 255;\n`;
    code += `    };\n\n`;

    code += `    ${className}.prototype.close = function() {\n`;
    code += `        this.openness = 0;\n`;
    code += `    };\n\n`;

    // Command 窗口特有方法
    if (className.includes('Command')) {
      code += `    // Command 窗口特有方法\n`;
      code += `    ${className}.prototype.setHandler = function(symbol, method) {\n`;
      code += `        this._handlers = this._handlers || {};\n`;
      code += `        this._handlers[symbol] = method;\n`;
      code += `    };\n\n`;

      code += `    ${className}.prototype.setBackgroundType = function(type) {\n`;
      code += `        this._backgroundType = type;\n`;
      code += `    };\n\n`;
    }

    return code;
  }

  /**
   * 生成单个对象的创建代码
   */
  private static generateObjectCreationCode(objData: ObjectData, indent: string): string {
    const { className, properties, constructorParams, referenceName, children } = objData;

    let code = `${indent}// 创建 ${className}\n`;

    // 生成构造函数调用
    const varName = referenceName || `obj_${Date.now()}`;
    code += `${indent}${referenceName ? `this.${referenceName}` : `const ${varName}`} = `;

    if (constructorParams && Object.keys(constructorParams).length > 0) {
      code += `new ${className}(${this.generateConstructorArgs(constructorParams)});\n`;
    } else {
      code += `new ${className}();\n`;
    }

    // 设置属性
    for (const [propName, value] of Object.entries(properties)) {
      code += this.generatePropertyAssignment(varName, propName, value, indent);
    }

    // 添加到父容器
    if (!referenceName || !referenceName.startsWith('_')) {
      code += `${indent}this.addChild(${varName});\n`;
    } else {
      // 有引用名称的对象，使用特殊的添加方法
      if (className.startsWith('Window')) {
        code += `${indent}this.addWindow(${varName});\n`;
      } else {
        code += `${indent}this.addChild(${varName});\n`;
      }
    }

    // 递归生成子对象
    if (children && children.length > 0) {
      code += `${indent}// 子对象\n`;
      for (const childData of children) {
        code += this.generateObjectCreationCode(childData, indent + '    ');
      }
    }

    code += '\n';
    return code;
  }

  /**
   * 生成构造函数参数
   */
  private static generateConstructorArgs(params: any): string {
    if (params.rect) {
      const { x, y, width, height } = params.rect;
      return `new Rectangle(${x}, ${y}, ${width}, ${height})`;
    }

    if (params.bitmap) {
      return `ImageManager.loadBitmap('${params.bitmap}')`;
    }

    return '';
  }

  /**
   * 生成属性赋值代码
   */
  private static generatePropertyAssignment(varName: string, propName: string, value: any, indent: string): string {
    const objRef = varName.startsWith('this.') ? varName : `this.${varName}`;

    // 处理特殊属性
    switch (propName) {
      case 'scaleX':
        return `${indent}${objRef}.scale.x = ${value};\n`;
      case 'scaleY':
        return `${indent}${objRef}.scale.y = ${value};\n`;
      case 'anchorX':
        return `${indent}${objRef}.anchor.x = ${value};\n`;
      case 'anchorY':
        return `${indent}${objRef}.anchor.y = ${value};\n`;
      case 'blendColor':
      case 'colorTone':
        return `${indent}${objRef}.${propName} = ${JSON.stringify(value)};\n`;
      case 'bitmap':
        if (value) {
          return `${indent}${objRef}.bitmap = ImageManager.loadBitmap('${value}');\n`;
        }
        return '';
      default:
        return `${indent}${objRef}.${propName} = ${JSON.stringify(value)};\n`;
    }
  }
}
