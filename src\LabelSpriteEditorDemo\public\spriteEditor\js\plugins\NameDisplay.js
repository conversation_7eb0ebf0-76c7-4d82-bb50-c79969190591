//=============================================================================
// RPG Maker MZ - Name Display System
//=============================================================================

/*:
 * @target MZ
 * @plugindesc 角色头顶名字显示系统
 * <AUTHOR> AI
 *
 * @param fontSize
 * @text 字体大小
 * @type number
 * @min 10
 * @max 72
 * @default 16
 * @desc 名字显示的字体大小
 *
 * @param fontColor
 * @text 字体颜色
 * @type string
 * @default #ffffff
 * @desc 名字显示的字体颜色（CSS颜色格式）
 *
 * @param outlineColor
 * @text 描边颜色
 * @type string
 * @default #000000
 * @desc 名字显示的描边颜色（CSS颜色格式）
 *
 * @param outlineWidth
 * @text 描边宽度
 * @type number
 * @min 0
 * @max 8
 * @default 4
 * @desc 名字显示的描边宽度
 *
 * @param yOffset
 * @text Y轴偏移
 * @type number
 * @min -100
 * @max 100
 * @default -36
 * @desc 名字显示相对于角色的Y轴偏移量
 *
 * @param showPlayerNames
 * @text 显示玩家名字
 * @type boolean
 * @default true
 * @desc 是否显示玩家名字
 *
 * @param showNpcNames
 * @text 显示NPC名字
 * @type boolean
 * @default true
 * @desc 是否显示NPC名字
 *
 * @help NameDisplay.js
 *
 * 这个插件为角色添加头顶名字显示功能：
 * 1. 自动在角色头顶显示名字
 * 2. 支持自定义字体样式
 * 3. 支持描边效果
 * 4. 支持位置偏移
 */

(() => {
  const pluginName = "NameDisplay";
  const parameters = PluginManager.parameters(pluginName);

  const fontSize = Number(parameters.fontSize || 16);
  const fontColor = String(parameters.fontColor || "#ffffff");
  const outlineColor = String(parameters.outlineColor || "#000000");
  const outlineWidth = Number(parameters.outlineWidth || 4);
  const yOffset = Number(parameters.yOffset || -36);
  const showPlayerNames =
    String(parameters.showPlayerNames || "true").toLowerCase() === "true";
  const showNpcNames =
    String(parameters.showNpcNames || "true").toLowerCase() === "true";

  // 扩展Sprite_Character来添加名字显示
  const _Sprite_Character_initialize = Sprite_Character.prototype.initialize;
  Sprite_Character.prototype.initialize = function (character) {
    _Sprite_Character_initialize.call(this, character);
    if (this.shouldDisplayName()) {
      this.createNameSprite();
    }
  };

  Sprite_Character.prototype.shouldDisplayName = function () {
    if (!this._character) return false;

    // 检查是否为主玩家或子玩家
    if (
      showPlayerNames &&
      (this._character instanceof Game_Player ||
        (this._character.constructor &&
          this._character.constructor.prototype instanceof Game_Player &&
          this._character.constructor.name !== "Game_Player"))
    )
      return true;

    // 检查是否为带有NAMEPOP标记的事件
    if (
      showNpcNames &&
      this._character instanceof Game_Event &&
      this._character.event() &&
      this._character.event().note &&
      this._character.event().note.match("NAMEPOP")
    )
      return true;

    return false;
  };

  Sprite_Character.prototype.createNameSprite = function () {
    this._nameSprite = new Sprite();
    this._nameSprite.bitmap = new Bitmap(200, 48);
    this._nameSprite.bitmap.fontSize = fontSize;
    this._nameSprite.anchor.x = 0.5;
    this._nameSprite.anchor.y = 1;
    this._nameSprite.y = yOffset;
    this.addChild(this._nameSprite);
    this.updateNameDisplay();
  };

  Sprite_Character.prototype.updateNameDisplay = function () {
    const bitmap = this._nameSprite.bitmap;
    bitmap.clear();

    // 设置字体样式
    bitmap.fontFace = $gameSystem.mainFontFace();
    bitmap.fontSize = fontSize;
    bitmap.textColor = fontColor;
    bitmap.outlineColor = outlineColor;
    bitmap.outlineWidth = outlineWidth;

    // 获取显示名字
    let displayNames = [];
    if (!this._character) return;
    if (this._character.constructor.name === "Game_SubPlayer") {
      console.log("Game_SubPlayer detected name", this._character);
      displayNames.push(this._character._characterName || "SubPlayer");
    } else if (this._character instanceof Game_Player) {
      // 只显示当前玩家的名字
      const leader = $gameParty.leader();
      if (leader && leader.name()) {
        displayNames.push(leader.name());
      }
    } else if (this._character instanceof Game_Follower) {
      // 显示跟随者对应的角色名字
      const actor = this._character.actor();
      if (actor) {
        displayNames.push(actor.name());
      }
    } else if (
      this._character instanceof Game_Event &&
      this._character.event()
    ) {
      displayNames.push(this._character.event().name);
    }

    // 绘制名字
    const width = bitmap.width;
    const height = bitmap.height;
    const lineHeight = Math.floor(height / Math.max(displayNames.length, 1));

    displayNames.forEach((name, index) => {
      const y = index * lineHeight;
      bitmap.drawText(name, 0, y, width, lineHeight, "center");
    });
  };

  // 扩展更新函数来确保名字位置正确
  const _Sprite_Character_update = Sprite_Character.prototype.update;
  Sprite_Character.prototype.update = function () {
    _Sprite_Character_update.call(this);
    if (this._nameSprite) {
      this.updateNameDisplay();
    }
  };
})();
