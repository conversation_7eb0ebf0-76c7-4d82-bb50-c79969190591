/**
 * 系统窗口模板生成器
 * 基于RPG Maker MZ源码，直接调用系统的窗口创建方法
 */

/**
 * 生成系统窗口创建代码的主方法
 * @param windowType 窗口类型（如 'Window_TitleCommand'）
 * @param indent 缩进字符串（可选，默认为4个空格）
 * @returns 生成的窗口创建代码
 */
export function generateSystemWindowCode(
  windowType: string, 
  indent: string = '    '
): string {
  const template = getSystemWindowTemplate(windowType);
  if (template) {
    return template(indent);
  }
  
  // 如果没有找到对应的模板，返回注释
  return `${indent}// 未找到 ${windowType} 的系统创建方法\n`;
}

/**
 * 获取系统窗口模板函数
 */
function getSystemWindowTemplate(windowType: string): ((indent: string) => string) | null {
  const templates: Record<string, (indent: string) => string> = {
    'Window_TitleCommand': generateTitleCommandWindowCode,
    'Window_MenuCommand': generateMenuCommandWindowCode,
    'Window_Gold': generateGoldWindowCode,
    'Window_MenuStatus': generateMenuStatusWindowCode,
    'Window_Help': generateHelpWindowCode,
    'Window_ItemCategory': generateItemCategoryWindowCode,
    'Window_ItemList': generateItemListWindowCode,
    'Window_SkillType': generateSkillTypeWindowCode,
    'Window_SkillList': generateSkillListWindowCode,
    'Window_SkillStatus': generateSkillStatusWindowCode,
    'Window_Status': generateStatusWindowCode,
    'Window_Options': generateOptionsWindowCode,
    'Window_SavefileList': generateSavefileListWindowCode,
    'Window_Message': generateMessageWindowCode,
    'Window_ScrollText': generateScrollTextWindowCode,
    'Window_MapName': generateMapNameWindowCode,
    'Window_ChoiceList': generateChoiceListWindowCode,
    'Window_NumberInput': generateNumberInputWindowCode,
    'Window_EventItem': generateEventItemWindowCode,
    'Window_NameBox': generateNameBoxWindowCode
  };

  return templates[windowType] || null;
}

// ===== 具体窗口模板函数 =====

/**
 * Scene_Title 的命令窗口
 */
function generateTitleCommandWindowCode(indent: string): string {
  return `${indent}// 创建标题命令窗口（调用系统方法）\n` +
         `${indent}this.createCommandWindow();\n`;
}

/**
 * Scene_Menu 的命令窗口
 */
function generateMenuCommandWindowCode(indent: string): string {
  return `${indent}// 创建菜单命令窗口（调用系统方法）\n` +
         `${indent}this.createCommandWindow();\n`;
}

/**
 * Scene_Menu 的金钱窗口
 */
function generateGoldWindowCode(indent: string): string {
  return `${indent}// 创建金钱窗口（调用系统方法）\n` +
         `${indent}this.createGoldWindow();\n`;
}

/**
 * Scene_Menu 的状态窗口
 */
function generateMenuStatusWindowCode(indent: string): string {
  return `${indent}// 创建状态窗口（调用系统方法）\n` +
         `${indent}this.createStatusWindow();\n`;
}

/**
 * 帮助窗口
 */
function generateHelpWindowCode(indent: string): string {
  return `${indent}// 创建帮助窗口（调用系统方法）\n` +
         `${indent}this.createHelpWindow();\n`;
}

/**
 * 物品分类窗口
 */
function generateItemCategoryWindowCode(indent: string): string {
  return `${indent}// 创建物品分类窗口（调用系统方法）\n` +
         `${indent}this.createCategoryWindow();\n`;
}

/**
 * 物品列表窗口
 */
function generateItemListWindowCode(indent: string): string {
  return `${indent}// 创建物品列表窗口（调用系统方法）\n` +
         `${indent}this.createItemWindow();\n`;
}

/**
 * 技能类型窗口
 */
function generateSkillTypeWindowCode(indent: string): string {
  return `${indent}// 创建技能类型窗口（调用系统方法）\n` +
         `${indent}this.createSkillTypeWindow();\n`;
}

/**
 * 技能列表窗口
 */
function generateSkillListWindowCode(indent: string): string {
  return `${indent}// 创建技能列表窗口（调用系统方法）\n` +
         `${indent}this.createItemWindow();\n`;
}

/**
 * 技能状态窗口
 */
function generateSkillStatusWindowCode(indent: string): string {
  return `${indent}// 创建技能状态窗口（调用系统方法）\n` +
         `${indent}this.createStatusWindow();\n`;
}

/**
 * 状态窗口
 */
function generateStatusWindowCode(indent: string): string {
  return `${indent}// 创建状态窗口（调用系统方法）\n` +
         `${indent}this.createStatusWindow();\n`;
}

/**
 * 选项窗口
 */
function generateOptionsWindowCode(indent: string): string {
  return `${indent}// 创建选项窗口（调用系统方法）\n` +
         `${indent}this.createOptionsWindow();\n`;
}

/**
 * 存档列表窗口
 */
function generateSavefileListWindowCode(indent: string): string {
  return `${indent}// 创建存档列表窗口（调用系统方法）\n` +
         `${indent}this.createListWindow();\n`;
}

/**
 * 消息窗口
 */
function generateMessageWindowCode(indent: string): string {
  return `${indent}// 创建消息窗口（调用系统方法）\n` +
         `${indent}this.createMessageWindow();\n`;
}

/**
 * 滚动文本窗口
 */
function generateScrollTextWindowCode(indent: string): string {
  return `${indent}// 创建滚动文本窗口（调用系统方法）\n` +
         `${indent}this.createScrollTextWindow();\n`;
}

/**
 * 地图名称窗口
 */
function generateMapNameWindowCode(indent: string): string {
  return `${indent}// 创建地图名称窗口（调用系统方法）\n` +
         `${indent}this.createMapNameWindow();\n`;
}

/**
 * 选择列表窗口
 */
function generateChoiceListWindowCode(indent: string): string {
  return `${indent}// 创建选择列表窗口（调用系统方法）\n` +
         `${indent}this.createChoiceListWindow();\n`;
}

/**
 * 数字输入窗口
 */
function generateNumberInputWindowCode(indent: string): string {
  return `${indent}// 创建数字输入窗口（调用系统方法）\n` +
         `${indent}this.createNumberInputWindow();\n`;
}

/**
 * 事件物品窗口
 */
function generateEventItemWindowCode(indent: string): string {
  return `${indent}// 创建事件物品窗口（调用系统方法）\n` +
         `${indent}this.createEventItemWindow();\n`;
}

/**
 * 姓名框窗口
 */
function generateNameBoxWindowCode(indent: string): string {
  return `${indent}// 创建姓名框窗口（调用系统方法）\n` +
         `${indent}this.createNameBoxWindow();\n`;
}
