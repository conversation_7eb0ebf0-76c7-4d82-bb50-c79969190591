/**
 * Sprite 处理器主入口
 * 分层处理 Sprite 数据，生成代码模板
 */

import { processBasicProperties } from './basicProperties';
import { processElements } from './elements';
import { processUrl } from './url';
import { generateSpriteCreation } from './creation';

/**
 * Sprite 数据接口
 */
export interface SpriteData {
  className: string;
  referenceName?: string;
  properties: Record<string, any>;
  constructorParams?: any;
  children?: SpriteData[];
}

/**
 * 处理结果接口
 */
export interface SpriteProcessResult {
  creationCode: string;
  basicPropertiesCode: string;
  elementsCode: string;
  urlCode: string;
  addToParentCode: string;
  childrenCode: string;
}

/**
 * 主处理函数 - 对外暴露的唯一方法
 * @param spriteData Sprite 保存的数据
 * @param indent 缩进字符串（默认4个空格）
 * @param parentContext 父容器上下文（可选）
 * @returns 生成好的代码模板
 */
export function processSpriteData(
  spriteData: SpriteData,
  indent: string = '    ',
  parentContext?: 'windowLayer' | 'scene'
): string {
  console.log(`=== 开始处理 Sprite: ${spriteData.className} ===`);

  // 第一层：生成对象创建代码
  const creationResult = generateSpriteCreation(spriteData, indent);

  // 第二层：处理基础属性
  const basicPropertiesResult = processBasicProperties(
    spriteData.properties,
    creationResult.varName,
    indent
  );

  // 第三层：检查并处理 elements 数组或 url（互斥）
  let elementsResult = '';
  let urlResult = '';

  if (spriteData.properties.elements && Array.isArray(spriteData.properties.elements)) {
    console.log('检测到 elements 数组，开始处理');
    elementsResult = processElements(
      spriteData.properties.elements,
      spriteData.properties,
      creationResult.varName,
      indent
    );
  } else if (spriteData.properties.url || spriteData.constructorParams?.bitmap) {
    console.log('检测到 url 或 bitmap，开始处理');
    const urlSource = spriteData.properties.url || spriteData.constructorParams?.bitmap;
    urlResult = processUrl(urlSource, creationResult.varName, indent);
  }

  // 第四层：生成添加到父容器的代码
  const addToParentResult = generateAddToParentCode(
    spriteData.className,
    creationResult.varName,
    indent,
    parentContext
  );

  // 第五层：递归处理子对象
  let childrenResult = '';
  if (spriteData.children && spriteData.children.length > 0) {
    console.log(`处理 ${spriteData.children.length} 个子对象`);
    for (const child of spriteData.children) {
      childrenResult += processSpriteData(child, indent + '    ', parentContext);
    }
  }

  // 组装最终代码
  const finalCode = [
    creationResult.code,
    basicPropertiesResult,
    elementsResult,
    urlResult,
    addToParentResult,
    childrenResult
  ].filter(code => code.trim()).join('');

  console.log(`=== Sprite 处理完成: ${spriteData.className} ===`);
  return finalCode + '\n';
}

/**
 * 生成添加到父容器的代码
 */
function generateAddToParentCode(
  className: string,
  varName: string,
  indent: string,
  parentContext?: 'windowLayer' | 'scene'
): string {
  if (className.startsWith('Window_')) {
    return `${indent}this.addWindow(${varName});\n`;
  } else if (className === 'WindowLayer') {
    return `${indent}// WindowLayer 位置已保存，将在窗口创建前应用\n`;
  } else if (parentContext === 'windowLayer') {
    // 在 WindowLayer 中的 UI 对象应该添加到 WindowLayer
    return `${indent}this.addWindow(${varName});\n`;
  } else {
    // 默认添加到场景
    return `${indent}this.addChild(${varName});\n`;
  }
}

/**
 * 批量处理多个 Sprite 对象
 * @param sprites Sprite 数据数组
 * @param indent 缩进字符串
 * @param parentContext 父容器上下文（可选）
 * @returns 生成的代码字符串
 */
export function processMultipleSpriteData(
  sprites: SpriteData[],
  indent: string = '    ',
  parentContext?: 'windowLayer' | 'scene'
): string {
  let code = '';

  for (const sprite of sprites) {
    code += processSpriteData(sprite, indent, parentContext);
  }

  return code;
}

// ===== 使用示例 =====
/*
// 示例1：基础 Sprite
const basicSpriteData: SpriteData = {
  className: 'Sprite',
  referenceName: '_mySprite',
  properties: {
    x: 100,
    y: 200,
    width: 64,
    height: 64,
    anchorX: 0.5,
    anchorY: 0.5,
    alpha: 0.8,
    url: 'img/pictures/Actor1.png'
  }
};

// 示例2：包含 elements 数组的 Sprite
const elementsSprite: SpriteData = {
  className: 'Sprite',
  referenceName: '_textSprite',
  properties: {
    x: 50,
    y: 100,
    fontBold: false,
    fontSize: 24,
    textColor: '#ffffff',
    elements: [
      {
        type: 'text',
        text: 'Hello World',
        x: 10,
        y: 10,
        align: 'center'
      }
    ]
  }
};

// 使用方法
const code1 = processSpriteData(basicSpriteData);
const code2 = processSpriteData(elementsSprite);

console.log('生成的代码:', code1);
console.log('生成的代码:', code2);
*/
