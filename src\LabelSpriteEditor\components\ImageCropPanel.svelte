<script lang="ts">
  import type { ImageElement } from '../../type/bitmap.svelte';
  import LabelInput from '../../components/LabelInput.svelte';
  import { bitmapModel } from '../stores/bitmapStore';
  import ImageCropComponent from './ImageCropComponent.svelte';

  // 🔧 Svelte 5: Props
  interface Props {
    element?: ImageElement | null;
  }

  let { element }: Props = $props();

  // 跟踪当前元素ID，避免重复初始化
  let lastElementId = $state('');

  // 🔧 强制重新渲染的计数器
  let forceUpdateCounter = $state(0);

  // 🔧 响应式变量来跟踪是否有裁切数据
  let hasValidCropData = $state(false);

  // 🔧 控制弹窗显示
  let showCropModal = $state(false);



  // 🔧 检查是否有裁切数据
  function hasCropData(): boolean {
    // 🔧 使用 forceUpdateCounter 来确保响应式更新
    const _ = forceUpdateCounter; // 确保依赖于这个状态

    const bitmapRegions = element?.source?.regions;
    const hasData = bitmapRegions && bitmapRegions.length > 0;
    console.log('🔍 hasCropData 检查:', {
      element: !!element,
      source: !!element?.source,
      regions: bitmapRegions?.length || 0,
      hasData,
      forceUpdateCounter
    });
    return hasData;
  }

  // 🔧 监听元素ID变化时初始化
  $effect(() => {
    const currentElementId = element?.id || '';

    // 只在元素真正切换时才初始化
    if (currentElementId !== lastElementId) {
      console.log('🔍 元素ID变化，从', lastElementId, '到', currentElementId);
      lastElementId = currentElementId;
      hasInitializedCrop = false;

      // 重置为默认值
      gridRows = 2;
      gridCols = 2;
      selectedGridX = 0;
      selectedGridY = 0;

      // 🔧 主动初始化图片数据
      if (element?.source) {
        console.log('🔧 主动初始化图片数据');
        loadImageUrl();

        // 🔧 如果有裁切数据，确保图片数据可用
        if (element.source.regions && element.source.regions.length > 0) {
          console.log('🔧 发现裁切数据，确保图片数据可用');
          setTimeout(() => {
            ensureImageLoaded();
            forceUpdateCounter++; // 强制重新渲染
          }, 100);
        }
      }
    }
  });

  // 🔧 监听裁切数据变化，更新响应式变量
  $effect(() => {
    // 🔧 依赖于 forceUpdateCounter 来确保更新
    forceUpdateCounter;

    const bitmapRegions = element?.source?.regions;
    const newHasValidCropData = bitmapRegions && bitmapRegions.length > 0;
    const regionsCount = bitmapRegions?.length || 0;

    console.log('🔍 监听裁切数据变化:', {
      newHasValidCropData,
      regionsCount,
      elementId: element?.id,
      forceUpdateCounter
    });

    // 🔧 更新响应式变量
    hasValidCropData = newHasValidCropData;

    if (hasValidCropData) {
      console.log('🔍 发现裁切数据，显示裁切结果');
      // 🔧 确保图片数据可用
      ensureImageLoaded();
      // 初始化网格设置（用于显示）
      initializeGridFromElementRegions(element?.source?.regions || []);
    } else {
      console.log('🔍 没有裁切数据，显示 ImageCropComponent');
    }
  });

  // 🔧 Canvas 相关代码已移动到 ImageCropComponent

  // 转换为相对路径的辅助函数
  function convertToRelativePath(absolutePath: string): string {
    // 简单的路径转换逻辑，可以根据需要调整
    if (absolutePath.includes('img/')) {
      const imgIndex = absolutePath.lastIndexOf('img/');
      return absolutePath.substring(imgIndex);
    }
    return absolutePath;
  }

  // 网格设置 - 默认 2x2 网格，方便用户开始裁切
  let gridRows = $state(2);
  let gridCols = $state(2);

  // 选中的网格
  let selectedGridX = $state(0);
  let selectedGridY = $state(0);

  // 图片尺寸
  let imageWidth = $state(0);
  let imageHeight = $state(0);



  // 图片URL状态
  let imageUrl = $state('');
  let imageLoaded = $state(false);

  // 当前加载的 bitmap 引用
  let currentBitmap = $state<any>(null);





  // 获取图片URL
  function loadImageUrl() {
    if (!element?.source) {
      imageUrl = '';
      currentBitmap = null;
      return;
    }

    try {
      // 检查 element.source 的类型
      if (typeof element.source === 'string') {
        // 如果是字符串路径，使用 ImageManager 加载
        if (typeof window !== 'undefined' && (window as any).ImageManager) {
          const ImageManager = (window as any).ImageManager;
          const bitmap = ImageManager.loadBitmapFromUrl(element.source);

          console.log('从字符串路径加载图片bitmap:', element.source, bitmap);
          handleBitmapLoaded(bitmap, element.source);
        }
      } else if (element.source && typeof element.source === 'object') {
        // 如果是 Bitmap 对象，直接使用
        const bitmap = element.source;
        console.log('使用现有的 Bitmap 对象:', bitmap);

        // 从 Bitmap 对象获取路径信息
        const sourcePath = bitmap._originalPath || bitmap._url || '';
        handleBitmapLoaded(bitmap, sourcePath);
      }
    } catch (error) {
      console.error('获取图片URL失败:', error);
      currentBitmap = null;
    }
  }

  // 处理 Bitmap 加载完成
  function handleBitmapLoaded(bitmap: any, sourcePath: string) {
    if (!bitmap) return;

    // 保存当前 bitmap 引用
    currentBitmap = bitmap;

    // 🔧 设置图片尺寸
    if (bitmap.width && bitmap.height) {
      imageWidth = bitmap.width;
      imageHeight = bitmap.height;
      imageLoaded = true;
      console.log('设置图片尺寸:', { imageWidth, imageHeight });
    }

    // 确保 bitmap 保存了正确的路径信息
    // _originalPath 应该是相对路径（用于代码生成）
    if (!bitmap._originalPath && sourcePath) {
      const relativePath = convertToRelativePath(sourcePath);
      bitmap._originalPath = relativePath;
      console.log('设置 bitmap._originalPath (相对路径):', relativePath);
    }

    // 确保 _url 是完整的绝对路径
    console.log('bitmap 路径信息:', {
      '_url': bitmap._url,
      '_originalPath': bitmap._originalPath,
      'sourcePath': sourcePath
    });

    // 初始化 bitmap 的 regions 数组（如果不存在）
    if (!bitmap.regions) {
      bitmap.regions = [];
      console.log('初始化 bitmap.regions 数组');
    }

    // 从 element.regions 恢复裁切信息到 bitmap.regions
    if (element?.regions && element.regions.length > 0) {
      console.log('从 element.regions 恢复裁切信息:', element.regions);

      // 将 element.regions 中的区域添加到 bitmap.regions（避免重复）
      element.regions.forEach(region => {
        const existingRegion = bitmap.regions.find((r: any) =>
          r.sx === region.sx && r.sy === region.sy && r.sw === region.sw && r.sh === region.sh
        );

        if (!existingRegion) {
          bitmap.regions.push({
            sx: region.sx,
            sy: region.sy,
            sw: region.sw,
            sh: region.sh,
            label: region.label
          });
          console.log('恢复裁切区域到 bitmap.regions:', region);
        }
      });
    }

    // 检查 bitmap 是否已经有可用的图片数据
    if (bitmap._image) {
      console.log('bitmap._image 已可用，可以直接绘制');
      // 设置一个标识符来触发响应式绘制
      imageUrl = 'bitmap_ready';
    } else if (bitmap._url && bitmap._url.startsWith('blob:')) {
      // 如果有 blob URL，尝试使用（但可能已失效）
      imageUrl = bitmap._url;
      console.log('尝试使用 blob URL:', imageUrl);
    } else {
      // 等待图片加载完成
      if (typeof bitmap.addLoadListener === 'function') {
        bitmap.addLoadListener(() => {
          console.log('图片加载完成，bitmap._image:', bitmap._image);
          console.log('图片加载完成，新URL:', bitmap._url);
          if (bitmap._image) {
            // 图片已加载到 _image，触发重绘
            console.log('触发重绘，因为 bitmap._image 已可用');
            imageUrl = 'bitmap_ready';
          } else if (bitmap._url && bitmap._url.startsWith('blob:')) {
            imageUrl = bitmap._url;
          }
        });
      }

      // 备用方案：定期检查
      const checkInterval = setInterval(() => {
        if (bitmap._image) {
          console.log('定期检查发现 bitmap._image 已可用');
          imageUrl = 'bitmap_ready';
          clearInterval(checkInterval);
        } else if (bitmap._url && bitmap._url.startsWith('blob:')) {
          imageUrl = bitmap._url;
          console.log('定期检查获得URL:', imageUrl);
          clearInterval(checkInterval);
        }
      }, 100);

      // 5秒后停止检查
      setTimeout(() => clearInterval(checkInterval), 5000);
    }
  }

  // 🔧 重新生成正确的网格数据
  function regenerateValidRegions(rows: number, cols: number) {
    if (!imageWidth || !imageHeight || rows <= 0 || cols <= 0) return [];

    const cellWidth = imageWidth / cols;
    const cellHeight = imageHeight / rows;
    const validRegions = [];

    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const sx = Math.round(col * cellWidth);
        const sy = Math.round(row * cellHeight);
        const sw = Math.round(cellWidth);
        const sh = Math.round(cellHeight);

        validRegions.push({
          sx, sy, sw, sh,
          gridIndex: row * cols + col,
          label: `区域 ${col + 1}-${row + 1}`
        });
      }
    }

    console.log('🔧 重新生成有效区域:', validRegions);
    return validRegions;
  }

  // 🔧 新增：从 element.regions 初始化网格设置
  function initializeGridFromElementRegions(regions: any[]) {
    if (!regions || regions.length === 0) {
      console.log('🔍 initializeGridFromElementRegions: regions 为空');
      return;
    }

    console.log('🎯 从 element.regions 初始化网格设置，regions 数量:', regions.length);
    console.log('🎯 regions 详细数据:', regions);

    // 分析 regions 来推断网格设置
    const analysisResult = analyzeRegionsGrid(regions);

    if (analysisResult) {
      console.log('🎯 分析得到网格设置:', analysisResult);

      // 更新网格设置
      gridRows = analysisResult.rows;
      gridCols = analysisResult.cols;

      console.log('🎯 设置网格完成:', { gridRows, gridCols });

      // 🔧 检查是否需要重新生成正确的区域数据
      const validRegions = regions.filter((region: any) => {
        const rSw = Number(region.sw);
        const rSh = Number(region.sh);
        return rSw < imageWidth * 0.9 || rSh < imageHeight * 0.9;
      });

      if (validRegions.length < gridRows * gridCols) {
        console.warn('⚠️ 有效区域数量不足，重新生成网格数据');
        const newRegions = regenerateValidRegions(gridRows, gridCols);

        // 更新 bitmap.regions
        if (element?.source) {
          element.source.regions = newRegions;
          console.log('🔧 已更新 bitmap.regions 为重新生成的数据');
        }
      }

      // 🔧 根据当前元素的 sx, sy, sw, sh 设置选中区域
      if (element && element.sx !== undefined && element.sy !== undefined &&
          element.sw !== undefined && element.sh !== undefined) {

        console.log('🔍 查找匹配的区域，元素坐标:', {
          sx: element.sx, sy: element.sy, sw: element.sw, sh: element.sh,
          types: {
            sx: typeof element.sx, sy: typeof element.sy,
            sw: typeof element.sw, sh: typeof element.sh
          }
        });

        // 🔧 使用更宽松的匹配逻辑，考虑数据类型和精度问题
        const findMatchingRegion = (r: any) => {
          const rSx = Number(r.sx);
          const rSy = Number(r.sy);
          const rSw = Number(r.sw);
          const rSh = Number(r.sh);
          const eSx = Number(element.sx);
          const eSy = Number(element.sy);
          const eSw = Number(element.sw);
          const eSh = Number(element.sh);

          // 允许1像素的误差
          const tolerance = 1;
          const matches = Math.abs(rSx - eSx) <= tolerance &&
                         Math.abs(rSy - eSy) <= tolerance &&
                         Math.abs(rSw - eSw) <= tolerance &&
                         Math.abs(rSh - eSh) <= tolerance;

          if (matches) {
            console.log('🎯 找到匹配区域:', {
              region: { sx: rSx, sy: rSy, sw: rSw, sh: rSh },
              element: { sx: eSx, sy: eSy, sw: eSw, sh: eSh },
              differences: {
                dx: Math.abs(rSx - eSx),
                dy: Math.abs(rSy - eSy),
                dw: Math.abs(rSw - eSw),
                dh: Math.abs(rSh - eSh)
              }
            });
          }

          return matches;
        };

        // 🔧 使用 bitmap.regions 查找匹配的区域
        const bitmapRegions = element?.source?.regions;
        if (!bitmapRegions) {
          selectedGridX = 0;
          selectedGridY = 0;
          console.log('🎯 无 bitmap.regions，选择第一个:', { selectedGridX, selectedGridY });
          return;
        }

        // 找到匹配当前裁剪区域的 region
        const currentRegion = bitmapRegions.find(findMatchingRegion);

        if (currentRegion && currentRegion.gridIndex !== undefined) {
          selectedGridX = currentRegion.gridIndex % gridCols;
          selectedGridY = Math.floor(currentRegion.gridIndex / gridCols);
          console.log('🎯 从 gridIndex 设置选中位置:', { selectedGridX, selectedGridY, gridIndex: currentRegion.gridIndex });
        } else {
          // 备用方案：根据坐标在 bitmap.regions 中查找匹配的区域
          const matchingRegionIndex = bitmapRegions.findIndex(findMatchingRegion);

          if (matchingRegionIndex >= 0) {
            selectedGridX = matchingRegionIndex % gridCols;
            selectedGridY = Math.floor(matchingRegionIndex / gridCols);
            console.log('🎯 从索引设置选中位置:', { selectedGridX, selectedGridY, matchingRegionIndex });
          } else {
            // 最后备用方案：选择第一个
            selectedGridX = 0;
            selectedGridY = 0;
            console.log('🎯 未找到匹配区域，选择第一个:', { selectedGridX, selectedGridY });
            console.log('🔍 所有 bitmap.regions 的坐标:');
            bitmapRegions.forEach((r: any, i: number) => {
              console.log(`  region[${i}]:`, { sx: r.sx, sy: r.sy, sw: r.sw, sh: r.sh });
            });
          }
        }
      }
    } else {
      console.warn('🔍 analyzeRegionsGrid 分析失败，使用默认网格设置');
      gridRows = 1;
      gridCols = 1;
      selectedGridX = 0;
      selectedGridY = 0;
    }
  }

  // 🔧 新增：分析 regions 数组来推断网格设置
  function analyzeRegionsGrid(regions: any[]): { rows: number, cols: number } | null {
    if (!regions || regions.length === 0) {
      console.log('🔍 analyzeRegionsGrid: regions 为空或长度为0');
      return null;
    }

    console.log('🔍 analyzeRegionsGrid 开始分析 regions:');
    console.log('🔍 regions 数量:', regions.length);
    console.log('🔍 前3个 regions 示例:', regions.slice(0, 3));

    // 方法1：如果 regions 中有 gridIndex，可以直接推断
    const hasGridIndex = regions.some(r => r.gridIndex !== undefined);
    console.log('🔍 是否有 gridIndex:', hasGridIndex);

    if (hasGridIndex) {
      const gridIndices = regions.map(r => r.gridIndex).filter(idx => idx !== undefined);
      const maxGridIndex = Math.max(...gridIndices);
      const totalCells = maxGridIndex + 1;

      console.log('🔍 从 gridIndex 分析:');
      console.log('  - gridIndices:', gridIndices);
      console.log('  - maxGridIndex:', maxGridIndex);
      console.log('  - totalCells:', totalCells);

      // 尝试找到合理的行列组合
      for (let cols = 1; cols <= totalCells; cols++) {
        if (totalCells % cols === 0) {
          const rows = totalCells / cols;
          console.log(`  - 尝试组合: ${rows}行 x ${cols}列 = ${rows * cols}`);
          if (rows <= cols * 2) { // 偏好更宽的布局
            console.log('🎯 找到合适的组合 (从gridIndex):', { rows, cols });
            return { rows, cols };
          }
        }
      }

      // 如果没有找到合适的组合，使用简单的方式
      const cols = Math.ceil(Math.sqrt(totalCells));
      const rows = Math.ceil(totalCells / cols);
      console.log('🎯 使用简单组合 (从gridIndex):', { rows, cols });
      return { rows, cols };
    }

    // 方法2：根据坐标分析
    console.log('🔍 使用坐标分析方法');

    // 收集所有起始坐标和结束坐标
    const allX = new Set<number>();
    const allY = new Set<number>();

    regions.forEach((r, index) => {
      if (index < 3) { // 只打印前3个的详细信息
        console.log(`  - region[${index}]: sx=${r.sx}, sy=${r.sy}, sw=${r.sw}, sh=${r.sh}`);
      }
      allX.add(r.sx);           // 起始X
      allX.add(r.sx + r.sw);    // 结束X
      allY.add(r.sy);           // 起始Y
      allY.add(r.sy + r.sh);    // 结束Y
    });

    const uniqueX = [...allX].sort((a, b) => a - b);
    const uniqueY = [...allY].sort((a, b) => a - b);

    // 网格的列数 = X坐标数量 - 1，行数 = Y坐标数量 - 1
    const cols = uniqueX.length - 1;
    const rows = uniqueY.length - 1;

    console.log('🔍 从坐标分析结果:');
    console.log('  - uniqueX:', uniqueX);
    console.log('  - uniqueY:', uniqueY);
    console.log('  - 计算得到: rows =', rows, 'cols =', cols);
    console.log('  - 验证: cols * rows =', cols * rows, 'regions.length =', regions.length);

    if (cols > 0 && rows > 0 && cols * rows === regions.length) {
      console.log('🎯 找到合适的组合 (从坐标):', { rows, cols });
      return { rows, cols };
    } else {
      console.log('🔍 坐标分析失败，原因:');
      console.log('  - cols > 0:', cols > 0);
      console.log('  - rows > 0:', rows > 0);
      console.log('  - cols * rows === regions.length:', cols * rows === regions.length);
    }

    // 方法3：最后的备用方案
    console.log('🔍 使用最后备用方案');
    if (regions.length > 1) {
      const cols = Math.ceil(Math.sqrt(regions.length));
      const rows = Math.ceil(regions.length / cols);
      console.log('🎯 备用方案计算:');
      console.log('  - regions.length:', regions.length);
      console.log('  - Math.sqrt(regions.length):', Math.sqrt(regions.length));
      console.log('  - cols = Math.ceil(Math.sqrt(regions.length)):', cols);
      console.log('  - rows = Math.ceil(regions.length / cols):', rows);
      console.log('🎯 使用最后备用方案:', { rows, cols });
      return { rows, cols };
    }

    console.log('🔍 analyzeRegionsGrid: 无法分析，返回 null');
    return null;
  }

  // 根据元素的裁切信息初始化网格选择（只在首次加载时）
  let hasInitializedCrop = $state(false);

  function initializeCropFromElement() {
    if (!element || !imageLoaded) return;

    // 🔧 只在首次加载时初始化，避免覆盖用户修改
    if (hasInitializedCrop) {
      console.log('🎯 跳过重复初始化，避免覆盖用户修改');
      return;
    }

    console.log('🎯 首次初始化裁切区域，根据元素数据:', {
      sx: element.sx,
      sy: element.sy,
      sw: element.sw,
      sh: element.sh,
      imageWidth,
      imageHeight
    });

    // 如果元素有有效的裁切信息，计算对应的网格位置
    if (element.sx !== undefined && element.sy !== undefined &&
        element.sw !== undefined && element.sh !== undefined &&
        element.sw > 0 && element.sh > 0) {

      // 计算网格位置（假设是均匀网格）
      const gridCellWidth = imageWidth / gridCols;
      const gridCellHeight = imageHeight / gridRows;

      const gridX = Math.floor(element.sx / gridCellWidth);
      const gridY = Math.floor(element.sy / gridCellHeight);

      // 确保网格位置在有效范围内
      selectedGridX = Math.max(0, Math.min(gridX, gridCols - 1));
      selectedGridY = Math.max(0, Math.min(gridY, gridRows - 1));

      console.log('🎯 设置网格选择位置:', { selectedGridX, selectedGridY });

      // 标记已初始化
      hasInitializedCrop = true;
    }
  }

  // 响应式：当element.source变化时重新加载图片
  $effect(() => {
    if (element?.source) {
      console.log('element.source变化，重新加载:', element.source);
      loadImageUrl();
    }
  });

  // 🔧 响应式：当裁切数据变化时，强制重新渲染裁切图片
  $effect(() => {
    const regionsCount = element?.source?.regions?.length || 0;
    // 确保依赖于强制更新计数器
    forceUpdateCounter;

    console.log('🔍 裁切数据变化，重新渲染裁切图片:', { regionsCount, forceUpdateCounter });

    if (regionsCount > 0 && currentBitmap?._image) {
      console.log('🔍 有裁切数据且图片已加载，准备显示所有裁切区域');
    }
  });



  // 响应式：当图片加载完成且元素数据可用时，初始化裁切区域
  $effect(() => {
    if (imageLoaded && element && element.sx !== undefined) {
      console.log('🎯 图片加载完成，初始化裁切区域');
      initializeCropFromElement();
    }
  });

  // 🔧 Canvas 相关函数已移动到 ImageCropComponent

  // 🔧 图片加载和绘制功能已移动到 ImageCropComponent

  // 🔧 Canvas 绘制功能已移动到 ImageCropComponent

  // 🔧 所有 Canvas 相关函数已移动到 ImageCropComponent

  // 🔧 确保图片数据可用
  function ensureImageLoaded(): HTMLImageElement | null {
    console.log('🔍 ensureImageLoaded 检查图片数据');

    // 方法1: 使用 currentBitmap._image
    if (currentBitmap?._image) {
      console.log('✅ currentBitmap._image 可用');
      return currentBitmap._image;
    }

    // 方法2: 使用 element.source._image
    if (element?.source?._image) {
      console.log('✅ element.source._image 可用');
      return element.source._image;
    }

    // 方法3: 如果 element.source 是 bitmap 对象
    if (element?.source && typeof element.source === 'object' && element.source._image) {
      console.log('✅ element.source 作为 bitmap 对象可用');
      return element.source._image;
    }

    // 方法4: 尝试从 element.source 重新加载
    if (element?.source && !currentBitmap) {
      console.log('🔧 尝试从 element.source 重新加载图片');

      // 如果 element.source 是 bitmap 对象，直接使用
      if (typeof element.source === 'object' && element.source.width && element.source.height) {
        currentBitmap = element.source;
        imageWidth = element.source.width;
        imageHeight = element.source.height;
        imageLoaded = true;

        if (element.source._image) {
          console.log('✅ 重新设置 currentBitmap 成功');
          return element.source._image;
        }
      }
    }

    console.log('❌ 无法获取图片数据');
    return null;
  }

  // 🔧 获取所有裁切图片数据
  function getCropImages() {
    console.log('🔍 getCropImages 开始获取裁切图片数据');

    // 检查必要的数据
    if (!element?.source?.regions) {
      console.log('❌ 没有 regions 数据');
      return [];
    }

    const regions = element.source.regions;
    console.log('🔍 找到 regions 数量:', regions.length);

    // 🔧 使用新的图片加载函数
    const imageSource = ensureImageLoaded();

    if (!imageSource) {
      console.log('❌ 无法获取图片数据');
      return [];
    }

    const cropImages = [];

    for (let i = 0; i < regions.length; i++) {
      const region = regions[i];
      console.log(`🔍 处理区域 ${i + 1}:`, region);

      try {
        // 创建临时 canvas 来裁切图片
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          console.error('❌ 无法获取 canvas context');
          continue;
        }

        canvas.width = region.sw;
        canvas.height = region.sh;

        // 绘制裁切区域
        ctx.drawImage(
          imageSource,
          region.sx, region.sy, region.sw, region.sh,
          0, 0, region.sw, region.sh
        );

        const dataUrl = canvas.toDataURL();
        console.log(`✅ 生成区域 ${i + 1} 的图片数据，大小: ${region.sw}x${region.sh}`);

        cropImages.push({
          dataUrl: dataUrl,
          label: region.label || `区域 ${i + 1}`,
          region: region,
          index: i
        });
      } catch (error) {
        console.error(`❌ 处理区域 ${i + 1} 时出错:`, error);
      }
    }

    console.log('🔍 getCropImages 完成，生成了', cropImages.length, '个裁切图片');
    return cropImages;
  }

  // 🔧 检查区域是否被选中
  function isRegionSelected(region: any, _index: number): boolean {
    if (!element) return false;

    // 检查当前元素的裁切坐标是否与这个区域匹配
    const tolerance = 1; // 允许1像素的误差
    const matches = Math.abs(Number(element.sx) - Number(region.sx)) <= tolerance &&
                   Math.abs(Number(element.sy) - Number(region.sy)) <= tolerance &&
                   Math.abs(Number(element.sw) - Number(region.sw)) <= tolerance &&
                   Math.abs(Number(element.sh) - Number(region.sh)) <= tolerance;

    return matches;
  }

  // 🔧 处理选择裁切区域
  function handleSelectCropRegion(region: any, index: number) {
    if (!element) return;

    console.log('🔍 选择裁切区域:', { index, region });

    // 更新当前元素的裁切坐标
    element.sx = Number(region.sx);
    element.sy = Number(region.sy);
    element.sw = Number(region.sw);
    element.sh = Number(region.sh);

    // 更新网格选择位置
    selectedGridX = index % gridCols;
    selectedGridY = Math.floor(index / gridCols);

    console.log('🔍 更新元素裁切坐标:', {
      sx: element.sx, sy: element.sy, sw: element.sw, sh: element.sh,
      selectedGridX, selectedGridY
    });

    // 触发UI更新
    bitmapModel.update(model => model);
  }

  // 🔧 打开裁切弹窗
  function openCropModal() {
    showCropModal = true;
    console.log('🔧 打开裁切弹窗');
  }

  // 🔧 关闭裁切弹窗
  function closeCropModal() {
    showCropModal = false;
    console.log('🔧 关闭裁切弹窗');
  }

  // 🔧 重置裁切数据
  function handleResetCropData() {
    if (element?.source) {
      element.source.regions = [];
      console.log('🔧 已删除裁切数据');

      // 🔧 强制重新渲染
      forceUpdateCounter++;
      console.log('🔧 强制更新计数器:', forceUpdateCounter);

      // 🔧 打开裁切弹窗进行重新裁切
      openCropModal();

      // 触发UI更新
      bitmapModel.update(model => model);
    }
  }

  // 🔧 处理裁切完成事件
  function handleCropCompleted(event: CustomEvent) {
    console.log('🔧 接收到裁切完成事件:', event.detail);

    // 🔧 强制触发响应式更新
    // 通过重新赋值来触发 $effect 重新运行
    if (element?.source?.regions) {
      const regions = element.source.regions;
      element.source.regions = [...regions]; // 创建新数组引用
      console.log('🔧 强制更新 regions 引用，触发响应式更新，regions数量:', regions.length);
    }

    // 🔧 强制重新渲染
    forceUpdateCounter++;
    console.log('🔧 强制更新计数器:', forceUpdateCounter);

    // 🔧 关闭裁切弹窗
    closeCropModal();

    // 🔧 强制重新检查裁切数据
    console.log('🔧 裁切完成后，强制重新检查数据');
    const hasData = hasCropData();
    console.log('🔧 重新检查结果:', hasData);

    // 触发UI更新
    bitmapModel.update(model => model);
  }

  // 🔧 处理重置事件
  function handleCropReset(event: CustomEvent) {
    console.log('🔧 接收到重置事件:', event.detail);

    // 🔧 强制触发响应式更新
    if (element?.source) {
      element.source.regions = []; // 确保清空
      console.log('🔧 强制清空 regions，触发响应式更新');
    }

    // 🔧 强制重新渲染
    forceUpdateCounter++;
    console.log('🔧 强制更新计数器:', forceUpdateCounter);

    // 🔧 关闭裁切弹窗
    closeCropModal();

    // 🔧 强制重新检查裁切数据
    console.log('🔧 重置完成后，强制重新检查数据');
    const hasData = hasCropData();
    console.log('🔧 重新检查结果:', hasData);

    // 触发UI更新
    bitmapModel.update(model => model);
  }
</script>

<div class="image-crop-panel">
  <!-- 🔧 调试信息 -->
  <div class="debug-info" style="font-size: 12px; color: #666; margin-bottom: 8px; padding: 8px; background: #f0f0f0; border-radius: 4px;">
    调试状态: hasValidCropData={hasValidCropData}, regions={element?.source?.regions?.length || 0},
    forceUpdateCounter={forceUpdateCounter}, elementId={element?.id}
  </div>

  {#if hasValidCropData}
    <!-- 🔧 有裁切数据时，显示裁切结果 -->
    <div class="crop-results">
      <h4>裁切结果 ({element?.source?.regions?.length || 0} 个区域)</h4>

      <!-- 🔧 裁切图片调试信息 -->
      <div class="debug-info" style="font-size: 12px; color: #666; margin-bottom: 8px; padding: 8px; background: #f0f0f0; border-radius: 4px;">
        裁切图片调试: currentBitmap={!!currentBitmap}, _image={!!currentBitmap?._image},
        element.source._image={!!element?.source?._image}, getCropImages()数量={getCropImages().length}, gridCols={gridCols}
      </div>

      <!-- 🔧 如果没有图片数据，显示加载提示 -->
      {#if !currentBitmap?._image && !element?.source?._image}
        <div class="loading-image-hint">
          <p>正在加载图片数据...</p>
          <button onclick={() => { loadImageUrl(); forceUpdateCounter++; }}>🔄 重新加载图片</button>
        </div>
      {/if}

      <!-- 显示裁切图片网格 -->
      <div
        class="crop-images-grid"
        style="grid-template-columns: repeat({Math.min(gridCols, 6)}, 1fr);"
      >
        {#each getCropImages() as cropImage, index}
          <button
            class="crop-image-item {isRegionSelected(cropImage.region, index) ? 'selected' : ''}"
            onclick={() => handleSelectCropRegion(cropImage.region, index)}
            title="点击选择区域 {index + 1} ({cropImage.label})"
          >
            <img
              src={cropImage.dataUrl}
              alt={cropImage.label}
              class="crop-image"
            />
            <div class="crop-label">{cropImage.label}</div>
            <div class="crop-index">#{index + 1}</div>
          </button>
        {:else}
          <div class="no-crop-images">
            <p>没有生成裁切图片</p>
            <p>调试信息: regions={element?.source?.regions?.length || 0}, currentBitmap={!!currentBitmap}, _image={!!currentBitmap?._image}</p>
          </div>
        {/each}
      </div>

      <!-- 网格信息 -->
      <div class="grid-info">
        <p>网格布局: {gridRows} 行 × {gridCols} 列 = {gridRows * gridCols} 个区域</p>
        <p>当前选中: 第 {selectedGridX + 1} 列，第 {selectedGridY + 1} 行</p>
      </div>

      <!-- 重置按钮 -->
      <div class="crop-actions">
        <button
          onclick={handleResetCropData}
          class="reset-btn"
          title="删除裁切数据，重新进行裁切"
        >
          🔄 重新裁切
        </button>
      </div>

      <!-- 🔧 调试信息 -->
      <div class="debug-info" style="font-size: 12px; color: #666; margin-top: 8px;">
        调试: 裁切模式, regions={element?.source?.regions?.length || 0},
        网格={gridRows}x{gridCols}, 选中={selectedGridX + 1},{selectedGridY + 1}
      </div>
    </div>
  {:else}
    <!-- 🔧 没有裁切数据时，显示开始裁切按钮 -->
    <div class="crop-editor">
      <h4>图片裁切</h4>
      <p>当前没有裁切数据，点击下方按钮开始裁切图片。</p>

      <div class="start-crop-section">
        <button
          onclick={openCropModal}
          class="start-crop-btn"
        >
          ✂️ 开始裁切
        </button>
      </div>

      <!-- 🔧 调试信息 -->
      <div class="debug-info" style="font-size: 12px; color: #666; margin-top: 8px;">
        调试: 编辑模式, element={!!element}, source={!!element?.source}
      </div>
    </div>
  {/if}

  <!-- 🔧 裁切弹窗 -->
  {#if showCropModal}
    <div
      class="crop-modal-overlay"
      role="dialog"
      aria-modal="true"
      tabindex="-1"
      onclick={closeCropModal}
      onkeydown={(e) => e.key === 'Escape' && closeCropModal()}
    >
      <div
        class="crop-modal-content"
        role="document"
        onclick={(e) => e.stopPropagation()}
        onkeydown={(e) => e.stopPropagation()}
      >
        <div class="crop-modal-header">
          <h3>图片裁切编辑器</h3>
          <button class="close-btn" onclick={closeCropModal}>✕</button>
        </div>

        <div class="crop-modal-body">
          <ImageCropComponent
            {element}
            onCropCompleted={handleCropCompleted}
            onCropReset={handleCropReset}
          />
        </div>
      </div>
    </div>
  {/if}
</div>



<style>
  .image-crop-panel {
    width: 100%;
  }

  /* 🔧 裁切结果样式 */
  .crop-results {
    padding: 16px;
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: var(--border-radius, 6px);
  }

  .crop-results h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--theme-text, #111827);
  }

  /* 🔧 裁切图片网格样式 */
  .crop-images-grid {
    display: grid;
    gap: 12px;
    margin-bottom: 16px;
    padding: 16px;
    background: var(--theme-surface-light, #f9fafb);
    border-radius: 8px;
    border: 1px solid var(--theme-border-light, #f3f4f6);
    /* 动态网格列数通过内联样式设置 */
  }

  .crop-image-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px;
    background: white;
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 100px;
    width: 100%;
    max-width: 140px;
    margin: 0 auto;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .crop-image-item:hover {
    border-color: var(--theme-primary-light, #dbeafe);
    background: var(--theme-primary-light, #dbeafe);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .crop-image-item.selected {
    border-color: var(--theme-primary, #3b82f6);
    background: var(--theme-primary-light, #dbeafe);
    box-shadow: 0 0 0 1px var(--theme-primary, #3b82f6);
  }

  .crop-image {
    width: 100%;
    height: 70px;
    object-fit: contain;
    border-radius: 6px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
  }

  .crop-label {
    margin-top: 6px;
    font-size: 11px;
    font-weight: 500;
    color: var(--theme-text-secondary, #6b7280);
    text-align: center;
    line-height: 1.3;
    word-break: break-word;
  }

  .crop-index {
    margin-top: 4px;
    font-size: 10px;
    font-weight: 600;
    color: var(--theme-primary, #3b82f6);
    text-align: center;
    background: var(--theme-primary-light, #dbeafe);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 20px;
  }

  /* 🔧 网格信息样式 */
  .grid-info {
    padding: 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
    border: 1px solid var(--theme-border-light, #f3f4f6);
    margin-bottom: 12px;
  }

  .grid-info p {
    margin: 4px 0;
    font-size: 12px;
    color: var(--theme-text-secondary, #6b7280);
    line-height: 1.4;
  }

  .grid-info p:first-child {
    font-weight: 500;
    color: var(--theme-text, #111827);
  }

  /* 🔧 裁切操作按钮样式 */
  .crop-actions {
    display: flex;
    justify-content: center;
    padding-top: 12px;
    border-top: 1px solid var(--theme-border-light, #f3f4f6);
  }

  .reset-btn {
    padding: 8px 16px;
    background: var(--theme-warning, #f59e0b);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
  }

  .reset-btn:hover {
    background: var(--theme-warning-hover, #d97706);
    transform: translateY(-1px);
  }

  /* 🔧 编辑器样式 */
  .crop-editor {
    padding: 16px;
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: var(--border-radius, 6px);
  }

  .crop-editor h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--theme-text, #111827);
  }

  /* 🔧 开始裁切按钮样式 */
  .start-crop-section {
    text-align: center;
    margin: 24px 0;
  }

  .start-crop-btn {
    padding: 12px 24px;
    background: var(--theme-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  }

  .start-crop-btn:hover {
    background: var(--theme-primary-hover, #2563eb);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
  }

  /* 🔧 弹窗样式 */
  .crop-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
  }

  .crop-modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
  }

  @keyframes modalSlideIn {
    from {
      opacity: 0;
      transform: scale(0.9) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  .crop-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
  }

  .crop-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
  }

  .close-btn:hover {
    background: #e5e7eb;
    color: #374151;
  }

  .crop-modal-body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
  }

  /* 🔧 无裁切图片时的提示样式 */
  .no-crop-images {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
    background: #f9fafb;
    border-radius: 8px;
    border: 2px dashed #d1d5db;
  }

  .no-crop-images p {
    margin: 8px 0;
    font-size: 14px;
  }

  .no-crop-images p:first-child {
    font-weight: 500;
    font-size: 16px;
    color: #374151;
  }

  /* 🔧 图片加载提示样式 */
  .loading-image-hint {
    text-align: center;
    padding: 20px;
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .loading-image-hint p {
    margin: 0 0 12px 0;
    color: #92400e;
    font-weight: 500;
  }

  .loading-image-hint button {
    padding: 8px 16px;
    background: #f59e0b;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.15s ease;
  }

  .loading-image-hint button:hover {
    background: #d97706;
  }
</style>
