/**
 * 对象删除逻辑
 */

import type { ObjectTreeNodeData } from './types';
import { globalObjectState } from '../../stores/objectState';
import { get } from 'svelte/store';

/**
 * 从父对象中移除子对象
 * @param parentObject 父对象
 * @param childObject 要移除的子对象
 */
function removeChildFromParent(parentObject: any, childObject: any): boolean {
  if (!parentObject || !childObject) {
    console.warn('父对象或子对象为空，无法执行删除操作');
    return false;
  }

  try {
    // 尝试使用 removeChild 方法
    if (parentObject.removeChild && typeof parentObject.removeChild === 'function') {
      parentObject.removeChild(childObject);
      console.log('使用 removeChild 方法成功移除对象');
      return true;
    }

    // 尝试从 children 数组中移除
    if (parentObject.children && Array.isArray(parentObject.children)) {
      const index = parentObject.children.indexOf(childObject);
      if (index > -1) {
        parentObject.children.splice(index, 1);
        console.log('从 children 数组中成功移除对象');
        return true;
      }
    }

    // 尝试遍历对象属性查找并移除
    for (const key in parentObject) {
      if (parentObject.hasOwnProperty(key)) {
        const value = parentObject[key];

        // 如果是数组，尝试从中移除
        if (Array.isArray(value)) {
          const index = value.indexOf(childObject);
          if (index > -1) {
            value.splice(index, 1);
            console.log(`从属性 ${key} 数组中成功移除对象`);
            return true;
          }
        }

        // 如果直接引用，设置为 null
        if (value === childObject) {
          parentObject[key] = null;
          console.log(`将属性 ${key} 设置为 null`);
          return true;
        }
      }
    }

    console.warn('无法找到合适的方法从父对象中移除子对象');
    return false;

  } catch (error) {
    console.error('从父对象中移除子对象时发生错误:', error);
    return false;
  }
}

/**
 * 递归清理对象及其所有子对象
 * @param obj 要清理的对象
 */
function cleanupObject(obj: any): void {
  if (!obj) return;

  try {
    // 如果对象有 destroy 方法，调用它
    if (obj.destroy && typeof obj.destroy === 'function') {
      obj.destroy();
      console.log('调用对象的 destroy 方法');
      return;
    }

    // 递归清理子对象
    if (obj.children && Array.isArray(obj.children)) {
      // 创建副本以避免在迭代过程中修改数组
      const childrenCopy = [...obj.children];
      for (const child of childrenCopy) {
        cleanupObject(child);
      }
      obj.children.length = 0; // 清空数组
    }

    // 清理其他可能的引用
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const value = obj[key];

        // 如果是数组，清空它
        if (Array.isArray(value)) {
          value.length = 0;
        }

        // 如果是对象且有 destroy 方法，调用它
        if (value && typeof value === 'object' && value.destroy && typeof value.destroy === 'function') {
          value.destroy();
        }
      }
    }

    console.log('对象清理完成');

  } catch (error) {
    console.error('清理对象时发生错误:', error);
  }
}

/**
 * 查找节点的父对象
 * @param targetNode 目标节点
 * @param rootObject 根对象
 * @param rootNode 根节点（可选，用于递归）
 */
function findParentObject(targetNode: ObjectTreeNodeData, rootObject: any, rootNode?: ObjectTreeNodeData): any {
  // 如果目标节点是根节点，没有父对象
  if (targetNode.isRoot) {
    return null;
  }

  // 简单情况：如果目标对象有 parent 属性
  if (targetNode.currentObject && targetNode.currentObject.parent) {
    return targetNode.currentObject.parent;
  }

  // 复杂情况：需要遍历对象树查找父对象
  // 这里需要实现一个递归搜索算法
  // 由于我们没有完整的对象树结构，这里返回根对象作为默认父对象
  return rootObject;
}

/**
 * 处理对象删除请求
 * @param targetNode 要删除的目标节点
 */
export async function handleObjectDeletion(targetNode: ObjectTreeNodeData): Promise<void> {
  console.log('开始删除对象:', {
    targetNode: targetNode.displayName,
    isRoot: targetNode.isRoot,
    targetObject: targetNode.currentObject
  });

  // 检查是否为根节点
  if (targetNode.isRoot) {
    console.warn('不能删除根节点');
    return;
  }

  try {
    // 获取当前全局状态
    const currentState = get(globalObjectState);

    if (!currentState.rootObject) {
      console.warn('没有根对象，无法执行删除操作');
      return;
    }

    // 查找父对象
    const parentObject = findParentObject(targetNode, currentState.rootObject);

    if (!parentObject) {
      console.warn('无法找到父对象，无法执行删除操作');
      return;
    }



    // 从父对象中移除目标对象
    const removed = removeChildFromParent(parentObject, targetNode.currentObject);

    if (removed) {
      console.log('对象删除成功');



      // 清理目标对象（递归清理所有子对象）
      cleanupObject(targetNode.currentObject);

      // 直接从状态管理中删除节点（包括所有子节点）
      const { objectTreeState } = await import('./objectTreeStore');
      const { ObjectTreeActions } = await import('./objectTreeActions');

      objectTreeState.update(state => {
        console.log('直接删除节点及其所有子节点:', targetNode.id);
        return ObjectTreeActions.removeNode(state, targetNode.id);
      });

      // 如果删除的是当前选中的对象，清除选中状态
      if (currentState.selectedObject && currentState.selectedObject.includes(targetNode.currentObject)) {
        globalObjectState.update(state => {
          state.selectedObject = null;
          state.selectedObjectType = null;
          state.selectedNodeId = null;
          return state;
        });
      }

      console.log('对象删除完成，包括所有子节点');

    } else {
      console.error('从父对象中移除目标对象失败');
    }

  } catch (error) {
    console.error('删除对象时发生错误:', error);
  }
}

/**
 * 批量删除对象
 * @param targetNodes 要删除的目标节点数组
 */
export async function handleBatchObjectDeletion(targetNodes: ObjectTreeNodeData[]): Promise<void> {
  console.log('开始批量删除对象:', targetNodes.map(node => node.displayName));

  // 过滤掉根节点
  const deletableNodes = targetNodes.filter(node => !node.isRoot);

  if (deletableNodes.length === 0) {
    console.warn('没有可删除的节点');
    return;
  }

  // 逐个删除对象
  for (const node of deletableNodes) {
    try {
      await handleObjectDeletion(node);
    } catch (error) {
      console.error(`删除节点 ${node.displayName} 时发生错误:`, error);
    }
  }

  console.log('批量删除完成');
}

/**
 * 检查对象是否可以被删除
 * @param targetNode 目标节点
 */
export function canDeleteObject(targetNode: ObjectTreeNodeData): boolean {
  // 根节点不能删除
  if (targetNode.isRoot) {
    return false;
  }

  // 检查对象是否存在
  if (!targetNode.currentObject) {
    return false;
  }

  // 其他检查可以在这里添加
  // 例如：检查对象是否被锁定、是否有特殊标记等

  return true;
}

// triggerObjectTreeRefresh 函数已移除
// 现在直接使用 ObjectTreeActions.removeNode 进行状态更新

/**
 * 获取对象的详细信息（用于调试）
 * @param obj 对象
 */
export function getObjectInfo(obj: any): any {
  if (!obj) return null;

  return {
    name: obj.name || 'Unknown',
    type: obj.constructor?.name || 'Unknown',
    hasChildren: !!(obj.children && obj.children.length > 0),
    childrenCount: obj.children ? obj.children.length : 0,
    hasParent: !!obj.parent,
    hasDestroy: !!(obj.destroy && typeof obj.destroy === 'function'),
    hasRemoveChild: !!(obj.removeChild && typeof obj.removeChild === 'function')
  };
}
